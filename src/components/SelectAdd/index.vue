<template>
  <div class="select-add">
    <el-input v-model="activityIndustryName" :disabled="typeDisabled" placeholder="请选择方案分类"
              @mouseup.stop.native="focusActive" readonly style="width:100%;">
      <i slot="suffix" class="el-icon-arrow-down" v-if="!activeClass" style="margin-right:5px"></i>
      <i slot="suffix" class="el-icon-arrow-up" v-if="activeClass" style="margin-right:5px"></i>
    </el-input>
    <transition name="el-zoom-in-top">
      <div class="active-class" :style="{ width: activeWidth }" v-show="activeClass" ref="addInput">
        <p class="add-class" @click.stop="addClass" v-show="showAdd"><i class="el-icon-plus"></i> 添加分类</p>
        <p v-show="showButton" class="class-fix">
          <el-input v-model="addActive" placeholder="请输入分类名" style="width:200px"></el-input>
          <el-button type="primary" style="margin-left:10px" @click="saveActive">保存</el-button>
          <el-button @click="cancelActive">取消</el-button>
        </p>
        <p :class="item.tag?'active':''" v-for="(item,index) in actIndustryIds" :key="item.typeId"
           @mousedown="chooseActive(item,index)">{{item.typeName}}</p>

      </div>
    </transition>
  </div>
</template>
<script>
export default {
  model: {
    prop: "activityIndustryName",
    event: "update",
  },
  props: {
    actIndustryIds: {
      type: Array,
      default: () => [],
    },
    activityIndustryName: {
      type: String,
      default: () => "",
    },
    typeDisabled: {
      type: Boolean,
      default: false
    },
    activeWidth: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      activeClass: false,
      showAdd: true,
      addActive: '',
      showButton: false,
    }
  },
  mounted() {
    document.addEventListener("mouseup", (e) => {
      let treeDom = this.$refs.addInput
      if (treeDom) {
        if (!treeDom.contains(e.target)) {
          this.activeClass = false;
        }
      }
    });
  },
  methods: {
    focusActive() {
      this.activeClass = !this.activeClass;
      this.$refs.addInput.scrollTop = 0;
    },
    addClass() {
      this.showButton = true;
      this.showAdd = false;
      this.activeClass = true;
    },
    // 添加分类
    saveActive() {
      this.$emit('saveActive', this.addActive);
    },
    // 取消添加分类
    cancelActive() {
      this.showAdd = true;
      this.showButton = false;
      this.addActive = null;
    },
    // 选择分类名
    chooseActive(item, index) {
      this.$emit("update", item.typeName);
      this.$emit('chooseActive', item);
      this.activeClass = false;
    },
    // 重置添加分类名
    resetAddActive() {
      this.addActive = '';
    }
  }
}
</script>
<style scoped lang="scss">
.select-add {
  .active-class {
    width: 100%;
    max-height: 300px;
    overflow-y: scroll;
    padding: 15px 0;
    position: absolute;
    top: 40px;
    left: 0;
    background: #fff;
    box-shadow: 0 0 4px #ccc;
    z-index: 100;

    p {
      margin: 0;
      padding-left: 15px;
      // border-bottom: 1px solid #ccc;
      line-height: 36px;
      cursor: pointer;

      &:hover {
        background: #f5f7fa;
      }

      &.active {
        color: #1265ed;
        background: #f5f7fa;
      }

      &.add-class {
        color: #1265ed;
        font-weight: bold;

        .el-icon-plus {
          font-weight: bold;
        }
      }

      &.class-fix {
        padding-bottom: 10px;

        &:hover {
          background: #fff;
        }
      }
    }
  }
}
</style>
