## custom-export 组件使用

### 组件配置

属性：    
`visible`: 是否展示弹窗 type：`Boolean`，默认值： `false`   
`list`: 已选择的导出字段列表 type：`Array`，默认值： `[]`   
`unselectList`: 未选择的导出字段列表 type：`Array`，默认值： `[]`

事件：
`submits` 保存配置事件

### 配置Demo

import CustomExport from '@/components/CustomExport/index.vue'
<CustomExport v-model:list="list" v-model:unselectList="unselectList" v-model:visible="dialogVisible" @submits="
submitField"></CustomExport>

const list = ref([
{ name: '<PERSON>', id: 1 },
{ name: '<PERSON><PERSON>', id: 2 },
{ name: '<PERSON>', id: 3 },
{ name: '<PERSON>', id: 4 },
{ name: '可编辑', id: 5, disable: true }
])
const unselectList = ref([
{ name: '为选择', id: 12, disable: true },
{ name: '为选择2', id: 22, disable: true }]
)