<template>
  <div class="date-content">
    <div class="date-range">
      <el-radio-group v-model="date" size="small" @change="changeDate">
        <el-radio v-for="(item,index) in dateArr" :key="index" :label="item.value"
                  :disabled="item.value=='999'&&isRepeat">
                  {{ item.label }}
        </el-radio>
      </el-radio-group>
       <el-popover
          v-if="isShowDate"
          placement="bottom"
          trigger="click">
        <div>
            <el-date-picker style="width:175px" ref="start" type="datetime" size="mini" :picker-options="pickerOptionsStart"
                        @change="startChange" v-model="startTime" :clearable="true" format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss" placeholder="请输入开始时间"></el-date-picker>
        -
        <el-date-picker style="width:175px" ref="end" :picker-options="pickerOptionsEnd" v-model="endTime" size="mini"
                        @change="endChange" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                        format="yyyy-MM-dd HH:mm:ss" placeholder="请输入结束时间"></el-date-picker>
        </div>
          <el-button type="text" :disabled="isRepeat" slot="reference" @click="changeDate('999')" 
          :class="(date==='999' && isShowDate) ? 'date-button-active' : 'date-button'">自定义</el-button>
        </el-popover>
                 

      <!-- <div v-show="date==='999'" :class="!isShowDate ? 'date-wrap' : 'date-top'"> -->
      <div v-show="date==='999' && !isShowDate" class="date-wrap">
        <el-date-picker style="width:175px" ref="start" type="datetime" size="mini" :picker-options="pickerOptionsStart"
                        @change="startChange" v-model="startTime" :clearable="true" format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss" placeholder="请输入开始时间"></el-date-picker>
        -
        <el-date-picker style="width:175px" ref="end" :picker-options="pickerOptionsEnd" v-model="endTime" size="mini"
                        @change="endChange" type="datetime" value-format="yyyy-MM-dd HH:mm:ss"
                        format="yyyy-MM-dd HH:mm:ss" placeholder="请输入结束时间"></el-date-picker>
      </div>
    </div>
    
  </div>
</template>

<script>
import {beforeTime} from '@/utils/time.js'

export default {
  data() {
    return {
      startTime: '',
      endTime: '',
      pickerOptionsStart: {
        disabledDate: (time) => {
          let endDateVal = this.endTime;
          if (endDateVal) {
            const endDate = new Date(endDateVal);
            return time.getTime() > endDate.getTime() && time.toDateString() !== endDate.toDateString();
          }
        },
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          let beginDateVal = this.startTime;
          if (beginDateVal) {
            const beginDate = new Date(beginDateVal);
            return time.getTime() < beginDate.getTime() && time.toDateString() !== beginDate.toDateString();
          }
        },
      },
      deptSearchLevelOption: [
        {
          value: '0',
          label: '不限制'
        },
        {
          value: '1',
          label: '一年'
        },
        {
          value: '2',
          label: '六个月'
        },
        {
          value: '3',
          label: '三个月'
        },
        {
          value: '4',
          label: '一个月'
        }
      ],
      date: 0,// props.ivalue

    }

  },
  props: {
    isRepeat: {
      type: Boolean,
      default: false
    },
    // 初始值
    ivalue: {
      type: Number,
      default: 0
    },
    list: {
      type: Array,
      default: () => []
    },
    setKey: {
      type: String,
      default: ''
    },
    isShowDate: { // 自定义弹窗显示
      type: Boolean,
      default: false
    },
  },
  computed: {
    deptInfo() {
      return this.$store.state.user.deptInfo
    },
    dateArr() {
      let propList = this.list
      if(!this.isShowDate){
        if (!propList.some(item => item.label === '自定义')) {
          propList.push({label: '自定义', value: '999'})
        }
      }
      
      // if (this.deptInfo.deptSearchLevel != 0 && this.deptInfo.deptSearchLevel != 1) { //查询范围小于1年，去除1年选项
      //     propList = propList.filter((item) => { return item.label !== '1年' })
      // }
      return propList
    }
  },
  mounted() {
    this.getAllDict()
  },
  methods: {
    getAllDict() {
      // getDicts('dept_search_level').then(res => {
      //     this.deptSearchLevelOption = res.data.data
      // })
    },
    dataStart() {
      const selectedTimePeriod = this.deptSearchLevelOption.find(item => parseInt(item.value) === this.deptInfo.deptSearchLevel)
      // debugger
      if (selectedTimePeriod) {
        let timeDifference = 0
        if (selectedTimePeriod.label === '一年') {
          timeDifference = 365 * 24 * 60 * 60 * 1000
        } else if (selectedTimePeriod.label === '六个月') {
          timeDifference = 6 * 30 * 24 * 60 * 60 * 1000
        } else if (selectedTimePeriod.label === '三个月') {
          timeDifference = 3 * 30 * 24 * 60 * 60 * 1000
        } else if (selectedTimePeriod.label === '一个月') {
          timeDifference = 30 * 24 * 60 * 60 * 1000
        } else {
          return
        }
        return new Date().getTime() - timeDifference
      }
    },
    resetParam() {
      // this.startTime = this.ivalue.split(',')[0]
      // this.endTime = this.ivalue.split(',')[1]
      this.date = this.ivalue
    },
    setParam(dateType, start, end) {
    console.log('dateType :>> ', dateType);
      this.date = dateType
      this.startTime = start
      this.endTime = end
    },
    changeDate(val) {
    
      if(this.isShowDate && val == '999'){
        this.date = '999'
        this.$emit('date-change', {key: this.setKey, type: 'dymatic'}) // 自定义不传参数
      }else{
        this.startTime = ''
        this.endTime = ''
        this.$emit('date-change', {key: this.setKey, type: 'fixed', date: val}) // 传固定的字符串
      }
     
        
      if (!this.isShowDate && val == '999') {
        this.startTime = ''
        this.endTime = ''
        this.$emit('date-change', {key: this.setKey, type: 'dymatic'}) // 自定义不传参数
      } else {
        this.$emit('date-change', {key: this.setKey, type: 'fixed', date: val}) // 传固定的字符串
      }
    },
    startChange(val) {
      let endDateVal = this.endTime;
      if (endDateVal && new Date(this.startTime).getTime() > new Date(endDateVal).getTime()) {
        this.endTime = val;
        this.$emit('date-change', {key: this.setKey, type: 'end', date: this.endTime}) // 传结束时间
      }
      console.log(this.startTime)
      this.$emit('date-change', {key: this.setKey, type: 'start', date: this.startTime}) // 传开始时间
    },
    endChange(val) {
      let beginDateVal = this.startTime;
      if (beginDateVal && new Date(this.endTime).getTime() < new Date(beginDateVal).getTime()) {
        this.startTime = val;
        this.$emit('date-change', {key: this.setKey, type: 'start', date: this.startTime}) // 传开始时间
      }
      console.log(this.endTime)
      this.$emit('date-change', {key: this.setKey, type: 'end', date: this.endTime}) // 传结束时间
    },
  },

}
</script>

<style lang="scss" scoped>
.date-range {
  .el-radio {
    margin-right: 10px;
  }

  .el-radio__input {
    display: none;
  }
}

.date-range {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.date-wrap {
  margin-top: 0;
}
.date-button.is-disabled {
    color: #C0C4CC;
}
.date-button-active{
  color: #fff;
  background: #247CFF;
  border-radius: 2px;
  padding: 7px 10px;
  margin-left: 10px;
  border: none;
}
.date-button{
  color: #333333;
  margin-left: 10px;
  border-radius: 2px;
  padding: 7px 10px;
  border: none;

}
.date-content{
  position: relative;
}
.date-top{
  position: absolute;
  top: -40px;
  right: -150px;
}
</style>
