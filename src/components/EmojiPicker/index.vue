<template>
  <div class="emoji-picker">
    <div class="emoji-tabs">
      <div
        v-for="(category, index) in categories"
        :key="index"
        :class="['tab-item', { active: activeCategory === index }]"
        @click.stop="activeCategory = index"
      >
        {{ category.icon }}
      </div>
      <div
        :class="['tab-item', { active: activeCategory === -1 }]"
        @click.stop="activeCategory = -1"
        v-if="recentEmojis.length > 0"
      >
        ⭐
      </div>
    </div>
    <div class="emoji-container">
      <template v-if="activeCategory === -1">
        <div class="category-title">最近使用</div>
        <div class="emoji-group">
          <div
            v-for="(emoji, index) in recentEmojis"
            :key="index"
            class="emoji-item"
            @click="(event) => selectEmoji(emoji, event)"
          >
            {{ emoji }}
          </div>
        </div>
      </template>
      <template v-else>
        <div class="category-title">{{ categories[activeCategory].name }}</div>
        <div class="emoji-group">
          <div
            v-for="(emoji, index) in categories[activeCategory].emojis"
            :key="index"
            class="emoji-item"
            @click="(event) => selectEmoji(emoji, event)"
          >
            {{ emoji }}
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmojiPicker',
  data() {
    return {
      activeCategory: 0,
      recentEmojis: [],
      categories: [
        {
          name: '表情',
          icon: '😊',
          emojis: [
            '😀', '😁', '😂', '🤣', '😃', '😄', '😅', '😆', '😉', '😊',
            '😋', '😎', '😍', '😘', '🥰', '😗', '😙', '😚', '🙂', '🤗',
            '🤩', '🤔', '🤨', '😐', '😑', '😶', '🙄', '😏', '😣', '😥',
            '😮', '🤐', '😯', '😪', '😫', '🥱', '😴', '😌', '😛', '😜'
          ]
        },
        {
          name: '情绪',
          icon: '😡',
          emojis: [
            '😝', '🤤', '😒', '😓', '😔', '😕', '🙃', '🤑', '😲', '☹️',
            '🙁', '😖', '😞', '😟', '😤', '😢', '😭', '😦', '😧', '😨',
            '😩', '🤯', '😬', '😰', '😱', '🥵', '🥶', '😳', '🤪', '😵',
            '🥴', '😠', '😡', '🤬', '😷', '🤒', '🤕', '🤢', '🤮', '🤧'
          ]
        },
        {
          name: '手势',
          icon: '👍',
          emojis: [
            '👍', '👎', '👏', '🙌', '👋', '🤝', '✌️', '🤞', '🤟', '🤘',
            '👌', '👈', '👉', '👆', '👇', '☝️', '✋', '🤚', '🖐️', '🖖',
            '👐', '🙏', '💪', '🦾', '🦿', '🦵', '🦶', '👂', '🦻', '👃'
          ]
        },
        {
          name: '符号',
          icon: '❤️',
          emojis: [
            '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
            '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️',
            '✝️', '☪️', '🕉️', '☸️', '✡️', '🔯', '🕎', '☯️', '☦️', '🛐'
          ]
        },
        {
          name: '动物',
          icon: '🐱',
          emojis: [
            '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
            '🦁', '🐮', '🐷', '🐸', '🐵', '🙈', '🙉', '🙊', '🐒', '🐔',
            '🐧', '🐦', '🐤', '🐣', '🐥', '🦆', '🦅', '🦉', '🦇', '🐺'
          ]
        },
        {
          name: '特殊表情',
          icon: '🤡',
          emojis: [
            '🤡', '🤠', '😈', '👻', '👽', '👾', '👿', '💀', '👹', '👺',
            '👶', '👦', '👧', '👨', '👩'
          ]
        }
      ]
    }
  },
  methods: {
    selectEmoji(emoji, event) {
      // 防止事件冲突
      if (event) {
        event.stopPropagation();
      }

      // 添加到最近使用
      this.addToRecent(emoji);

      // 发送选择表情事件，并指示不关闭选择器（false）
      this.$emit('select', emoji, event, false);
    },

    addToRecent(emoji) {
      // 如果已经存在，先移除
      const index = this.recentEmojis.indexOf(emoji);
      if (index > -1) {
        this.recentEmojis.splice(index, 1);
      }

      // 添加到最前面
      this.recentEmojis.unshift(emoji);

      // 只保留最近10个
      if (this.recentEmojis.length > 10) {
        this.recentEmojis = this.recentEmojis.slice(0, 10);
      }

      // 存入localStorage
      try {
        localStorage.setItem('recentEmojis', JSON.stringify(this.recentEmojis));
      } catch (e) {
        console.error('Failed to save recent emojis to localStorage', e);
      }
    }
  },

  created() {
    // 从 localStorage 加载最近使用的表情
    try {
      const saved = localStorage.getItem('recentEmojis');
      if (saved) {
        this.recentEmojis = JSON.parse(saved);
      }
    } catch (e) {
      console.error('Failed to load recent emojis from localStorage', e);
    }
  },

  // 添加点击事件处理
  mounted() {
    // 防止点击表情选择器内部关闭选择器
    this.$el.addEventListener('click', (event) => {
      event.stopPropagation();
    });
  }
}
</script>

<style scoped lang="scss">
.emoji-picker {
  width: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 10px;

  .emoji-tabs {
    display: flex;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 8px;
    margin-bottom: 8px;
    justify-content: space-around;

    .tab-item {
      font-size: 20px;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.3s;
      position: relative;

      &:hover {
        background-color: #f2f2f2;
        transform: scale(1.1);
      }

      &.active {
        background-color: #ecf5ff;
        color: #409EFF;

        &:after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 24px;
          height: 2px;
          background-color: #409EFF;
          border-radius: 1px;
        }
      }
    }
  }

  .emoji-container {
    max-height: 220px;
    overflow-y: auto;
    padding: 0 5px;

    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 3px;
      background-color: #c0c4cc;
    }

    &::-webkit-scrollbar-track {
      border-radius: 3px;
      background-color: #f5f7fa;
    }

    .category-title {
      font-size: 14px;
      color: #606266;
      margin-bottom: 8px;
      font-weight: 500;
      padding-left: 4px;
      border-left: 3px solid #409EFF;
    }

    .emoji-group {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 10px;

      .emoji-item {
        font-size: 24px;
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s;
        border-radius: 4px;

        &:hover {
          background-color: #f2f2f2;
          transform: scale(1.1);
        }
      }
    }
  }
}
</style>
