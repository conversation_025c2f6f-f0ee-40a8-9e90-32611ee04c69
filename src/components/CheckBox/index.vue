<template>
  <div class="checkbox-wrap">
    <el-checkbox v-model="checkAll" @change="handleCheckAllChange" class="check-all">
      全部
      <div v-show="showNum" class="itemNum">
        <div>{{ totalNumber||0 }}</div>
      </div>
    </el-checkbox>
    <el-checkbox-group v-model="checkedList" @change="handleCheckedChange">
      <el-checkbox v-for="item in list" :label="item.dictValue" :key="item.dictValue"
                   :style="{ 'margin-bottom': showNum ? '25px' : '10px' }">
        {{item.dictLabel}}
        <div v-show="showNum" class="itemNum">
          <div>{{ item.number||0 }}</div>
        </div>
      </el-checkbox>
    </el-checkbox-group>
  </div>
</template>
<script>
export default {
  model: {
    prop: "activeList",
    event: "update",
  },
  data() {
    return {
      checkAll: true,
      checkedList: []
    }
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    activeList: {
      type: Array,
      default: () => [],
    },
    showNum: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    list(newVal, oldVal) {
      if (newVal) {
        // this.handleCheckAllChange(true)
        this.checkAll = this.activeList.length === newVal.length;
      }
    },
    activeList: {
      immediate: true,
      handler(newVal) {
        this.checkedList = newVal;
        this.checkAll = newVal.length === this.list.length;
      }
    }
  },
  computed: {
    //求得全部选项的和
    totalNumber() {
      return this.list.reduce((acc, item) => acc + (item?.number || 0), 0);
    },
  },
  methods: {
    handleCheckAllChange(val) {
      let choseList = this.list.map((item) => item.dictValue)
      this.checkedList = val ? choseList : [];
      this.$emit("update", this.checkedList);
      this.isIndeterminate = false;
    },
    handleCheckedChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.list.length;
      this.$emit("update", this.checkedList);
    }

  }
}

// margin-bottom: 10px;30px
</script>
<style lang="scss" scoped>
.checkbox-wrap {
  display: flex;
  align-items: baseline;

  .check-all {
    margin-right: 25px;
  }

  ::v-deep .el-checkbox {
    margin-right: 25px;
    font-size: 14px;
    color: #333;

    // .el-checkbox__label {
    //     margin-bottom: 10px;
    // }
  }

  .itemNum {
    position: absolute;
    top: 19px;
    width: 62%;

    div {
      width: fit-content;
      padding: 0px 7px;
      background: #E8F1FF;
      border-radius: 9px;
      font-size: 12px;
      color: #2D71FF;
      margin: 0 auto;
    }
  }
}
</style>
