<template>
  <div>
    <el-upload
      class="myupload"
      ref="upload"
      action="#"
      :limit="fileLimit"
      :accept="acceptType"
      :http-request="handleFileUpload"
      :headers="upload.headers"
      :on-progress="handleFileUploadProgress"
      :auto-upload="true"
      :on-success="handleFileSuccess"
      :show-file-list="true"
      :file-list="fileList"
      list-type="text"
      :on-exceed="handleExceed"
      :on-remove="removeList"
      :before-upload="handleBeforeUpload"
      drag
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">
        <p>点击此处选择文件上传</p>
        <p>或将文件直接拖拽至此处</p>
        <div class="el-upload__tip" slot="tip">
          支持扩展名：{{ acceptType }}格式文件！最多可传{{ fileLimit }}个文件,单个文件不超过{{ fileSize }}MB.
        </div>
      </div>
      <div class="file-list" slot="tip">
        <div v-for="(item, index) in fileList" :key="index" class="file-main">
          <div class="file-mask">
            <i class="el-icon-zoom-in" @click="openImg(item)"></i>
          </div>
          <i
            v-if="isImgType(item)"
            class="el-icon-error file-img"
            @click="delFile(item, index)"
          ></i>
          <img :src="transBlob(item)" alt="" v-if="isImgType(item)"/>
        </div>
      </div>
      <!-- <div class="el-upload__tip" slot="tip">

          <div @click="delFile">sdfsdf</div>
          <el-link type="info" style="font-size:12px" @click="importTemplate">下载模板</el-link>
        </div> -->
      <!-- <div class="el-upload__tip" style="color:red" slot="tip">提示：仅允许导入“xls”或“xlsx”格式文件！</div> -->
    </el-upload>
    <el-dialog title="" :visible.sync="dialogVisible" width="680px" append-to-body>
      <img :src="bigImg" alt="" class="big-img"/>
    </el-dialog>
  </div>
</template>
<script>
import {getUploadMinio} from '@/api/fileList/fileIndex'
import {encryptFile} from '@/utils/jsencrypt'
import {atobToblob, renderFileIcon} from '@/utils/index'

export default {
  model: {
    prop: "fileArray",
    event: "update",
  },
  props: {
    // 数量限制
    fileLimit: {
      type: Number,
      default: 5,
    },
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 50,
    },
    // 文件类型
    acceptType: {
      type: String,
      default: () => ".xlsx,.xls,.docx,.doc,.png,.jpeg,.pdf,.jpg",
    },
    // 文件数组-用于传值到后端
    fileArray: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      renderFileIcon,
      fileToBlob: '',
      bigImg: "",
      dialogVisible: false,
      limitNumber: 7,
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        // headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/system/user/importData",
      },
      fileList: [], // 文件格式、文件数量、文件大小
    };
  },
  methods: {
    openImg(item) {
      this.dialogVisible = true;
      if (item.raw) {
        this.bigImg = URL.createObjectURL(item.raw);
      } else {
        this.bigImg = item.url
      }
    },
    // 删除文件
    delFile(item, index) {
      this.fileList.splice(index, 1);
      this.$emit("update", this.fileList);
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      let fileList = this.acceptType.split(",");
      // 校检文件类型
      if (this.acceptType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = "." + file.name.slice(file.name.lastIndexOf(".") + 1); // auto-upload=false
          // fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);  // auto-upload=true
        }
        const isTypeOk = fileList.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$message.error(
            `文件格式不正确, 请上传${this.acceptType}格式文件!`
          );
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      // 校验名称是否重复
      let arrs = false
      this.fileList.map((item) => {
        if (item.name == file.name) {
          arrs = true
        }
      })
      if (arrs) {
        this.$message.error('同一文件已经上传')
        return false
      }
      return true;
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 上传文件
    handleFileUpload(file) {
      this.$emit('pageLoad', true)
      this.upload.isUploading = true
      let reader = new FileReader()
      reader.readAsArrayBuffer(file.file)
      reader.onload = () => {
        var fileContent = new Uint8Array(reader.result)
        var encryptedContent = encryptFile(fileContent);
        let blobType = file.file.type
        let fileName = file.file.name
        let blob = atobToblob(encryptedContent, blobType)
        let formData = new FormData();
        formData.append("file", blob, fileName);
        getUploadMinio(formData).then((res) => {
          this.upload.isUploading = false
          this.$message.success("上传成功");
          this.fileList.push({
            id: res.data, // 接口返回的文件id-业务代码需要的值
            file: blob, // 密文文件-接口传递时传值内容
            name: file.file.name,
            raw: file.file // 明文文件(本地回显)-传递时需要去掉
          })
          this.$emit("update", this.fileList)
          this.$emit('pageLoad', false)
          this.renderFileIcon('el-upload-list__item-name', this.fileList);
        }).catch((error) => {
          this.$emit('pageLoad', false)
          this.upload.isUploading = false
          this.$message.error("上传失败, 请重试");
          console.log(error, 'error')
          let delFile = this.fileList.filter((obj) => obj.name != file.file.name)
          this.fileList = delFile
          this.$emit("update", this.fileList);
          this.renderFileIcon('el-upload-list__item-name', this.fileList);
        })
      }


    },
    handleReset() {
      this.fileList = []
      this.$refs.upload.clearFiles();
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(response.msg, "导入结果", {dangerouslyUseHTMLString: true});
      // this.getList();
    },
    // 判断文件是不是照片类型
    isImgType(item) {
      let fileName = item.name;
      let fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
      if (
        ["png", "jpg", "jpeg", ".gif", "PNG", "JPG", "JPEG", "GIF"].indexOf(
          fileType
        ) != -1
      ) {
        return true;
      } else {
        return false;
      }
    },
    // 将照片转成blob格式
    transBlob(item) {
      if (item.raw) {
        return URL.createObjectURL(item.raw);
      } else {
        return item.url
      }
    },
    // 移除文件事件
    removeList(file, fileList) {
      this.fileList = fileList;
      this.$emit("update", this.fileList);
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`只允许上传${this.fileLimit}个文件`);
    },
    // 附件回显 父->子
    setFileList(fileList) {
      this.fileList = fileList;
      this.renderFileIcon('el-upload-list__item-name', this.fileList);
    },
  },
};
</script>
<style scoped>
.big-img {
  /* width: 680px; */
  width: 100%;
  height: 510px;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
}

.file-list div {
  position: relative;
}

.file-main:hover .file-img {
  display: inline-block;
  font-size: 20px;
}

.file-main:hover .file-mask {
  display: block;
  height: 120px;
}

.file-main .file-mask {
  display: none;
  position: absolute;
  /* height: 120px; */
  top: 0;
  left: 0;
  width: calc(100% - 20px);
  height: calc(100% - 20px);
  right: 20px;
  bottom: 0;
  text-align: center;
  color: #fff;
  font-size: 20px;
  background-color: rgba(0, 0, 0, 0.5);
}

.file-main .file-mask i {
  cursor: pointer;
  margin-top: 50px;
}

.file-img {
  display: none;
}

.file-list img {
  margin-right: 20px;
  display: inline-block;
  width: 160px;
  height: 120px;
  margin-bottom: 10px;
}

.file-list .el-icon-error {
  color: #ec0d1d;
  position: absolute;
  right: 10px;
  top: -9px;
  cursor: pointer;
}

.myupload .el-upload__text p {
  font-size: 12px;
  line-height: 20px;
  /* color: #ffffff; */
  margin: 0;
}

.myupload >>> .el-upload-dragger {
  /* background: #f4f8ff; */
  border: 1px dashed #666666;
}

.myupload .el-upload__text .el-upload__tip {
  margin: 0;
  line-height: 17px;
  color: #999;
}

.myupload >>> .imgicon {
  display: none;
}

.myupload >>> .imgicon + .el-upload-list__item-status-label {
  display: none;
}

.myupload >>> .imgicon-img {
  display: inline-block;
  /* display: inline-block; */
  width: 20px;
  margin-bottom: -3px;
  height: 20px;
  background-size: 100% 100%;
  margin-right: 10px;
  background-image: url("./img.png");
}

.myupload >>> .imgicon-pdf {
  display: inline-block;
  width: 20px;
  margin-bottom: -3px;
  height: 20px;
  background-size: 100% 100%;
  margin-right: 10px;
  background-image: url("./pdf.png") !important;
}

.myupload >>> .imgicon-docx {
  display: inline-block;
  width: 20px;
  margin-bottom: -3px;
  height: 20px;
  background-size: 100% 100%;
  margin-right: 10px;
  background-image: url("./docx.png") !important;
}

.myupload >>> .imgicon-zip {
  display: inline-block;
  width: 20px;
  margin-bottom: -3px;
  height: 20px;
  background-size: 100% 100%;
  margin-right: 10px;
  background-image: url("./zip.png") !important;
}

.myupload >>> .imgicon-pptx {
  display: inline-block;
  width: 20px;
  margin-bottom: -3px;
  height: 20px;
  background-size: 100% 100%;
  margin-right: 10px;
  background-image: url("./ppt.png") !important;
}

.myupload >>> .imgicon-xlsx {
  display: inline-block;
  width: 20px;
  margin-bottom: -3px;
  height: 20px;
  background-size: 100% 100%;
  margin-right: 10px;
  background-image: url("./xls.png") !important;
}

.myupload >>> .imgicon-default {
  display: inline-block;
  width: 20px;
  margin-bottom: -3px;
  height: 20px;
  background-size: 100% 100%;
  margin-right: 10px;
  background-image: url("./text.png") !important;
}
</style>
