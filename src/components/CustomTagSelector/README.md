# CustomTagSelector 自定义标签选择器组件

## 📋 功能说明

自定义标签选择器组件，用于在舆情数据列表中为每条数据添加自定义标签。使用el-select实现单选标签功能，支持搜索过滤、实时更新等功能。

## 🎯 特性

- ✅ **单选标签**: 支持为单条数据选择一个自定义标签
- ✅ **多选兼容**: 兼容数组格式的数据，方便将来扩展为多选
- ✅ **搜索过滤**: 支持按标签名称搜索过滤
- ✅ **实时更新**: 标签变更后立即调用API更新
- ✅ **友好界面**: 使用el-select提供直观的下拉选择界面
- ✅ **错误处理**: 完善的错误提示和异常处理
- ✅ **可复用**: 组件化设计，方便在其他页面使用
- ✅ **性能优化**: 使用全局状态管理，避免重复API请求

## 📦 使用方法

### 1. 基本用法（单个值格式）

```vue
<template>
  <CustomTagSelector
    :row-data="rowData"
    v-model="selectedTagId"
    @change="handleTagChange"
  />
</template>

<script>
import CustomTagSelector from '@/components/CustomTagSelector/index.vue'

export default {
  components: {
    CustomTagSelector
  },
  data() {
    return {
      rowData: {
        id: '123456',
        md5: 'abc123',
        title: '示例标题'
      },
      selectedTagId: null // 已选标签ID
    }
  },
  methods: {
    handleTagChange(tagId) {
      console.log('标签已更新:', tagId)
    }
  }
}
</script>
```

### 1.1. 兼容简单数组格式用法

```vue
<template>
  <CustomTagSelector
    :row-data="rowData"
    v-model="selectedTagIds"
    @change="handleTagChange"
  />
</template>

<script>
export default {
  data() {
    return {
      rowData: {
        id: '123456',
        md5: 'abc123',
        title: '示例标题'
      },
      selectedTagIds: [] // 简单ID数组格式，组件会取第一个元素显示
    }
  },
  methods: {
    handleTagChange(tagIds) {
      // tagIds 会是数组格式，如 [123] 或 []
      console.log('标签已更新:', tagIds)
    }
  }
}
</script>
```

### 1.2. 兼容对象数组格式用法（新格式）

```vue
<template>
  <CustomTagSelector
    :row-data="rowData"
    v-model="contentMeta"
    @change="handleTagChange"
  />
</template>

<script>
export default {
  data() {
    return {
      rowData: {
        id: '123456',
        md5: 'abc123',
        title: '示例标题'
      },
      contentMeta: [
        {
          "id": "1927993177215053824",
          "tagContent": "会也"
        }
      ] // 对象数组格式，组件会取第一个对象的id显示
    }
  },
  methods: {
    handleTagChange(contentMeta) {
      // contentMeta 会是对象数组格式，如：
      // [{ "id": "123", "tagContent": "新标签" }] 或 []
      console.log('标签已更新:', contentMeta)
    }
  }
}
</script>
```

### 2. 在表格中使用

```vue
<el-table :data="tableData">
  <el-table-column label="自定义标签">
    <template slot-scope="scope">
      <CustomTagSelector
        :row-data="scope.row"
        v-model="scope.row.customTags"
        @change="handleCustomTagChange(scope.row, $event)"
      />
    </template>
  </el-table-column>
</el-table>
```

## 🔧 Props

| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| rowData | Object | 是 | - | 当前行数据，必须包含id和md5字段 |
| value | Number/String/Array | 否 | null | 当前已选标签ID或标签ID数组（兼容多选格式） |

## 📤 Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| input | tagId: Number/String/Array | v-model双向绑定事件，格式与传入的value保持一致 |
| change | tagId: Number/String/Array | 标签变更事件，格式与传入的value保持一致 |

## 🔌 API接口

组件依赖以下API接口：

### 1. 标签列表接口
- **接口**: `listTag(data)`
- **文件**: `@/api/system/tag`
- **说明**: 获取所有可用标签列表

### 2. 标签更新接口
- **接口**: `updateRiskApi(param)`
- **文件**: `@/api/search/index`
- **参数**:
  ```javascript
  {
    md5: row.md5,        // 数据MD5
    indexId: row.id,     // 数据ID
    changeType: 2,       // 变更类型：2-自定义标签
    changeValue: tagId   // 标签ID
  }
  ```

## 🎨 样式说明

组件使用scoped样式，主要样式类：

- `.custom-tag-selector`: 组件容器，设置为inline-block并添加边距

## 🔄 数据格式兼容性

### 输入格式支持

组件支持以下四种输入格式：

1. **null/undefined**: 表示未选择任何标签
2. **单个值**: `123` 或 `"123"` - 直接的标签ID
3. **简单数组格式**: `[123]` 或 `["123"]` - 简单ID数组，组件会取第一个元素
4. **对象数组格式**: `[{"id": "123", "tagContent": "标签名"}]` - 完整标签信息数组

### 输出格式保持一致

组件会根据输入的格式返回相同格式的数据：

- 输入单个值 → 输出单个值
- 输入简单数组 → 输出简单数组（单选模式下数组只包含一个元素）
- 输入对象数组 → 输出对象数组（单选模式下数组只包含一个对象）

### 示例

```javascript
// 单个值格式
// 输入: null → 选择标签123 → 输出: 123
// 输入: 456 → 选择标签789 → 输出: 789

// 简单数组格式
// 输入: [123] → 选择标签456 → 输出: [456]
// 输入: [] → 选择标签123 → 输出: [123]

// 对象数组格式（新格式）
// 输入: [{"id": "123", "tagContent": "旧标签"}]
// → 选择标签456 →
// 输出: [{"id": "456", "tagContent": "新标签"}]

// 输入: [] → 选择标签123 →
// 输出: [{"id": "123", "tagContent": "标签名"}]
```

## 📝 注意事项

1. **数据格式**: rowData必须包含id和md5字段
2. **权限控制**: 确保用户有标签管理权限
3. **网络异常**: 组件已处理网络请求异常情况
4. **性能优化**: 标签列表会在组件创建时加载
5. **单选模式**: 当前为单选模式，但兼容多选数据格式
6. **格式一致性**: 输出格式与输入格式保持一致

## 🔄 更新日志

- **v2.0.0**: 重构为单选模式，使用el-select替代多选实现
- **v1.0.0**: 初始版本，支持多选标签功能

## 🚀 技术实现

### 核心功能

1. **标签加载**: 组件创建时自动调用`listTag`接口获取标签列表
2. **标签选择**: 使用el-select提供下拉选择功能，支持搜索过滤
3. **实时更新**: 选择标签后立即调用`updateRiskApi`接口更新数据
4. **错误处理**: 更新失败时自动恢复到原始值并显示错误信息

### 数据流

```
用户选择标签 → handleTagChange → 调用updateRiskApi →
成功: 更新本地状态 + 触发事件 + 显示成功消息
失败: 恢复原值 + 显示错误消息
```

## ⚡ 性能优化

### 全局状态管理

组件使用Vuex全局状态管理标签选项，避免重复API请求：

- **缓存机制**: 5分钟内不重复请求标签列表
- **共享数据**: 所有组件实例共享同一份标签数据
- **自动加载**: 页面创建时自动预加载标签选项

### 性能对比

| 场景 | 优化前 | 优化后 |
|------|--------|--------|
| 10行表格 | 10次API请求 | 1次API请求 |
| 100行表格 | 100次API请求 | 1次API请求 |
| 跨页面使用 | 每页都请求 | 全局缓存 |

### 手动刷新缓存

```javascript
// 强制刷新标签选项
this.$store.dispatch('tagOptions/refreshTagOptions')
```

## 🔧 开发调试

如需调试组件，可以在浏览器控制台查看以下信息：

```javascript
// 检查标签选项状态
console.log('标签选项:', this.$store.getters['tagOptions/tagOptions'])
console.log('加载状态:', this.$store.getters['tagOptions/tagOptionsLoading'])

// 手动触发加载
this.$store.dispatch('tagOptions/loadTagOptions')
```

## 📞 技术支持

如有问题，请检查：
1. 网络连接是否正常
2. API接口是否可访问
3. 用户权限是否足够
4. 数据格式是否正确
5. Vuex store模块是否正确注册
