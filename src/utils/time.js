import moment from 'moment'

export const beforeTime = {
  'momentDay': `${moment(new Date()).format('YYYY-MM-DD 00:00:00')},${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}`,
  'oneDay': `${moment(new Date()).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss')},${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}`,
  'twoDay': `${moment(new Date()).subtract(2, 'day').format('YYYY-MM-DD HH:mm:ss')}, ${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}`,
  'threeDay': `${moment(new Date()).subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss')},${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}`,
  'sevenDay': `${moment(new Date()).subtract(7, 'day').format('YYYY-MM-DD HH:mm:ss')},${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}`,
  'yearDay': `${moment(new Date()).subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss')},${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}`,
  'tenDay': [moment(new Date()).subtract(10, 'day').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
  'oneMonth': `${moment(new Date()).subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss')},${moment(new Date()).format('YYYY-MM-DD HH:mm:ss')}`,
  'lastTwoMonth': [moment(new Date()).subtract(2, 'months').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
  'halfMonth': [moment(new Date()).subtract(6, 'months').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')],
  'oneYear': [moment(new Date()).subtract(1, 'year').format('YYYY-MM-DD'), moment(new Date()).format('YYYY-MM-DD')]
}
