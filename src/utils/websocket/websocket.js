import { getToken } from "@/utils/auth";
import EventBus, { WS_EVENTS } from "@/utils/eventBus";

class WS {
  #tasks = []; // 存储待发送的任务队列
  #connectReady = false; // WebSocket 连接是否就绪
  #connection = null; // WebSocket 实例
  #heartTimer = null; // 心跳定时器
  #heartbeatTimeoutTimer = null; // 心跳响应超时计时器
  #reconnectCount = 0; // 重连次数
  #reconnectCountMax = 10; // 重连次数上限
  #lockReconnect = false; // 重连锁
  #reconnectTimer = null; // 重连定时器
  #isManualClose = false; // 新增手动关闭标记

  constructor() {
    this.initConnect(); // 初始化 WebSocket 连接
    // 后台重试次数达到上限之后，tab 获取焦点再重试
    document.addEventListener("visibilitychange", () => {
      if (!document.hidden && !this.#connectReady) {
        this.initConnect();
      }
    });
  }

  // 关闭连接方法
  closeConnection() {
    if (this.#connection) {
      this.#isManualClose = true; // 标记为手动关闭
      this.#connection.close(1000, "Manual closure"); // 使用正常关闭代码
      this.#clearHeartBeat(); // 这会同时清除心跳超时计时器
      this.#connectReady = false;
      console.log("WebSocket连接终止");
    }
  }

  // 初始化 WebSocket 连接
  initConnect = () => {
    const token = getToken();
    // // 如果 token 是 null，而且 localStorage 的用户信息有值，需要清空用户信息
    // if (token === null && localStorage.getItem("USER_INFO")) {
    //   localStorage.removeItem("USER_INFO");
    // }
    if (token) {
      this.#initConnection(token);
    } else {
      console.log("token为空,无法发起WebSocket连接");
    }
  };

  // 初始化 WebSocket
  #initConnection = (token) => {
    console.log("发起WebSocket连接");
    if (this.#connection) {
      console.log("已存在WebSocket连接,关闭旧连接");
      this.#connection.removeEventListener("message", this.#onMessage);
      this.#connection.removeEventListener("open", this.#onOpen);
      this.#connection.removeEventListener("close", this.#onClose);
      this.#connection.removeEventListener("error", this.#onError);
      this.#connection.close();
    }

    this.#connection = new WebSocket(process.env.VUE_APP_WS_URL, [`${token}`]);

    this.#connection.addEventListener("message", this.#onMessage);
    this.#connection.addEventListener("open", this.#onOpen);
    this.#connection.addEventListener("close", this.#onClose);
    this.#connection.addEventListener("error", this.#onError);
  };

  // WebSocket 消息事件
  #onMessage = (e) => {
    // 检查是否是心跳响应
    if (e.data === 'PONG') {
      this.#resetHeartbeatTimeout(); // 重置心跳超时计时器
      return;
    }

    const params = JSON.parse(e.data);
    this.onMessage(params); // 收到消息回调
  };

  // WebSocket 打开事件
  #onOpen = () => {
    this.#connectReady = true;
    this.#dealTasks(); // 处理任务队列
    this.#startHeartBeat(); // 开始心跳
    EventBus.$emit(WS_EVENTS.CONNECT); // 触发连接事件
  };

  // WebSocket 关闭事件
  #onClose = () => {
    console.log("WebSocket连接中断");
    this.#connectReady = false;
    this.#clearHeartBeat();

    // 只有非手动关闭时才触发重连
    if (!this.#isManualClose) {
      EventBus.$emit(WS_EVENTS.DISCONNECT);
      this.#reconnect();
    } else {
      // 重置手动关闭标记
      this.#isManualClose = false;
    }

    // 清理连接引用
    this.#connection = null;
  };

  // WebSocket 错误事件
  #onError = () => {
    this.#connectReady = false;
    this.#clearHeartBeat(); // 清除心跳
    this.#reconnect(); // 重连
  };

  // 开始心跳
  #startHeartBeat = () => {
    this.#heartTimer = setInterval(() => {
      if (this.#connection?.readyState === WebSocket.OPEN) {
        this.#connection.send('PING'); // 发送心跳
        this.#startHeartbeatTimeout(); // 启动心跳响应超时检测
      }
    }, 9900);
  };

  // 启动心跳响应超时检测
  #startHeartbeatTimeout = () => {
    // 清除之前的超时计时器
    this.#clearHeartbeatTimeout();

    // 设置新的超时计时器，如果在指定时间内没有收到PONG响应，则重新连接
    this.#heartbeatTimeoutTimer = setTimeout(() => {
      console.log('心跳响应超时，重新连接');
      // 关闭当前连接，触发重连
      if (this.#connection) {
        this.#connection.close();
      }
    }, 5000); // 5秒超时时间
  };

  // 重置心跳超时计时器
  #resetHeartbeatTimeout = () => {
    this.#clearHeartbeatTimeout();
  };

  // 清除心跳超时计时器
  #clearHeartbeatTimeout = () => {
    if (this.#heartbeatTimeoutTimer) {
      clearTimeout(this.#heartbeatTimeoutTimer);
      this.#heartbeatTimeoutTimer = null;
    }
  };

  // 清除心跳
  #clearHeartBeat = () => {
    if (this.#heartTimer) {
      clearInterval(this.#heartTimer);
      this.#heartTimer = null;
    }
    this.#clearHeartbeatTimeout(); // 同时清除心跳超时计时器
  };

  // 重连
  #reconnect = () => {
    if (this.#isManualClose) return; // 手动关闭时不重连
    if (this.#lockReconnect) return;

    this.#lockReconnect = true;

    if (this.#reconnectTimer) {
      clearTimeout(this.#reconnectTimer);
      this.#reconnectTimer = null;
    }

    if (this.#reconnectCount >= this.#reconnectCountMax) {
      this.#reconnectCount = 0;
      return;
    }

    this.#reconnectTimer = setTimeout(() => {
      this.initConnect();
      this.#reconnectCount++;
      this.#lockReconnect = false;
    }, 2000);
  };

  // 处理任务队列
  #dealTasks = () => {
    // this.#connectReady = true;

    setTimeout(() => {
      // const userStore = useUserStore();
      // const loginStore = useWsLoginStore();

      // if (userStore.isSign) {
      // 处理堆积的任务
      this.#tasks.forEach((task) => {
        this.send(task);
      });
      // 清空缓存的消息
      this.#tasks = [];
      // } else {
      //   // 如果没登录，而且已经请求了登录二维码，就要更新登录二维码。
      //   loginStore.loginQrCode && loginStore.getLoginQrCode();
      // }
    }, 500);
  };

  // 发送消息
  send = (params) => {
    if (this.#connectReady && this.#connection?.readyState === WebSocket.OPEN) {
      this.#connection.send(JSON.stringify(params));
    } else {
      // 放到队列
      this.#tasks.push(params);
    }
  };

  // 收到消息回调
  onMessage = (params) => {
    // 触发全局事件，携带消息类型和数据
    EventBus.$emit(WS_EVENTS.MESSAGE, {
      // 使用统一事件常量
      channel: params.channel,
      data: params.data,
    });
  };
}

export default new WS();
