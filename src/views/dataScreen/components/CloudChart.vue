<template>
  <div class="ciyun">
    <canvas id="myCanvas"></canvas>
    <div id="tags">
      <a href="http://www.baidu.com" target="_blank" v-for="item in cloudData" :key="item.name">{{item.name}}</a>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-wordcloud';

export default {
  props: {
    data: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      cloudData: [{
        "name": "美食",
        "value": 20
      },
        {
          "name": "网红店",
          "value": 20
        },
        {
          "name": "新冠肺炎疫情表彰大会",
          "value": 20
        },
        {
          "name": "打卡",
          "value": 19
        },
        {
          "name": "十一长假",
          "value": 18
        },
        {
          "name": "自由行",
          "value": 18
        },
        {
          "name": "教师节",
          "value": 17
        },
        {
          "name": "旅游景点攻略",
          "value": 17
        },
        {
          "name": "坡子街",
          "value": 17
        },
        {
          "name": "体育",
          "value": 17
        },
        {
          "name": "张家界",
          "value": 15
        },
        {
          "name": "橘子洲",
          "value": 15
        },
        {
          "name": "房价",
          "value": 15
        },
        {
          "name": "开学",
          "value": 15
        },
        {
          "name": "湘江",
          "value": 15
        },
        {
          "name": "繁华",
          "value": 15
        },
        {
          "name": "湖南公考",
          "value": 14
        },
        {
          "name": "中秋节",
          "value": 14
        },
      ]
    }
  },
  mounted() {
    this.ciyun();
  },
  methods: {
    ciyun() {
      try {
        var i;
        var et = document.getElementById('tags').childNodes;
        for (i in et) {
          // 随机颜色  textColour 设为null
          if (et[i].nodeName == 'A') {
            et[i].style.color = 'rgb(' + parseInt(Math.random() * 255) + ',' + parseInt(Math.random() * 255) + ',' + parseInt(Math.random() * 255) + ')';
          }
          et[i].nodeName == 'A' && et[i].addEventListener('mouseover', function (e) {
            e.preventDefault();
          });
        }

        window.TagCanvas.Start('myCanvas', 'tags', {
          // // 默认颜色
          textColour: null,
          // 默认移入边框
          outlineColour: '#fff',
          reverse: true,
          depth: 2.9,
          // false牵引（鼠标指向的方向就是转动的地方）
          // true 拖拽
          dragControl: true,
          // dragControl: true,
          // 拖拽速度
          decel: 0.95,
          // interval:30,
          // 速度
          maxSpeed: 0.02,
          minSpeed: 0.01,
          // 速度
          initial: [-0.2, 0]
        });
      } catch (e) {
      }
    },
    initChart() {
      this.chart = echarts.init(this.$refs.cloud)
      const data = [{
        "name": "美食",
        "value": 20
      },
        {
          "name": "网红店",
          "value": 20
        },
        {
          "name": "新冠肺炎疫情表彰大会",
          "value": 20
        },
        {
          "name": "打卡",
          "value": 19
        },
        {
          "name": "十一长假",
          "value": 18
        },
        {
          "name": "自由行",
          "value": 18
        },
        {
          "name": "教师节",
          "value": 17
        },
        {
          "name": "旅游景点攻略",
          "value": 17
        },
        {
          "name": "坡子街",
          "value": 17
        },
        {
          "name": "体育",
          "value": 17
        },
        {
          "name": "张家界",
          "value": 15
        },
        {
          "name": "橘子洲",
          "value": 15
        },
        {
          "name": "房价",
          "value": 15
        },
        {
          "name": "开学",
          "value": 15
        },
        {
          "name": "湘江",
          "value": 15
        },
        {
          "name": "繁华",
          "value": 15
        },
        {
          "name": "湖南公考",
          "value": 14
        },
        {
          "name": "中秋节",
          "value": 14
        },
      ]
      const option = {
        backgroundColor: '#012248',
        series: [{
          type: 'wordCloud',
          gridSize: 7,
          sizeRange: [15, 40],
          rotationRange: [-90, 90],
          // rotationStep: 45,
          shape: 'sphere',
          width: '100%',
          height: '100%',
          drawOutOfBound: false,
          textStyle: {
            normal: {
              color: function () {
                // 随机颜色
                return 'rgb(' + [
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55),
                  Math.round(Math.random() * 200 + 55)
                ].join(',') + ')';
              },
            },
            emphasis: {
              shadowBlur: 10,
              shadowColor: '#ffffff'
            }
          },
          data: data
        }]
      }

      this.chart.setOption(option, true)
    },

  }
}


</script>
<style lang="scss" scoped>
.ciyun {
  text-align: center;
  width: 100%;
  height: 100%;
}

#myCanvas {
  width: 100%;
  height: 100%;
}

#tags {
  width: 100%;
  height: 100%;
}
</style>
