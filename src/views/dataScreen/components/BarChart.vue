<template>
  <!-- echarts -->
  <div class="echarts" ref="bar"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    data: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.bar)
      const xData = ['男', '女']
      const yData = [20, 50]
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          formatter: function (parms) {
            var str =
              parms[0].axisValue + ':' + parms[0].value
            return str;
          },
        },
        legend: {
          show: false,
          data: ['预警项目'],
          textStyle: {fontSize: 12, color: '#fff'},
          itemWidth: 12,
          itemHeight: 12,
          itemGap: 15,
          top: '-1%',
          right: '2%',
        },
        textStyle: {
          color: '#ffffff',
        },
        color: ['#24F3FF', '#24F3FF', '#FDBF47', '#FDBF47'],
        grid: {
          containLabel: true,
          left: '6%',
          top: '20%',
          bottom: '6%',
          right: '6%',
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisLine: {
            lineStyle: {
              color: '#B5B5B5',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            margin: 10, //刻度标签与轴线之间的距离。
            fontFamily: 'Microsoft YaHei',
            color: '#ffffff',
          },
        },
        yAxis: {
          name: "个",
          nameTextStyle: {
            verticalAlign: 'middle',
            align: "right"
          },
          type: 'value',
          min: 0,
          boundaryGap: ['20%', '60%'],
          axisLine: {
            show: true,
            lineStyle: {
              color: '#B5B5B5',
            },
          },
          splitLine: {
            lineStyle: {
              // 使用深浅的间隔色
              color: ['#B5B5B5'],
              type: 'dashed',
              opacity: 0.5,
            },
          },
          axisLabel: {},
        },
        series: [
          {
            name: "预警项目",
            data: yData,
            stack: 'zs',
            type: 'bar',
            barMaxWidth: 'auto',
            barWidth: 22,
            itemStyle: {
              color: {
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                type: 'linear',
                global: false,
                colorStops: [
                  {
                    offset: 0,
                    color: '#0FA0FF',
                  },
                  {
                    offset: 1,
                    color: '#24F3FF',
                  },
                ],
              },
            },
          },
          {
            data: yData,
            type: 'pictorialBar',
            barMaxWidth: '10',
            symbolPosition: 'end',
            symbol: 'diamond',
            symbolOffset: [0, '-50%'],
            symbolSize: [22, 10],
            zlevel: 2,
          },
        ],
      };

      this.chart.setOption(option, true)
    }
  }
}


</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
}
</style>
