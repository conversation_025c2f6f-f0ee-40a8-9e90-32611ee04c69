<template>
  <div ref="line" class="chart"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  props: {
    chartData: {
      type: Object,
      default: () => {
      }
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.line)
      const xxData = ['2024-09-01', '2024-09-02', '2024-09-03', '2024-09-04', '2024-09-05', '2024-09-06']
      const yyData = [[39348858, 22, 234551, 8, 10, 3, 19], [0.1, 3, 6, 8, 2, 3, 1], [0.1, 4, 6, 8, 10, 3, 19], [1, 4, 16, 18, 10, 3, 19]]
      const aimData = [{name: '网站', aimId: 1}, {name: '微博', aimId: 2}, {name: '抖音', aimId: 3}, {
        name: '头条',
        aimId: 4
      }]
      const data = []
      const colors = ['#1D80DA', '#02F4FF', '#E3BC2D', '#FF6632', '#A7FFB0', '#8A01E1']
      for (var i = 0; i < yyData.length; i++) {
        data.push(
          {
            name: aimData[i].name,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 8,
            shadowColor: 'rgba(0, 0, 0, 0.1)',
            shadowBlur: 10,
            showSymbol: false,
            lineStyle: {
              width: 2,
              color: colors[i]
            },
            itemStyle: {
              color: colors[i],
            },
            emphasis: {
              color: colors[i],
              borderColor: 'rgba(0,196,132,0.2)',
              extraCssText: 'box-shadow: 4px 4px 4px rgba(0, 0, 0, 1);',
              borderWidth: 10
            },
            data: yyData[i]
          }
        )
      }
      // aimData
      const option = {
        tooltip: {
          trigger: 'axis',
          show: true,
          backgroundColor: '#1d398e',
          borderColor: '#1d398e',
          borderWidth: 0,
          padding: 10,
          textStyle: {
            color: '#fff' // 文字的颜色
          },
          formatter: function (params) {
            let html = params[0].name
            params.forEach((item, index) => {
              html += (`<br/>${item.marker + item.seriesName}  ${item.value === 0.1 ? 0 : item.value}`)
            })
            return html
          }
        },
        legend: {
          type: 'scroll',
          show: true,
          icon: 'roundRect',
          itemWidth: 10,
          itemHeight: 8,
          textStyle: {
            fontSize: 12,
            color: '#fff'
          }
        },
        grid: {
          top: '20%',
          left: '0%',
          right: '0%',
          bottom: '1%',
          containLabel: true
        },
        xAxis: [{
          type: 'category',
          axisLine: {
            show: true
          },
          axisTick: {
            show: true,
            inside: true
          },
          axisLabel: {
            interval: 0,
            fontSize: 12,
            color: 'rgba(255,255,255,0.5)'
          },
          splitLine: {
            show: false
          },
          // data: ['08/01', '08/02', '08/03', '08/04', '08/05', '08/06', '08/07']
          data: xxData

        }],

        yAxis: [{
          type: 'log',
          splitLine: {
            show: true,
            lineStyle: {
              color: ['#fff'],
              opacity: 0.06
            }
          },
          axisLine: {
            show: false
          },
          axisLabel: {
            show: true,
            margin: 20,
            fontSize: 12,
            color: 'rgba(255,255,255,0.5)',
            formatter: (value) => {
              return value === 0.1 ? 0 : value
            }
          },
          axisTick: {
            show: false
          }
        }],
        series: data
      }
      this.chart.setOption(option, true)
    }
  }
}
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>

