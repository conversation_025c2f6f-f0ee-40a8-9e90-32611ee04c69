@import '@/assets/styles/home.scss';

.home {
  width: 100%;
  // height: calc(100vh - 80px);
  // background: url("../assets/images/background.jpg") no-repeat;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center center;
  background-size: cover;
  padding: 0px;
  font-family: PangMenZhengDao;
  // overflow: auto;
  position: relative;

  .home-content {
    .home-head {
      display: flex;
      justify-content: center;
      position: relative;
      height: 14%;

      img {
        width: 100%;
        height: 100%;
      }

      .head-title {
        font-size: 0.48rem;
        font-weight: bold;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -80%);
        // color: #ffffff;
        @include font_color('mainColor');
        font-family: PangMenZhengDao;
      }

      .date {
        font-size: 0.16rem;
        // color: #ffffff;
        @include font_color('mainColor');
        position: absolute;
        right: 15%;
        top: 11%;
      }

      .switch {
        position: absolute;
        right: 7%;
        top: 8%;

        ::v-deep .el-switch__core {
          width: 0.4rem !important;
          height: 0.2rem;
        }

        ::v-deep .el-switch__core::after {
          width: 0.16rem;
          height: 0.16rem;
        }

        ::v-deep .el-switch.is-checked .el-switch__core::after {
          margin-left: -0.17rem;
        }


      }

      .full-icon {
        width: 0.2rem;
        height: 0.2rem;
        position: absolute;
        right: 3%;
        top: 9%;
        cursor: pointer;
      }
    }

    // 内容
    .home-contain {
      margin-top: 0.2rem;
      display: flex;
      justify-content: space-between;
      color: #FFFFFF;
      padding: 0 4.84%;
      // padding-bottom: 1%;
      // height: 100%;

      // 左
      .home-lf {
        width: 30%;
        height: 100%;
        display: flex;
        flex-direction: column;


        .focus-space {
          height: 3.4%;
        }

        .focus-ct {
          // margin: 9% 0;
        }

        .emtion-style {
          height: 28.1%;
          display: flex;
          flex-direction: column;

          .emtion-chart {
            // background: url("../assets/images/emtion-bg.png");
            background-repeat: no-repeat;
            background-position: center center;
            background-size: 100% 100%;
            margin-left: 2%;
            margin-top: 2%;
            padding-top: 1%;
            flex: 1;
            // height: 80%;
          }
        }
      }

      .home-space {
        height: 4%;
      }

      .home-space-two {
        height: 2%;
      }

      // 中
      .home-ct {
        flex: 1;
        height: 100%;
        // margin-top: 1%;
        margin-left: 4%;
        margin-right: 4%;

        .databaseTotol {
          margin: 0.72rem 0 0.52rem 0;
          font-size: 0.24rem;
          font-weight: 600;
          color: #FFFFFF;
          text-align: center;
          display: flex;
          align-items: center;
          height: 0.6rem;

          .databaseTotol_title {
            display: inline-block;
            vertical-align: middle;
          }

          .databaseTotol_num {
            display: inline-block;
            vertical-align: middle;
            margin-left: 4%;
            cursor: pointer;
          }
        }

        .light-Total {
          border: 1px solid #84B4FF;
          margin: 0.8rem 0 0.52rem 0;
          height: 0.52rem;
          box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.15);
          font-size: 0.2rem;
          border-radius: 0.36rem;
          color: #333333;
          text-align: center;
          // margin-bottom: 0.1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          // width: 5.7rem;
          // height: 0.72rem;
          background: #E6F0FF;
          cursor: pointer;

          .total-number {
            font-weight: bold;
            font-size: 0.36rem;
            color: #247CFF;
            line-height: 0.47rem;
            text-shadow: 0px 1px 3px rgba(0, 0, 0, 0.5);
          }
        }

        .lineChart {
          // margin-top: 5%;
          height: 20%;
        }

        .active-account {
          // margin-top: 6%;
          // margin-bottom: 7%;
          height: 21%;
          display: flex;
          flex-direction: column;
        }

        .account-chart {
          // background: url("../assets/images/one.png") no-repeat center center;
          background-repeat: no-repeat;
          background-position: center center;
          background-size: 100% 100%;
          margin-left: 2%;
          margin-top: 2%;
          flex: 1;
          // height: 80%;
        }

        .cloud-data {
          height: 3.13rem;
          display: flex;
          flex-direction: column;
          margin-bottom: 0.4rem;

          .clound-chart {
            background-repeat: no-repeat;
            background-position: center center;
            background-size: 100% 100%;
            margin-left: 2%;
            margin-top: 0.14rem;
            box-sizing: border-box;
            height: 80%;
            width: 100%;
            // flex: 1;
          }
        }
      }

      .home-rg {
        width: 30%;
        height: 100%;


        .right-three {
          height: 3.13rem;
          display: flex;
          flex-direction: column;
          margin-bottom: 0.4rem;

          .trend-list {
            // height: 80%;
            flex: 1;
            // background: url("../assets/images/trend-bg.png") no-repeat center center;
            background-repeat: no-repeat;
            background-position: center center;
            background-size: 100% 100%;

            .trend-title {
              display: flex;
              justify-content: flex-start;
              text-align: left;

              span {
                width: 25%;
                line-height: 0.3rem;
              }

              span:nth-child(1) {
                width: 15%;
              }
            }
          }

          .trend-wrap {
            height: 80%;
            overflow: hidden;

            .el-tooltip__popper {
              width: 250px;
            }
          }

          .trend-main {
            display: flex;
            justify-content: flex-start;
            text-align: center;
            font-size: 0.16rem;
            // color: #fff;
            @include font_color('mainColor');
            padding: 0.1rem 0;
            height: 100%;

            .deal-title-pre {
              width: 15%;
              display: flex;
              justify-content: center;

              span {
                color: #FFFFFF;
                font-size: 0.16rem;
                display: inline-block;
                width: 0.2rem;
                height: 0.2rem;
                border-radius: 50%;
                // background: #00B3FF;
                @include background-color('borderColor');
                text-align: center;
                line-height: 0.2rem;
              }
            }

            // span:nth-child(2) {
            //     white-space: nowrap;
            //     overflow: hidden;
            //     text-overflow: ellipsis;
            //     cursor: pointer;
            //     margin: 0 5%;
            //     width: 45%;
            // }

            span:nth-child(3) {
              width: 25%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              margin-right: 2%;
            }

            span:nth-child(4) {
              width: 25%;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
  }

  .search-btn {
    margin-left: 10px;
    border-radius: 4px;
    // border: 1px solid #00B3FF;
    // background: #00B3FF;
    color: #fff;
    @include border_color('borderColor');
    @include background_color('borderColor');
  }

}

.area-list {
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  margin-left: 2%;
  margin-top: 0.14rem;
  // height: 80%;
  flex: 1;
  overflow: hidden;
  font-size: 0.16rem;
  /* 隐藏 WebKit 浏览器的滚动条 */
  .deal-wrap::-webkit-scrollbar {
    display: none;
  }

  .area-mainer .area-deal-border {
    padding-right: 0.1rem;
    border-right: 1px dashed #fff;
  }

  .area-mainer .area-deal-border-light {
    padding-right: 0.1rem;
    border-right: 1px dashed #999;
  }

  .area-main-title {
    padding: 0 0.2rem;
    display: flex;
    justify-content: space-between;

    .deal-title {
      width: 50%;
      font-size: 0.16rem;
      margin: 0.09rem 0;
    }
  }

  .main-title-line {
    padding-right: 0.1rem;
  }

  .area-mainer .area-main-line {
    width: 50%;
  }

  .area-mainer::-webkit-scrollbar {
    display: none;
  }

  .area-mainer {
    padding: 0 0.2rem;
    height: calc(100% - 0.44rem);
    overflow: scroll;
    display: flex;
    justify-content: space-between;

    .area-line {
      height: 100%;
      border-right: 1px dashed #fff;
    }
  }

  .area-main-wrap {
    width: 50%;
    font-size: 0.16rem;
    line-height: 0.22rem;

    .area-total {
      color: #57CDFF;
      text-decoration: underline;
    }
  }

  .deal-title {
    margin-top: 0.12rem;
    display: flex;
    justify-content: flex-start;
    text-align: center;
    // color: #02F4FF;
    @include font_color('subTitleColor');
    font-weight: 500;
    margin-bottom: 1.5%;

    span:nth-child(1) {
      width: 65%;
    }

    span {
      display: inline-block;
      width: 35%;
      text-align: center;
    }
  }

  .deal-main {
    margin-bottom: 0.05rem;

    span:nth-child(1) {
      width: 65%;
    }

    span {
      display: inline-block;
      width: 35%;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      @include font_color('mainColor');

      &:nth-child(2) {
        @include font_color('yqColor');
      }
    }
  }
}

// 公共热点列表
.deal-list {
  // background: url("../assets/images/one.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  margin-left: 2%;
  margin-top: 0.14rem;
  padding-top: 1%;
  // height: 80%;
  flex: 1;
  overflow: hidden;

  .point-wrap {
    padding: 0.14rem 0.2rem 0 0.2rem;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;

    .point-content {
      cursor: pointer;
      width: 100%;
    }

    .point-main {
      height: 90%;
      overflow: auto;
    }

    .point-box-light {
      background: #E6F0FF;
      border: 1px solid #84B4FF;
      color: #999;
    }

    .point-box-dark {
      background: rgba(10, 60, 130, 0.58);
      border: 1px solid #84B4FF;
      color: #ccc;
    }

    .point-box {
      height: 0.52rem;
      width: 46%;
      margin-bottom: 0.06rem;
      border-radius: 8px;
      display: flex;
      align-items: center;
      font-size: 0.12rem;

      img {
        width: 0.32rem;
        height: 0.32rem;
        margin: 0 0.06rem 0 0.12rem;
        border-radius: 50%;
      }

      .point-name {
        @include font_color('accountColor');
        font-size: 0.14rem;
        overflow: hidden; /* 确保超出容器的文本被裁剪 */
        white-space: nowrap; /* 确保文本在一行内显示 */
        text-overflow: ellipsis;
        width: 90%;
      }
    }
  }

  .deal-title {
    margin-top: 0.12rem;
    font-size: 0.16rem;
    line-height: 0.22rem;
    display: flex;
    justify-content: flex-start;
    text-align: center;
    // color: #02F4FF;
    @include font_color('subTitleColor');
    font-weight: 500;
    margin-bottom: 1.5%;

    span {
      display: inline-block;
      width: 15%;
      text-align: center;
    }

    span:nth-child(3) {
      width: 19%;
    }
  }

  /* 隐藏 WebKit 浏览器的滚动条 */
  .deal-wrap::-webkit-scrollbar {
    display: none;
  }

  .deal-wrap {
    height: 80%;
    overflow-y: auto;

    .deal-title-pre {
      width: 15%;
      display: flex;
      justify-content: center;

      img {
        width: 0.17rem;
        height: 0.22rem;
      }
    }

    .deal-main {
      display: flex;
      justify-content: flex-start;
      font-size: 0.16rem;
      line-height: 0.22rem;
      color: #fff;
      padding: 0.1rem 0;
      align-items: center;
      text-align: center;

      span.deal-text-left {
        text-align: left;
      }

      span:nth-child(1) {
        color: #FFFFFF;
        font-size: 0.14rem;
        display: inline-block;
        width: 0.2rem;
        height: 0.2rem;
        border-radius: 50%;
        // background: #00B3FF;
        @include background_color('borderColor');
        line-height: 0.2rem;
      }

      span:nth-child(2) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 66%;
        cursor: pointer;
        // margin: 0 0.2rem;
        @include font_color('mainColor');
      }

      span:nth-child(3) {
        width: 19%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        // flex: 1;
        @include font_color('mainColor');

        .fire {
          width: 0.17rem;
          height: 0.21rem;
          margin-bottom: -5px;
          margin-right: 2px;
        }
      }
    }
  }
}

.focus-case {
  display: flex;
  flex-direction: column;
  // height: 21.1%;
  height: 3.13rem;
  margin-bottom: 0.4rem;
}

.home-content .alert-message {
  margin-bottom: 0.58rem;
}

// 公共头部
.section-title {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: space-between;
  font-size: 0.2rem;
  line-height: 0.3rem;
  color: #DCDEE0;
  width: 100%;
  height: 0.4rem;
  // padding-bottom: 1.8%;
  // padding-top: 1%;
  box-sizing: border-box;
  // background-image: url("../assets/images/sub-bg.png");
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;

  .focus-center {
    display: flex;
    align-items: center;
  }

  .home-title-more {
    margin-bottom: 3px;
    font-size: 0.16rem;
    line-height: 0.2rem;
    background: -webkit-linear-gradient(top, #0EC5EC, #FFFFFF);
    -webkit-background-clip: text;
    color: transparent;
    cursor: pointer;
  }

  .home-title-more-light {
    margin-bottom: 3px;
    font-size: 0.16rem;
    line-height: 0.2rem;
    color: #666;
    cursor: pointer;
  }

  .text {
    // cursor: pointer;
    background: linear-gradient(to bottom, #0EC5EC 0%, #FFFFFF 59%, #FFFFFF 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-left: 0.4rem;
    font-weight: bold;
  }

  .light-text {
    cursor: pointer;
    color: #333333;
    font-weight: bold;
  }

  .light-spot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #247CFF;
    margin-right: 6px;
  }

  .go-image {
    width: 0.28rem;
    height: 0.28rem;
  }
}

.btn {
  margin-right: 0.12rem;

  ::v-deep .el-radio-button__inner {
    background: none;
    padding: 0.07rem;
    border: none;
    font-size: 0.16rem;
    // color: #FFFFFF;
    @include font_color('mainColor');

  }
}

.timeSelect .el-select-dropdown__item.selected {
  color: #02F4FF;
}

.home-content {
  .timeRange {
    position: absolute;
    right: 0.4rem;
    height: 0.26rem;
    bottom: 0.14rem;
    font-size: 0.16rem;
    @include font_color('titleColor');
  }

  .time-light-range {
    position: absolute;
    right: 0.4rem;
    height: 0.26rem;
    bottom: 0.14rem;
    font-size: 0.16rem;
    @include font_color('titleColor');

  }
}

.date-wrap {
  ::v-deep .el-input__inner {
    background: none;
    // border: 1px solid #FFFFFF;
    // color: #FFFFFF;
    @include border_color('dateBorderColr');
    @include font_color('mainColor');
  }
}


.warnless-warp {
  overflow: hidden;
  color: #fff;
  padding: 1%;
  height: 100%;

  .item {
    height: 100%;

    .title {
      padding: 6px;
      display: inline-block;
    }
  }
}

.seamless-warp {
  overflow: hidden;
  color: #fff;
  padding: 1%;
  height: 100%;

  .item {
    height: 100%;

    .title {
      padding: 6px;
      display: inline-block;
    }
  }
}

.text_warp {
  display: block;
  max-width: 500px;
  max-height: 300px;
  overflow: auto;
  white-space: wrap;
  cursor: pointer;
}

.text_els {
  width: 50%;
  margin: 0px 4%;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  cursor: pointer;
}

.noneData {
  // color: #fff;
  @include font_color('mainColor');
  font-size: 0.14rem;
  text-align: center;
  margin-top: 10%;
  width: 100%;

  img {
    width: 0.6rem;
    height: 0.8rem;
  }
}

.fullscreen-mode .home {
  @include background-color('homeBackground');

  .home-content {
    height: 100vh;
    overflow: auto;
  }
}

.point-case-black {
  background: linear-gradient(180deg, #186FB8 0%, #0A3C82 100%);

  p {
    color: #fff;
  }

  .case-box {
    .case-point-num {
      color: #02F4FF;
      text-shadow: 0px 2px 3px rgba(0, 0, 0, 0.5);
    }
  }
}

.point-case-light {
  background: #E6F0FF;
  border: 1px solid #84B4FF;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.15);

  p {
    color: #333;
  }

  .case-box {
    .case-point-num {
      color: #247CFF;
      text-shadow: 0px 2px 3px rgba(0, 0, 0, 0.5);
    }
  }
}

.point-case {
  height: 1.12rem;
  width: 100%;
  margin-bottom: 0.75rem;
  padding: 0 0.44rem;
  border-radius: 0.89rem;

  .case-box {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.24rem;
    font-weight: bold;

    p {
      margin: 0;
      height: 100%;
      display: flex;
      align-items: center;
    }

    .case-point-num {
      margin: 0 0.22rem;
      font-size: 0.48rem;
      font-weight: bold;
      cursor: pointer;
    }

    .case-unit {
      font-size: 0.18rem;
      @include font_color('mainColor');
    }
  }

  img {
    width: 0.93rem;
  }
}
