<template>
  <div class="login" :style="styleObj">
    <div class="login-left">
      <img :src="backLeftLogo?backLeftLogo:leftLogo" alt="">
    </div>
    <div class="login-form">
      <div class="top-wechat">
        <!-- <div class="code-style" v-if="flag">
          <img src="@/assets/images/login-bubble.png" alt="" class="bubble-img">
          <img src="@/assets/images/two-code.svg" alt="" class="code-img" @click="handleChat">
        </div>
        <div class="code-style" v-if="!flag">
          <img src="@/assets/images/login-code.png" alt="" class="bubble-img">
          <img src="@/assets/images/computer.svg" alt="" class="code-img" @click="handleChat(1)">
        </div> -->
      </div>
      <!-- 短信图形验证码 -->
      <Verify
        v-show="msgShow"
        @clickShow="clickShow"
        @success="capctchaCheckSuccess"
        :mode="'pop'"
        :captchaType="'blockPuzzle'"
        :imgSize="{ width: '330px', height: '155px' }"
        ref="verify"
      ></Verify>
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" v-if="flag">
        <div class="logo-box"><img class="login-logo" :src="sideLogo?sideLogo:logo" alt=""></div>
        <h3 class="title">{{systemContent.sysName || '博约舆情监测系统'}}</h3>
        <el-form-item prop="username" style="width:76%;margin-left: 12%;">
          <el-input v-model="loginForm.username" type="text" auto-complete="off" placeholder="账号名称">
            <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
          </el-input>
        </el-form-item>
        <el-form-item prop="password" style="width:76%;margin-left: 12%;">
          <el-input v-model="loginForm.password" show-password type="password" auto-complete="off" placeholder="账号密码"
                    @keyup.enter.native="handleLogin">
            <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
          </el-input>
        </el-form-item>
        <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 12%;">记住密码</el-checkbox>
        <el-form-item style="width:76%;margin-left: 12%;">
          <el-button :loading="loading" size="medium" type="primary" style="width:100%;"
                     @click.native.prevent="handleLogin">
            <span v-if="!loading">登 录</span>
            <span v-else>登 录 中...</span>
          </el-button>
        </el-form-item>
      </el-form>
      <!-- 微信二维码  -->
      <div class="wechat-code" v-if="!flag">
        <div class="logo-box"><img class="login-logo" src="@/assets/logo/logo-title.png" alt=""></div>
        <h3 class="title">博约舆情监测系统</h3>
        <template v-if="codeCurSta">
          <div class="bind-code">
            <!-- <div class="code-back" @click="goAccount">
              <img src="@/assets/images/back.png" alt="">
              <span>返回账号登录</span>
            </div> -->
            <div class="showWechat">
              <img src="@/assets/images/finish.png" alt/>
              微信扫码成功，请绑定账号
            </div>
            <el-form ref="loginForm" :model="loginForm" :rules="loginRules">
              <el-form-item prop="username">
                <el-input v-model="loginForm.username" style="width:76%;margin-left: 12%;" type="text"
                          auto-complete="off" placeholder="请输入手机号">
                  <svg-icon slot="prefix" icon-class="user" class="el-input__icon input-icon"/>
                </el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input v-model="loginForm.password" style="width:76%;margin-left: 12%;" type="password" show-password
                          auto-complete="off" placeholder="请输入登录密码" @keyup.enter.native="handleLogin">
                  <svg-icon slot="prefix" icon-class="password" class="el-input__icon input-icon"/>
                </el-input>
              </el-form-item>
              <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 10px 12%;">记住密码</el-checkbox>
              <el-form-item style="width:76%;margin-left: 12%;">
                <el-button :loading="loading" size="medium" type="primary" style="width:100%;background: #247CFF;"
                           @click.native.prevent="handleLogin">
                  <span v-if="!loading">确认绑定并登录</span>
                  <span v-else>登 录 中...</span>
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </template>
        <template v-else>
          <div v-if="showCodeImg" class="wechatDiv">
            <div class="center-image"><img src="@/assets/images/code.png" alt=""></div>
            <div class="refresh-code" @click="refreshWechat"><i class="el-icon-refresh"></i> 刷新二维码</div>
          </div>
          <div v-if="!showCodeImg" class="rejectShow">
            <i class="el-icon-error"></i>
            <p>您已取消此次登录</p>
            <el-button type="primary" size="mini" @click="tryAgain">重试</el-button>
          </div>
        </template>
      </div>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2010-{{copyrightYear}} 安徽博约科技 版权所有 <a href="https://beian.miit.gov.cn/"
                                                                        target="_blank">皖ICP备19003697号</a></span>
    </div>
  </div>
</template>

<script>
import {getCodeImg} from '@/api/login'
import Cookies from 'js-cookie'
import {encrypt, decrypt} from '@/utils/jsencrypt'
import Verify from "@/components/Verifition/Verify";
import leftLogo from '@/assets/logo/default-sider-bg.png'
import logoImg from '@/assets/logo/default-logo.png'

export default {
  name: 'Login',
  components: {Verify},
  data() {
    return {
      logo: logoImg,
      leftLogo: leftLogo,
      styleObj: {},
      flag: true,
      codeCurSta: false,
      showCodeImg: true,
      codeTimer: null,
      codeStatus: '',
      wechatQRCodeId: '',
      wechatImg: '',
      msgShow: false,
      copyrightYear: '',
      codeUrl: '',
      cookiePassword: '',
      loginForm: {
        username: '',
        password: '',
        rememberMe: false
      },
      loginRules: {
        username: [{required: true, trigger: 'blur', message: '账号名称不能为空'}],
        password: [{required: true, trigger: 'blur', message: '账号密码不能为空'}]
      },
      loading: false,
      redirect: undefined
    }
  },
  computed: {
    systemContent() {
      return this.$store.state.user
    },
    backImage() {
      return this.$store.state.user.backImage
    },
    backLeftLogo() {
      return this.$store.state.user.logoImg
    },
    sideLogo() {
      return this.$store.state.user.logo
    },
  },
  watch: {
    $route: {
      handler: function (route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    },
    backImage: {
      handler: function (val) {
        console.log(val, 'val');
        if (val) {
          this.styleObj = {
            background: 'url(' + val + ') 0% 0% / cover no-repeat'
          }
        } else {
          this.styleObj = {
            background: 'url(' + require("@/assets/logo/default-bg.png") + ') 0% 0% / cover no-repeat'
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.refreshCopyrightYear()
    this.getCookie()
  },
  methods: {
    // 登录方式切换
    handleChat(val) {
      this.flag = !this.flag
      if (val) {
        this.wechatLogin()
      }
    },
    // 微信登录
    wechatLogin() {
      this.codeCurSta = false
      this.showCodeImg = true
      // this.getWechatLogo()
    },
    // 重试
    tryAgain() {
      this.showCodeImg = true
      this.refreshWechat()
    },
    // 刷新二维码
    async refreshWechat() {
      const res = await getWechatlogoApi()
      this.wechatImg = res.data.data.qRCode
      this.wechatQRCodeId = res.data.data.wechatQRCodeId
      this.start()
    },
    start() {
      // 获取二维码状态
      this.codeTimer = setInterval(() => {
        const params = {
          wechatQRCodeId: this.wechatQRCodeId
        }
        getCodeStatusApi(params).then((res) => {
          this.codeStatus = res.data;
          if (this.codeStatus == "SIGNED_IN") {
            this.$store.dispatch("weChatLogin", params).then(() => {
              this.$router.push({path: "/index"}).catch(() => {
              });
            });
            clearInterval(this.codeTimer);
          } else if (this.codeStatus == "NO_USER_SCANNED") {
            // 没有可登录账户,微信第一次扫码
            this.codeCurSta = true;
            this.loginForm.rejectNum = 1;
            clearInterval(this.codeTimer);
            // this.getCode();
          } else if (this.codeStatus == "REJECT_LOGIN") {
            // 拒绝登录
            this.showCodeImg = false;
            clearInterval(this.codeTimer);
          }
        })
      }, 2000)
    },
    // 账号登录
    goAccount() {
      this.codeCurSta = false
      this.flag = true
      clearInterval(this.codeTimer);
    },
    clickShow() {
      this.msgShow = false;
    },
    capctchaCheckSuccess(params) {
      if (params.captchaVerification) {
        this.loading = true;
        if (this.loginForm.rememberMe) {
          Cookies.set('username', this.loginForm.username, {expires: 30})
          Cookies.set('password', encrypt(this.loginForm.password), {expires: 30})
          Cookies.set('rememberMe', this.loginForm.rememberMe, {expires: 30})
        } else {
          Cookies.remove('username')
          Cookies.remove('password')
          Cookies.remove('rememberMe')
        }
        let params = {
          username: this.loginForm.username,
          password: this.$md5(this.loginForm.password),
          rememberMe: this.loginForm.rememberMe
        }
        this.$store
          .dispatch('Login', params)
          .then(() => {
            // this.$store.dispatch('GetRouters').then(() => {
            //   const homePath = this.$store.state.user.newPath
            //   console.log('homePath :>> ', this.$store.state.user.newPath);
            //   this.$router.push(`/${homePath}`)
            // })
            // this.$router.push({ path: '/index' }).catch(() => {})

            this.$store.dispatch('GetInfo').then(res => {
              // 拉取user_info
              const roles = res.roles
              this.$store.dispatch('GenerateRoutes', {roles}).then(accessRoutes => {
                // 根据roles权限生成可访问的路由表
                this.$router.addRoutes(accessRoutes) // 动态添加可访问路由表
                const homePath = accessRoutes[0].children[0].path
                console.log('homePath :>> ',accessRoutes[0].children[0].path);
                this.$store.commit('SET_NEWPATH', homePath)
                this.$router.push(`/${homePath}`)
              })
            }).catch(err => {
            })
          })
          .catch(() => {
            this.loading = false
            this.msgShow = false;
          })
      }
    },
    refreshCopyrightYear() {
      this.copyrightYear = new Date().getFullYear()
    },
    getCookie() {
      const username = Cookies.get('username')
      const password = Cookies.get('password')
      const rememberMe = Cookies.get('rememberMe')
      this.loginForm = {
        username: username === undefined ? this.loginForm.username : username,
        password: password === undefined ? this.loginForm.password : decrypt(password),
        rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.msgShow = true;
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.center-image {
  display: flex;
  justify-content: center;

  img {
    width: 140px;
    height: 140px;
  }
}

.showWechat {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  font-weight: bold;
  color: #333;
  font-size: 18px;

  img {
    width: 24px;
    margin-right: 4px;
  }
}

.refresh-code {
  margin-top: 10px;
  display: flex;
  justify-content: center;
  color: #537BC2;
  cursor: pointer;
}

.rejectShow {
  width: 100%;
  overflow: hidden;
  text-align: center;
  padding: 20px 0px 40px;
  box-sizing: border-box;

  .el-icon-error {
    font-size: 40px;
    color: red;
  }

  p {
    margin: 20px 0px 30px;
    padding: 0;
    text-align: center;
  }
}

.top-wechat {
  .code-style {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .bubble-img {
      // width: 110px;
      height: 28px;
    }

    .code-img {
      width: 75px;
      height: 75px;
      cursor: pointer;
    }
  }
}

.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  // background-image: url('../assets/images/login-background.png');
  // background-size: cover;
}

.title {
  font-weight: 500;
  font-size: 28px;
  color: #537BC2;
  line-height: 52px;
  margin: 0px auto 20px auto;
  text-align: center;
}

.login-left {
  background: #85A9E7;
  width: 400px;
  height: 458px;
  border-radius: 12px 0 0 12px;
  box-shadow: 7px 0px 27px 0px #85A9E7;

  img {
    width: 100%;
    height: 458px;
  }
}

.login-form {
  border-radius: 0 12px 12px 0;
  background: #ffffff;
  width: 400px;
  height: 458px;
  padding: 25px 25px 20px 25px;
  box-shadow: 7px 0px 27px 0px #85A9E7;

  .logo-box {
    width: 100%;
    text-align: center;
    margin-bottom: 10px;
  }

  .login-logo {
    width: 80px;
  }

  .el-input {
    height: 38px;

    input {
      height: 38px;
      background: #f8f8f8;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  width: 33%;
  height: 38px;
  float: right;

  img {
    cursor: pointer;
    vertical-align: middle;
  }
}

.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #333;
  // font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}

.login-code-img {
  height: 38px;
}
</style>
