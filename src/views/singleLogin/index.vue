<template>
  <div class="out">
    <div>页面正在跳转中，请稍后... <i class="el-icon-loading"></i></div>
  </div>
</template>

<script>
import {removeToken, getToken} from '@/utils/auth'
import {getLoginToken} from '@/api/login'

export default {
  name: 'SingleLogin',
  data() {
    return {
      loading: false
    }
  },
  created() {
    this.handleLogin()
  },
  methods: {
    async handleLogin() {
      removeToken()
      const token = decodeURIComponent(this.$route.query.token)
      let params = {token: token}

      if(!this.$route.query?.token&&this.$route.query?.param&&this.$route.query?.code){
       await getLoginToken({code: this.$route.query?.code,param: this.$route.query?.param}).then(res => {
          if(res.code==200){
            params = {token: res.token}
          }
        })
      }
      
      this.loading = true
      this.$store
        .dispatch('LoginBySingle', params)
        .then(() => {
          this.$store.dispatch('GetRouters').then(() => {

            if (this.$route.query?.flag == 'collect') {
              this.$router.push({path: '/system/collect'})
            } else if (this.$route.query?.flag == 'case') {
              this.$router.push({
                path: '/keyFocusMonitor',
                query: {data: this.$route.query?.data}
              })
            } else if (this.$route.query?.flag == 'politic' || this.$route.query?.flag == 'plat') {
              this.$router.push({
                path: '/fullSearch/searchRank',
                query: {flag: this.$route.query.flag}
              })
            } else if (this.$route.query?.flag == '1') { // 舆情处置
              this.$router.push({
                path: '/infoDeal',
                query: {flag: this.$route.query?.flag, name: this.$route.query?.name}
              })
            } else if (this.$route.query?.flag == '2') { // 舆情分发
              this.$router.push({
                path: '/infoSend',
                query: {flag: this.$route.query?.flag, name: this.$route.query?.name}
              })
            } else if (this.$route.query?.param&&this.$route.query?.code) { // 舆情监测
              this.$router.push({path: '/publicOpinionMonitor'})
            } else {
              const homePath = this.$store.state.user.newPath
              console.log('homePath :>> ', this.$store.state.user.newPath);
              this.$router.push(`/${homePath}`)
            }

          })

        })
        .catch(() => {
          this.loading = false
        })
    }
  }
}
</script>

