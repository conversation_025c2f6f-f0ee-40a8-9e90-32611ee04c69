<template>
  <div class="app-container">
    <div class="navbar">
      <el-form :model="queryParams" ref="queryParams" :inline="true">
        <el-form-item label="过滤内容" prop="word">
          <el-input
            v-model.trim="queryParams.word"
            placeholder="请输入过滤内容"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item label="所属模块" prop="isPlan">
          <el-select v-model="queryParams.isPlan" placeholder="所属模块" clearable size="small">
            <el-option
              v-for="item in moduleOptions"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属方案" prop="state" v-if="queryParams.isPlan == 2">
          <el-select v-model="queryParams.state" placeholder="所属方案" clearable size="small">
            <el-option
              v-for="item in planList"
              :key="item.planId"
              :label="item.planName"
              :value="item.planId"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="content">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            size="mini"
            @click="cancelFilter"
            icon="el-icon-delete"
            :disabled="multiple"
          >批量过滤
          </el-button>
        </el-col>
      </el-row>

      <el-table
        v-loading="loading" :data="wordList" @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" type="index" align="center" :index="indexMethod"></el-table-column>
        <el-table-column label="过滤内容" prop="word" align="center"></el-table-column>
        <el-table-column label="所属模块" prop="isPlan" align="center">
          <template slot-scope="scope">
            {{ scope.row.isPlan == 1 ? '数据概览热词云' : '方案热词云' }}
          </template>
        </el-table-column>
        <el-table-column label="创建时间" prop="createTime" align="center"></el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="cancelFilter(scope.row)"
              style="color:red"
            >取消过滤
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                  @pagination="pagination"/>
    </div>
  </div>
</template>

<script>
import {getHotWordPage, hideHotWord} from "@/api/system/filterWord.js";
import {getPlanType} from '@/api/system/filterMsg.js'
import {getInfo} from '@/api/login'

export default {
  name: "FilterMessage",
  data() {
    return {
      loading: false,
      wordList: [],
      moduleOptions: [{name: '数据概览热词云', value: 1}, {name: '方案热词云', value: 2}],
      planList: [],
      // 查询参数
      queryParams: {
        word: undefined,
        isPlan: undefined,
        pageNum: 1,
        pageSize: 10
      },
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      total: 0,
    };
  },
  created() {
    this.getList();
    getInfo().then(res => {
      const userId = res.user.userId
      getPlanType({userId: userId, isOwner: true, pageSize: 0, pageNum: 1}).then(res => {
        this.planList = res.rows
      })
    })

  },
  methods: {
    indexMethod(index) {
      return this.queryParams.pageNum * this.queryParams.pageSize - this.queryParams.pageSize + (index + 1)
    },
    // 分页查询
    pagination(page) {
      this.queryParams.pageNum = page.page
      this.queryParams.pageSize = page.limit
      this.getList()
    },
    /** 查询菜单列表 */
    getList() {
      this.loading = true;
      getHotWordPage(this.queryParams).then(response => {
        this.wordList = response.rows
        this.total = Number(response.total)
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryParams");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 取消过滤操作 */
    cancelFilter(row) {
      let wordIds = row.id || this.ids;
      if (Array.isArray(wordIds)) {
        wordIds = wordIds.join(',')
      }
      this.$confirm('是否确认过滤所选中的数据?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return hideHotWord({ids: wordIds});
      }).then(() => {
        this.getList();
        this.msgSuccess("操作成功");
      })
    }
  }
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #F4F7FB;
  height: calc(100vh - 80px);

  .navbar {
    background: #fff;
    padding: 20px;
    margin-bottom: 20px;
  }

  .content {
    background: #fff;
    padding: 20px;
  }
}
</style>
