<template>
    <div class="vsSingUp">
        <div class="title">扫码签到</div>
        <template v-if="!sended">
            <div class="formBox">
                <el-form ref="form" :model="queryForm" label-width="80px" :rules="rules">
                    <el-form-item label="姓名：" prop="userName">
                        <el-input v-model.trim="queryForm.userName" placeholder="请输入您的姓名" style="width: 100%" />
                    </el-form-item>
                    <el-form-item label="手机号：" prop="phone">
                        <el-input v-model.trim="queryForm.phone" placeholder="请输入您的手机号" style="width: 100%" />
                    </el-form-item>
                </el-form>
            </div>
            <div class="buttonGroup">
                <el-button type="primary" @click="onSubmit" :loading="loading">提交</el-button>
                <!-- <el-button style="margin-left: 30px;" @click="handelClose">关闭</el-button> -->
            </div>
        </template>
        <template v-else>
            <div class="sended">
                <img src="@/assets/images/simulatedVS/singUpSuccess.png" alt="" />
                <div>签到成功！</div>
            </div>
        </template>
    </div>
</template>

<script>
import { drillSingUpApi } from "@/api/simulatedVS/index.js";

export default {
    data() {
        return {
            loading: false,
            queryForm: {
                userName: '',
                phone: '',
            },
            rules: {
                userName: [
                    { required: true, message: '请输入姓名', trigger: 'change' },
                ],
                phone: [
                    { required: false, message: '请输入手机号码', trigger: 'blur' },
                    { validator: this.validatePhoneNumber, trigger: 'change' }
                ],
            },
            sended: false,// 是否已提交
        };
    },
    computed: {
        drillTaskId() {
            return this.$route.query.drillTaskId
        },
    },
    created() {
        document.title = '扫码签到'
    },
    methods: {
        onSubmit() {
            this.$refs.form.validate(valid => {
                if (valid) {
                    let params = {
                        drillTaskId: this.drillTaskId,
                        ...this.queryForm,
                    };

                    console.log('params', params)
                    this.loading = true;
                    drillSingUpApi(params).then((res) => {
                        this.$message.success('签到成功')
                        this.loading = false;
                        this.sended = true;
                    }).catch((err) => {
                        this.loading = false;
                    })
                }
            })
        },

        // 自定义手机号码验证函数
        validatePhoneNumber(rule, value, callback) {
            // 检查值是否为空，如果是，则直接通过验证
            if (!value) {
                callback();
            } else {
                const phonePattern = /^1[1-9]\d{9}$/;
                // 如果值不为空，则继续进行手机号码格式验证
                if (!phonePattern.test(value)) {
                    callback(new Error('请输入正确的11位手机号码'));
                } else {
                    callback();
                }
            }
        },
        handelClose() {
            window.close();
        },

    },
};
</script>

<style lang="scss" scoped>
.vsSingUp {
    width: 100%;
    height: 100%;
    position: relative;

    .title {
        font-size: 20px;
        font-weight: 600;
        padding: 20px;
        background-color: #EDEDED;
        text-align: center;
    }
    .formBox{
        padding: 40px 20px;
    }

    .buttonGroup {
        width: 100%;
        position: absolute;
        bottom: 20px;
        padding-top:20px;
        display: flex;
        justify-content: center;
        align-items: center;

        background: #FFFFFF;
        box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.05);
    }
    .sended{
        img{
            width: 100%;
            padding: 30px 30px 20px 30px;
        }
        div{
            font-size: 22px;
            font-weight: 600;
            text-align: center;
        }
    }
}
</style>