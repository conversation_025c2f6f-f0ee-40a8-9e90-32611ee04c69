<template>
    <div class="article-detail-wrap">
        <div class="detail-left" v-loading="loading">
            <div style="display: flex;justify-content: center;">
                <!-- <el-popover placement="top-start" trigger="hover">
                    <div v-html="detailParams.sector" class="detail-popover"></div>
                    <div class="detail-title" slot="reference" v-html="detailParams.sector">
                    </div>
                </el-popover> -->
                <div v-if="isWeb" class="detail-title" slot="reference" v-html="detailParams.sector">
                </div>
                <div v-else class="detail-title" slot="reference" v-html="detailParams.nickname"></div>
            </div>
            <div class="detail-operate">
                <!-- <div class="operate-content" @click="goOrigin(detailParams.url)">
                    <img src="@/assets/images/goOrigin.png" alt="">
                    查看原文
                </div>
                <div class="operate-content" @click="copyText(detailParams.url,true)">
                    <img src="@/assets/images/copyLink.png" alt="">
                    拷贝地址
                </div> -->
                <div class="operate-content" @click="goHomeWeb(detailParams)">
                    <img src="@/assets/images/goOrigin.png" alt="">
                    前往主页
                </div>
            </div>
            <div class="formH3">
                <div>
                    <div class="formH3Point"></div>
                    <div>账号/网站基本信息</div>
                </div>
            </div>
            <div class="detail-content">
                <div class="baseInfo">
                    <img v-if="detailParams.avatar" class="baseInfo-img"
                        :src="baseUrl+'/common/url/image?url='+ encodeURIComponent(detailParams.avatar)" alt="" />
                    <img v-else class="baseInfo-img" :src="transImage($route.query.type)" alt="">
                    <!-- 网站 -->
                    <div v-if="isWeb" class="baseInfo-content">
                        <div class="baseInfo-content-item">
                            <div class="baseInfo-content-item-title">平台类型</div>
                            <div class="baseInfo-content-item-info">网站</div>
                        </div>
                        <div class="baseInfo-content-item">
                            <div class="baseInfo-content-item-title">网站名称</div>
                            <div class="baseInfo-content-item-info">{{ detailParams.sector||'暂无' }}</div>
                        </div>
                        <div class="baseInfo-content-item">
                            <div class="baseInfo-content-item-title">域名</div>
                            <div class="baseInfo-content-item-info">{{ detailParams.domain||'暂无' }}</div>
                        </div>
                    </div>
                    <!-- 账户 -->
                    <div v-else class="baseInfo-content">
                        <div class="baseInfo-content-item">
                            <div class="baseInfo-content-item-title">平台类型</div>
                            <div class="baseInfo-content-item-info">
                                {{ detailParams.sector||'暂无' }}
                            </div>
                        </div>
                        <div class="baseInfo-content-item">
                            <div class="baseInfo-content-item-title">账号信息</div>
                            <div class="baseInfo-content-item-info">
                                {{ `${detailParams.accountId||''}
                                ${detailParams.province!='其他'?(detailParams.province||''):''}
                                ${detailParams.city!='其他'?(detailParams.city||''):''}
                                ${detailParams.authorsex==1?'男':detailParams.authorsex==1?'女':''}`}}
                            </div>
                        </div>
                        <div class="baseInfo-content-item">
                            <div class="baseInfo-content-item-title">账号简介</div>
                            <div class="baseInfo-content-item-info">
                                {{ detailParams.desc&&detailParams.desc!='null'?detailParams.desc:'暂无' }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="formH3">
                <div class="formH3Point"></div>
                <div>近期相关发文</div>
            </div>
            <div class="similar-info">
                <el-table :data="tableData" border style="width: 100%" v-loading="tableLoading">
                    <el-table-column prop="title" align="center" show-overflow-tooltip label="标题">
                        <template slot-scope="scope">
                            <a class="link" @click="goOrigin(scope.row.url)" type="primary">{{scope.row.title}}</a>
                        </template>
                    </el-table-column>
                    <el-table-column prop="updateTime" align="center" label="发布时间"></el-table-column>
                    <el-table-column prop="host" align="center" show-overflow-tooltip label="来源"></el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>
<script>
import {
    accountDetailApi,
    accountArticleListApi
} from "@/api/search/index";
import { copyText, replaceHtml, transImage } from "@/utils/index"
import { goHomepage } from "../../utils";
import {jumpLinkApi} from "@/api/menu";

export default {
    data() {
        return {
            transImage,
            baseUrl: process.env.VUE_APP_BASE_API,
            text: '',
            loading: false,
            tableData: [],
            detailParams: {},
            tableLoading: false,
        }
    },
    computed: {
        //是否为网站
        isWeb() {
            return this.$route.query.type == 0 || this.$route.query.type == 1 || this.$route.query.type == 17 || this.$route.query.type == 25
        },
    },
    async created() {
        await this.queryInfo()

    },
    methods: {
        // 跳转详情页
        goOrigin(url) {
            window.open(url, '_blank')
        },
        copyText(text, tips) {
            copyText(text, tips)
        },
        async queryInfo() {
            this.querySimilar()
            try {
                this.loading = true
                const { type, author, url, host } = this.$route.query
                let res = await accountDetailApi({ type, author, url, host })
                this.loading = false
                this.detailParams = res.data
            } finally {
                this.loading = false
            }
        },
        async querySimilar() {
            try {
                this.tableLoading = true
                const { type, author, url, host } = this.$route.query
                let res = await accountArticleListApi({
                    type,
                    author,
                    url,
                    host,
                    pageNum: 1,
                    pageSize: 10
                })
                this.tableLoading = false
                this.tableData = res.rows
            } finally {
                this.tableLoading = false
            }
        },
        goHomeWeb(row) {
            let params = {
                type: row.type,
                url: row.url,
                bizId: this.$route.query.bizId,
                author: this.$route.query.author,
                authorId: this.$route.query.authorId
            }
            jumpLinkApi(params).then(res => {
                console.log('res', res)
                if (res.code == 200) {
                    window.open(res.data, '_blank')
                }
            })
        },
    }
}
</script>
<style>
.detail-popover em {
    color: red;
    font-style: normal;
}

.detail-title em {
    color: red;
    font-style: normal;
}

.detail-content em {
    color: red;
    font-style: normal;
}

.detail-content span {
    color: blue;
    font-style: normal;
}

.detail-content em.red {
    background: #f1bf41;
}

.detail-content span.blue {
    background: #f1bf41;
}
</style>
<style lang="scss" scoped>
.detail-operate {
    display: flex;
    align-items: center;
    justify-content: center;

    .operate-content {
        display: flex;
        align-items: center;
        margin-right: 20px;
        font-size: 12px;
        line-height: 17px;
        color: #666666;
        cursor: pointer;

        img {
            width: 12px;
            margin-right: 5px;
            vertical-align: text-bottom;
        }
    }
}

.bdl {
    border-left: 1px solid #efefef;
}

.cp {
    cursor: pointer;
}

.cloud-word {
    height: 250px;

    .noneData {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        img {
            height: 80px;
            margin-bottom: 10px;
        }
    }
}

.article-detail-wrap {
    margin: 20px 40px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .detail-left {
        width: calc(100% - 0px);
        background: #fff;
        padding: 30px 40px;

        .detail-title {
            // margin-bottom: 38px;
            margin-bottom: 20px;
            font-weight: 500;
            font-size: 24px;
            color: #333333;
            // line-height: 36px;
            text-align: center;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
    }

}

.site-table {
    margin: 20px 0 40px 0;
    border-top: 1px solid #efefef;

    .site-wrap {
        display: flex;
        border-bottom: 1px solid #efefef;
    }

    .row {
        min-height: 40px;
        padding: 8px;
        font-size: 14px;
        line-height: 20px;
    }

    .el-row {
        border-bottom: 1px solid #efefef;

        &:last-child {
            border-bottom: none;
        }

    }

    .name-row {
        width: 15%;
        background-color: #f9f9fc;
        text-align: center;
        color: #666;
        border-right: 1px solid #efefef;
    }

    .data-row {
        border-right: 1px solid #efefef;
        width: 35%;
        text-align: center;
        color: #333;

        span {
            cursor: pointer;

            &.active {
                color: red;
            }
        }
    }

    .data-url {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
}

.form-devide {
    justify-content: space-between;
}

.formH3 {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 14px;
    border-bottom: 1px solid #EEE;
    font-size: 16px;
    color: #333;

    div {
        display: inline-block;
        vertical-align: middle;
    }

    .word-title {
        margin-right: 6px;
        width: 20px;
    }

    .formH3Point {
        width: 8px;
        height: 8px;
        margin-right: 5px;
        background: #247CFF;
        border-radius: 50%;
    }
}

.detail-content {
    margin-bottom: 50px;
    font-weight: 400;
    font-size: 14px;
    color: #333333;
    line-height: 28px;
    max-height: 400px;
    overflow-y: auto;

    .baseInfo {
        margin-left: 40px;
        display: flex;
        justify-content: flex-start;

        .baseInfo-img {
            width: 50px;
            height: 50px;
            margin-right: 30px;
            border-radius: 5px;
        }

        .baseInfo-content {
            flex: 1;

            .baseInfo-content-item {
                display: flex;

                .baseInfo-content-item-title {
                    width: 4em;
                    text-align-last: justify;
                    color: #CFD2D1;
                    margin-right: 20px;
                }

                .baseInfo-content-item-info {
                    flex: 1;

                }
            }
        }
    }
}

.similar-info {
    margin-top: 40px;

    .link {
        color: #46a6ff;
        padding: 3px 0;

        &:hover {
            border-bottom: 1px solid #1890ff;
        }
    }
}

.footIcon {
    cursor: pointer;
}
</style>