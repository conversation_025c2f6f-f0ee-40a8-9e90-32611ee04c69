<template>
  <!-- 弹窗 -->
  <div class="form">
    <div class="module">
      <el-tabs v-model="searchMode" type="card" @change="changeMode">
        <el-tab-pane :key="item.value" v-for="item in modes" :name="item.value" :label="item.label">
        </el-tab-pane>
      </el-tabs>
    </div>
    <el-form ref="form" class="formMain" :model="taskForm" label-width="160px" :rules="rules">
      <el-form-item label="方案分类：" prop="typeId" v-show="false">
        <SelectAdd class="m-2" ref="selectAdd" :actIndustryIds="typeOptions" v-model="taskForm.typeName"
                   @saveActive="saveActive" style="width: 100%" @chooseActive="chooseActive"
                   :typeDisabled="taskForm.historyFlag=='1'"/>
      </el-form-item>
      <el-form-item label="设置方案名：" prop="planName">
        <el-input v-model.trim="taskForm.planName" :disabled="taskForm.historyFlag=='1'" placeholder="请输入方案名"
                  style="width: 100%"/>
        <div class="formTip">
          *方案名的字数请尽量控制在20个字以内
        </div>

      </el-form-item>

      <div class="formSetPart2">
        <div class="formH2">
          <span class="h2Num">1. </span>
          精准地域设置
        </div>

        <el-form-item v-if="searchMode=='0'" label="精准地域：" prop="area">
          <el-cascader v-model="taskForm.area" :append-to-body="false" :disabled="taskForm.historyFlag=='1'"
                       :options="sourceAreaData"
                       :props="optionProps" filterable clearable :show-all-levels="false" style="width: 100%;"/>
          <div class="formTip">
            输入并选择您需要的监测方案所在地区，最多支持10个地名，系统将通过您输入的地域进行研判，去除跟该地域无关的信息。
          </div>
        </el-form-item>

        <el-form-item v-else label="精准地域：" prop="highArea">
          <el-cascader v-model="taskForm.highArea" :disabled="taskForm.historyFlag=='1'"
                       :options="sourceAreaData" :props="optionProps" filterable clearable :show-all-levels="false"
                       style="width: 100%;"/>
          <div class="formTip">
            输入并选择您需要的监测方案所在地区，最多支持10个地名，系统将通过您输入的地域进行研判，去除跟该地域无关的信息。
          </div>
        </el-form-item>

      </div>

      <div class="formSetPart2">
        <div class="formH2">
          <span class="h2Num">2. </span>
          监测方案设置
        </div>

        <div class="formSetPart3">
          <div class="formH3">
            <div class="formH3Point"></div>
            <div>监测关键词设置</div>
          </div>

          <template v-if="searchMode=='0'">
            <el-form-item label="地域关键词：" prop="kw1">
              <el-tooltip class="item" effect="light" placement="top-start"
                          popper-class="addProgram-tip-class">
                <template #content>
                  <div class="explain-words" v-html="tipText"></div>
                </template>
                <!-- <i class="el-icon-question tipIconGrey"></i> -->
                <img class="tipIconGrey" src="@/assets/images/icon-question.png" alt="">
              </el-tooltip>
              <el-input v-model="taskForm.kw1" :disabled="taskForm.historyFlag=='1'" type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        placeholder="请输入地域关键词，多个以空格隔开，逻辑关系为“或”"
                        style="width: 100%"/>
              <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                   @click="clearTextarea('kw1')">
                <img src="@/assets/images/clearWords.png" alt="">
                清空
              </div>
              <div class="el-textarea__icon" @click="copyText(taskForm.kw1)">
                <img src="@/assets/images/copyWords.png" alt="">
                复制
              </div>
              <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                   @click="openLexiconDialog('kw1')">
                <img src="@/assets/images/lexicon.png" alt="">
                词库
              </div>
            </el-form-item>
            <el-form-item label="主体关键词：" prop="kw2">
              <el-tooltip class="item" effect="light" placement="top-start"
                          popper-class="addProgram-tip-class">
                <template #content>
                  <div class="explain-words" v-html="tipText"></div>
                </template>
                <!-- <i class="el-icon-question tipIconGrey"></i> -->
                <img class="tipIconGrey" src="@/assets/images/icon-question.png" alt="">
              </el-tooltip>
              <el-input v-model="taskForm.kw2" :disabled="taskForm.historyFlag=='1'" type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        placeholder="请输入主体关键词，多个以空格隔开，逻辑关系为“或”"
                        style="width: 100%"/>
              <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                   @click="clearTextarea('kw2')">
                <img src="@/assets/images/clearWords.png" alt="">
                清空
              </div>
              <div class="el-textarea__icon" @click="copyText(taskForm.kw2)">
                <img src="@/assets/images/copyWords.png" alt="">
                复制
              </div>
              <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                   @click="openLexiconDialog('kw2')">
                <img src="@/assets/images/lexicon.png" alt="">
                词库
              </div>
            </el-form-item>
            <el-form-item label="事件关键词：" prop="kw3">
              <el-tooltip class="item" effect="light" placement="top-start"
                          popper-class="addProgram-tip-class">
                <template #content>
                  <div class="explain-words" v-html="tipText"></div>
                </template>
                <!-- <i class="el-icon-question tipIconGrey"></i> -->
                <img class="tipIconGrey" src="@/assets/images/icon-question.png" alt="">
              </el-tooltip>
              <el-input v-model="taskForm.kw3" :disabled="taskForm.historyFlag=='1'" type="textarea"
                        :autosize="{ minRows: 2, maxRows: 4 }"
                        placeholder="请输入事件关键词，多个以空格隔开，逻辑关系为“或”"
                        style="width: 100%"/>
              <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                   @click="clearTextarea('kw3')">
                <img src="@/assets/images/clearWords.png" alt="">
                清空
              </div>
              <div class="el-textarea__icon" @click="copyText(taskForm.kw3)">
                <img src="@/assets/images/copyWords.png" alt="">
                复制
              </div>
              <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                   @click="openLexiconDialog('kw3')">
                <img src="@/assets/images/lexicon.png" alt="">
                词库
              </div>
            </el-form-item>
          </template>
          <!-- 高级模式 -->
          <el-form-item v-else label="监测关键词：" prop="highMonitorWord">
            <el-tooltip class="item" effect="light" placement="top-start"
                        popper-class="addProgram-tip-class">
              <template #content>
                <div class="explain-words" v-html="explainWords"></div>
              </template>
              <img class="tipIconGrey" src="@/assets/images/icon-question.png" alt="">
            </el-tooltip>
            <el-input ref="highMonitorWordRef" v-model="taskForm.highMonitorWord" type="textarea"
                      placeholder="请输入监测关键词"
                      style="width: 100%"/>
            <div class="el-textarea__icon" @click="clearTextarea('highMonitorWord')">
              <img src="@/assets/images/clearWords.png" alt="">
              清空
            </div>
            <div class="el-textarea__icon" @click="copyText(taskForm.highMonitorWord)">
              <img src="@/assets/images/copyWords.png" alt="">
              复制
            </div>
            <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                 @click="openLexiconDialog('highMonitorWord')">
              <img src="@/assets/images/lexicon.png" alt="">
              词库
            </div>
            <div style="width: 100%;margin-top: 5px;">
              <el-button v-for="button in buttons" :key="button.text" size="small" :type="button.type"
                         @click="toggleButton('highMonitorWordRef','highMonitorWord',button)">{{ button.text }}
              </el-button>
            </div>
          </el-form-item>

          <div class="formH3">
            <div class="formH3Point"></div>
            <div>排除关键词设置</div>
          </div>

          <el-form-item v-if="searchMode=='0'" label="排除关键词：" prop="excludeWord">
            <el-tooltip class="item" effect="light" placement="top-start"
                        popper-class="addProgram-tip-class">
              <template #content>
                <div class="explain-words" v-html="tipText"></div>
              </template>
              <img class="tipIconGrey" src="@/assets/images/icon-question.png" alt="">
            </el-tooltip>
            <el-input v-model="taskForm.excludeWord" :disabled="taskForm.historyFlag=='1'" type="textarea"
                      :autosize="{ minRows: 2, maxRows: 4 }"
                      placeholder="请输入排除关键词，多个以空格隔开，逻辑关系为“或”"
                      style="width: 100%"/>
            <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                 @click="clearTextarea('excludeWord')">
              <img src="@/assets/images/clearWords.png" alt="">
              清空
            </div>
            <div class="el-textarea__icon" @click="copyText(taskForm.excludeWord)">
              <img src="@/assets/images/copyWords.png" alt="">
              复制
            </div>
            <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                 @click="openLexiconDialog('excludeWord')">
              <img src="@/assets/images/lexicon.png" alt="">
              词库
            </div>
          </el-form-item>

          <el-form-item v-else label="排除关键词：" prop="highExcludeWord">
            <el-tooltip class="item" effect="light" placement="top-start"
                        popper-class="addProgram-tip-class">
              <template #content>
                <div class="explain-words" v-html="tipText"></div>
              </template>
              <img class="tipIconGrey" src="@/assets/images/icon-question.png" alt="">
            </el-tooltip>
            <el-input v-model="taskForm.highExcludeWord" :disabled="taskForm.historyFlag=='1'"
                      type="textarea" :autosize="{ minRows: 2, maxRows: 4 }"
                      placeholder="请输入排除关键词，多个以空格隔开，逻辑关系为“或”" style="width: 100%"/>
            <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                 @click="clearTextarea('highExcludeWord')">
              <img src="@/assets/images/clearWords.png" alt="">
              清空
            </div>
            <div class="el-textarea__icon" @click="copyText(taskForm.highExcludeWord)">
              <img src="@/assets/images/copyWords.png" alt="">
              复制
            </div>
            <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
                 @click="openLexiconDialog('highExcludeWord')">
              <img src="@/assets/images/lexicon.png" alt="">
              词库
            </div>
          </el-form-item>
        </div>
      </div>

      <!-- <div class="formSetPart2">
        <div class="formH2">
          <span class="h2Num">3. </span>
          热搜词设置
        </div>
        <el-form-item label="热搜词：" prop="hotKw">
          <el-tooltip class="item" effect="light" placement="top-start"
                      popper-class="addProgram-tip-class">
            <template #content>
              <div class="explain-words"
                   v-html="'热搜词设置后统计分析会出现相关的热搜信息，不设置则统计分析不会出现相关的热搜信息'"></div>
            </template>
            <img class="tipIconGrey" style="left: -95px;" src="@/assets/images/icon-question.png" alt="">
          </el-tooltip>
          <el-input v-model="taskForm.hotKw" :disabled="taskForm.historyFlag=='1'" type="textarea"
                    :autosize="{ minRows: 2, maxRows: 4 }" placeholder="请输入热搜词，多个以空格隔开，逻辑关系为“或”"
                    style="width: 100%"/>
          <div v-show="taskForm.historyFlag!='1'" class="el-textarea__icon"
               @click="clearTextarea('hotKw')">
            <img src="@/assets/images/clearWords.png" alt="">
            清空
          </div>
          <div class="el-textarea__icon" @click="copyText(taskForm.hotKw)">
            <img src="@/assets/images/copyWords.png" alt="">
            复制
          </div>
        </el-form-item>
      </div> -->
    </el-form>
    <!-- <div style="text-align: center;">
        <span class="dialog-footer">
            <el-button type="primary" :loading="btnLoading" @click="submitForm">确定</el-button>
            <el-button @click="emits('tabBack')">取消</el-button>
        </span>
    </div> -->
    <lexiconDialog :visible.sync="lexiconDialogVisible" @visibleChange="lexiconVisibleChange"
                   :lexiconType="lexiconType" @submit="submitWords"></lexiconDialog>
  </div>

</template>

<script>
import {sourceAreaApi, getTypeLists, addTaskType} from "@/api/publicOpinionMonitor/index.js";
import lexiconDialog from '@/views/publicOpinionMonitor/components/lexiconDialog/index.vue'

export default {
  components: {
    lexiconDialog
  },
  data() {
    return {
      modes: [{
        label: '快速配置',
        value: '0'
      }, {
        label: '高级配置',
        value: '1'
      }],
      searchMode: '0',

      taskForm: {
        searchMode: '0',
        typeId: '',
        planName: '',
        area: [],
        kw1: '',
        kw2: '',
        kw3: '',
        excludeWord: '',
        typeName: '',
        highArea: [],
        highMonitorWord: '',
        highExcludeWord: '',
        hotKw: '',
      },
      rules: {
        typeId: [{required: true, message: '请选择方案分类', trigger: 'blur'}],
        planName: [{required: true, message: '请输入方案名', trigger: 'blur'}, {
          pattern: /^(\S|\S.*\S)$/,
          message: '首尾字符不能为空格',
          trigger: 'blur'
        }, {max: 20, message: "方案名请控制在20个字以内", trigger: "change"}],
        area: [{validator: this.checkMaxAreaIds, message: '最多支持10个地名', trigger: 'change'}],
        highArea: [{validator: this.checkMaxHighAreaIds, message: '最多支持10个地名', trigger: 'change'}],
      },
      typeOptions: [],
      btnLoading: false,
      buttons: [
        {type: 'primary', text: '+'},
        {type: 'primary', text: '|'},
        {type: 'primary', text: '('},
        {type: 'primary', text: ')'}
      ],
      optionProps: {
        multiple: true,
        value: 'id',
        label: 'name',
        children: 'children',
        emitPath: false,
        checkStrictly: true
      },
      tipText: '<div>1：地域、主体、事件 三类都可输入多个关键词，关键词之间用空格分开。三个类型都可以为空。<br>2：每一类的多个关键词之间默认为或(或者)”的关系。<br>3：地域、主体、事件三个类型之间是“与(并且)”的关系。例如：地域配置了“上海”，主体配置了“周某”，事件配置了“撞车”，只有同时满足这3个条件的文章才会被监测到。<br>4：命中排除关键词的信息，将不进入系统，关键词之间用空格分开。</div> ',
      explainWords: '<div>1：“+”表示“并且”,“|”表示“或”<br />2：什么情况下用“|”：如想关注北京或上海或广州的新闻，表达式为“北京|上海|广州”，表示文章中出现 “北京”、“上海”、“广州”任意一个城市就能监测到。<br />3：什么情况下用“+”：如想关注北京车牌摇号的新闻，表达式为“北京+车牌摇号”，表示文章中同时出现 “北京”和“车牌摇号”两个关键词才能监测到。<br />4：什么情况下同时用到“+”、“|”：如想关注上海世博会的新闻，由于“世博会”又可能被称为“世界博览会”，表达式为 “上海+(世博会|世界博览会)”，表示文章中出现“上海”，同时出现“世博会”或者 “世界博览会”中任意一个词，就能监测到；<br />5：什么情况下用到排除关键词：如想关注上海、北京、广州的新闻，但又不想看到内容中有“三室一厅”、“二室 一厅”这种关键词的广告，可以使用排除关键词的方式。 匹配关键词表达式“北京|上海|广州” 排除关键词表达式“三室一厅|二室一厅”<br />6：关键词内包含”+“需用”@@“代替搜索，例如“互联网+”需用“互联网@@“代替搜索<br />7：所有符号均为英文符号</div>',
      sourceAreaData: [],

      lexiconDialogVisible: false,
      lexiconType: '',
    }
  },
  watch: {
    searchMode(newValue) {
      this.taskForm.searchMode = newValue
    }
  },
  mounted() {
    this.sourceArea()
    this.TypeLists()
  },
  methods: {
    // 回显数据字典 name-->id
    selectDictId(datas, value) {
      var actions = [];
      Object.keys(datas).some((key) => {
        if (datas[key].typeName == value) {
          actions.push(datas[key].typeId);
          return true;
        }
      })
      return actions.join('');
    },
    //获取信源区域字典表
    async sourceArea() {
      const res = await sourceAreaApi('0/2')
      this.sourceAreaData = res.data
      this.sourceAreaData.push({name: '其他', id: '999999'})
    },
    chooseActive(item, index) {
      this.$forceUpdate();
      this.typeOptions.map((itema) => {
        if (itema == item) {
          itema.tag = true
        } else {
          itema.tag = false
        }
      })
      this.taskForm.typeId = this.selectDictId(this.typeOptions, this.taskForm.typeName)
    },
    // 添加分类
    saveActive(addActive) {
      if (addActive) {
        addTaskType({typeName: addActive}).then((res) => {
          if (res.code == 200) {
            this.$message.success(res.msg);
            this.$refs.selectAdd.resetAddActive();
            getTypeLists().then((res) => {
              this.typeOptions = res.data
              this.typeOptions.map((item) => {
                if (item.typeName == this.taskForm.typeName) {
                  this.$set(item, 'tag', true)
                } else {
                  this.$set(item, 'tag', false)
                }
              })
            })
          }
        })
      } else {
        this.msgInfo('请输入分类名')
      }

    },
    // 获取任务分类
    TypeLists() {
      getTypeLists().then((res) => {
        this.typeOptions = res.data
        this.typeOptions.map((item) => {
          if (item.typeName == this.taskForm.typeName) {
            this.$set(item, 'tag', true)
          } else {
            this.$set(item, 'tag', false)
          }
        })
      })
    },
    setTypeName() {
      this.typeOptions.map((item) => {
        if (item.typeId == this.taskForm.typeId || item.typeName == this.taskForm.typeName) {
          this.taskForm.typeName = item.typeName
          this.$set(item, 'tag', true)
        } else {
          this.$set(item, 'tag', false)
        }
      })
    },
    changeMode(value) {
      console.log(value)
    },
    // 限制最多支持10个地名
    checkMaxAreaIds(value) {
      if (this.taskForm.area instanceof Array && this.taskForm.area.length > 10) {
        return Promise.reject(new Error(value.message));
      }
      return Promise.resolve();
    },
    // 限制最多支持10个地名(高级配置)
    checkMaxHighAreaIds(value) {
      if (this.taskForm.highArea instanceof Array && this.taskForm.highArea.length > 10) {
        return Promise.reject(new Error(value.message));
      }
      return Promise.resolve();
    },
    // 点击+-（）按钮
    toggleButton(refName, wordName, val) {
      if (!refName || !wordName) return
      let value = val.text

      let elInput = this.$refs[refName].$el.firstElementChild;// el-input中的input元素
      let txt = elInput.value;// el-input的值

      // 做插入前做长度校验（如果有这个需要的话）
      // if (txt.length + value.length > 64) {
      //     return;
      // }

      let startPos = elInput.selectionStart;// 选区开始位置
      let endPos = elInput.selectionEnd;// 选区结束位置

      if (startPos === undefined || endPos === undefined) return;

      // 将文本插入光标位置
      this.taskForm[wordName] = txt.substring(0, startPos) + value + txt.substring(endPos);
      // 将光标移至文本末尾
      elInput.focus();
      this.$nextTick(() => {
        elInput.selectionStart = startPos + value.length;
        elInput.selectionEnd = startPos + value.length;
      })
    },
    // 清空文本域
    clearTextarea(item) {
      this.taskForm[item] = ''
    },
    // 提交按钮
    submitForm() {
      this.$refs['form'].validate((valid) => {


        if (valid) {
          this.$emit('submit', this.taskForm)
          return
          this.btnLoading = true
          this.taskForm.area = this.taskForm.area.join(',')
          this.taskForm.searchMode = this.searchMode
          if (this.taskForm.searchMode != '0') {
            this.taskForm.kw2 = ''
            this.taskForm.kw2 = ''
          }
          // ElMessageBox.confirm('您的配置方案发生变更，系统将清空历史数据，重新扫描', '提示', {
          //     confirmButtonText: '确定',
          //     cancelButtonText: '取消',
          //     type: 'warning'
          // }).then(() => {
          //     putTaskCustom(this.taskForm).then((res) => {
          //         this.btnLoading = false
          //         emits('getLists')
          //         emits('tabBack')
          //         ElMessage.success('修改成功')
          //     }).catch((err) => {
          //         this.btnLoading = false
          //     })
          // })


        }
      })
    },


    // 重置表单
    reset() {
      this.taskForm = {
        // 表单数据
        searchMode: '0',
        typeId: '',
        planName: '',
        area: [],
        kw1: '',
        kw2: '',
        kw3: '',
        excludeWord: '',
        typeName: '',
        highArea: [],
        highMonitorWord: '',
        highExcludeWord: '',
        hotKw: '',
      }
      this.TypeLists()
      this.$nextTick(() => {
        this.$refs['form'].resetFields()
        this.taskForm.typeId = '-2'
        this.taskForm.typeName = '重点方案'
      })
    },


    // 复制
    copyText(content) {
      if (!content) {
        this.$message.warning('无复制内容')
        return
      }
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(content).then(() => {
          this.$message.success('复制成功')
        }).catch((error) => {
          this.$message.error('复制失败')
        })
      } else {
        // 创建text area
        const textArea = document.createElement('textarea')
        textArea.value = content
        // 使text area不在viewport，同时设置不可见
        textArea.style.position = 'absolute'
        textArea.style.opacity = 0
        textArea.style.left = '-999999px'
        textArea.style.top = '-999999px'
        document.body.appendChild(textArea)
        textArea.focus()
        textArea.select()
        return new Promise((res, rej) => {
          // 执行复制命令并移除文本框
          document.execCommand('copy') ? res() : rej()
          textArea.remove()
          this.$message.success('复制成功')
        })
      }
    },
    lexiconVisibleChange(val) {
      this.lexiconDialogVisible = val
    },

    openLexiconDialog(key) {
      this.lexiconDialogVisible = true
      // if (key == 'kw1') {
      //     this.lexiconType = 'kw1'
      // }
      this.lexiconType = key
    },

    submitWords(type, wordsList) {
      // console.log(type, wordsList);
      if (wordsList.length == 0 || !type) return

      //无该字段时，赋初始值为空字符串
      if (!this.taskForm[type]) {
        this.taskForm[type] = ''
      }

      if (type == 'highMonitorWord') {
        //有值且最后一位不为+时，添加+作为间隔
        if (this.taskForm[type] && this.taskForm[type]?.length > 0 && this.taskForm[type]?.slice(-1) !== '+') {
          this.taskForm[type] += '+'
        }
        this.taskForm[type] += `(${wordsList.join('|')})`
      } else {
        //有值且最后一位不为空格时，添加空格作为间隔
        if (this.taskForm[type] && this.taskForm[type]?.length > 0 && this.taskForm[type]?.slice(-1) !== ' ') {
          this.taskForm[type] += ' '
        }
        this.taskForm[type] += wordsList.join(' ')
      }
    },

    // highMonitorWordBlur(e){
    //     this.highMonitorWordIndex = e.srcElement.selectionStart
    // },
  }
}
</script>

<style lang="scss" scoped>
.tipIconGrey {
  position: absolute;
  left: -123px;
  top: 10px;
  cursor: pointer;
  color: #fff;
  font-size: 16px;
}

.el-textarea__icon {
  color: #247CFF;
  text-align: end;
  cursor: pointer;
  width: fit-content;
  float: right;
  margin-left: 10px;

  img {
    height: 15px;
    vertical-align: text-bottom;
  }
}

::v-deep .module {
  .el-tabs__item {
    background-color: #eeeeee;
  }

  .is-active {
    background-color: #fff;
  }
}

.form {
  padding: 0px 40px;

  .formMain {
    ::v-deep .el-form-item__label {
      font-weight: 400;
    }

    ::v-deep .el-textarea__inner {
      font-family: Arial;
    }

    .formTip {
      font-weight: 400;
      font-size: 12px;
      color: #247CFF;
      line-height: 14px;
      margin-top: 6px;
    }

    .formSetPart2 {
      margin-left: 50px;
    }

    .formH2 {
      font-size: 18px;
      line-height: 25px;
      margin-bottom: 15px;

      .h2Num {

        color: #247CFF;
      }
    }

    .formH3 {
      font-size: 16px;
      color: #666666;
      margin-bottom: 10px;

      div {
        display: inline-block;
        vertical-align: middle;
      }

      .formH3Point {
        width: 8px;
        height: 8px;
        background: #247CFF;
        border-radius: 50%;
        margin-right: 5px;
      }
    }
  }
}

.explain-words {
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  line-height: 24px;
}
</style>
