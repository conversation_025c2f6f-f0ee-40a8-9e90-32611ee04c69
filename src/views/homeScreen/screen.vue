<template>
  <div class="dataAcq">
      <div class="dataAcqBody">
          <div class="dataHead">
              <img src="@/assets/images/publicSitFont.png" alt="">
          </div>
          <div class="dataCot">
             <!-- 翻页 -->
                <div class="hometablist">
                    <!-- <span class="tab-prev" @click="goPage('prev')">上一页</span> -->
                    <span class="tab-next" @click="goPage('next')">下一页</span>
                    <div class="date">{{currentTime}}</div>
                </div>
              <div class="jumpPage">
                  <ul class="jumpUL">
                     <li 
                     :class="currentIndex == item.planId ? 'cur': '' " 
                     v-for="(item,index) in plates" 
                     :key="item.planId"
                     @click="changePlate(item.planId)">{{ item.planName }}</li>
                  </ul>
              </div>
              <div class="dataContent">
                  <div class="dataOneFloor">
                      <div class="dataOneFloorLeft">
                         <!-- 舆情监测总数 -->
                          <div class="dataCollectCotTop">
                              <h2 class="dataCollH2">
                                <span>舆情监测总数</span>
                                <div class="spanImg">
                                    <img src="@/assets/images/dataLi.png" alt="">
                                </div>
                              </h2>
                              <div class="dataCollection dataTotalOfPub adddataTotalOfPub">
                                  <div class="dataMapHead">
                                    <moni-flipper ref="flipperRef"></moni-flipper>
                                  </div>
                              </div>

                          </div>
                          <!-- 专项情感倾向 -->
                          <div class="dataCollectCotTop">
                             <h2 class="dataCollH2">
                               <span>专项情感倾向</span>
                               <div class="spanImg">
                                 <img src="@/assets/images/dataLi.png" alt="">
                               </div>
                             </h2>
                             <div class="dataCollection dataEmotionEchart" v-loading="emtionLoading"
                                element-loading-background="rgba(18, 42, 96,0)">
                                  <pie-emotion :chartData="pieData"></pie-emotion>
                             </div>
                          </div>
                      </div>
                      <!-- 地图 -->
                      <div class="bigMap">
                        <div class="dataMap" id="dataChinaMap" v-loading="mapLoading"
                                element-loading-background="rgba(18, 42, 96,0)">
                            <china-map 
                            :data="mapData"
                           :params="queryParams"
                            :areaInfo="newParams"
                           style="width:100%;height:100%"></china-map>
                        </div>
              
                       <div class="dataRank">
                            <h2>
                                <img rc="@/assets/images/dataLi.png" alt=""> 地域热点排行
                            </h2>
                            <ul id="dataRankUl">
                              <li v-for="(item,index) in topData"><em>{{ index + 1}}</em> {{item.name}}<span>{{item.value}}</span></li>
              
                            </ul>
                        </div>
                      </div>
                     <div class="dataOneFloorLeft">
                        <!-- 字符云 -->
                        <div class="dataCollectCotTop">
                            <h2 class="dataCollH2">
                                <span>字符云</span>
                                <div class="spanImg">
                                    <img src="@/assets/images/dataLi.png" alt="">
                                </div>
                            </h2>
                            <div class="dataCollection dataTotalOfPub" id="dataFontCloud" v-loading="cloudLoading"
                                element-loading-background="rgba(18, 42, 96,0)">
                              <cloud-chart style="height:100%;" ref="cloud" :data="cloudData"></cloud-chart>
                              <div v-if="cloudData.length ==0 && !cloudLoading" style="margin-top:-30%;" class="noneData" >
                                <img :src="noDataImg" alt="">
                                <div>暂无数据</div>
                              </div>
                            </div>
                            
                        </div>
                        <!-- 实时舆情 -->
                        <div class="dataCollectCotTop" id="RealTimeOneDayTop" style="display:block;">
                            <h2 class="dataCollH2" style="width: 130%; cursor: pointer">
                                <span style="width: 100%">实时舆情(
                                  <b :class="realTime == '-1' ? 'realTime' : '' " @click="RealTimeData('-1')">24小时</b>/ 
                                  <b :class="realTime == '-7' ? 'realTime' : '' " @click="RealTimeData('-7')">一周</b>
                                )</span>
                                <div class="spanImg">
                                    <img src="@/assets/images/dataLi.png" alt="">
                                </div>
                            </h2>
                            <div class="dataNewTime dataEmotionEchart" id="dataRealTime">
                              <ul id="dataRealTimeOne" v-loading="realLoading" @click="viewKey($event)" element-loading-background="rgba(18, 42, 96,0)"
                              style="height:100%;">
                                <vue-seamless-scroll
                                    :step="0.1"
                                    hover="true"
                                    :data="realData"
                                    :class-option="defaultOption(realData)"
                                    class="seamless-warp">
                                    <div class="trend-wrap">
                                      <div v-for="(item,index) in realData" :key="index" class="trend-main">
                                          <div class="text-title">
                                            <a target="_blank" :href="item.url">
                                              <span v-html="item.title"></span>
                                            </a>
                                          </div>
                                          <div class="text-name">
                                            <div class="source">{{item.author}}</div>
                                            <div class="text-time">
                                              <img src="@/assets/images/time.png" alt="">
                                              {{item.publishTime}}
                                            </div>
                                          </div>
                                      </div>
                                      <div v-if="realData.length ==0 && !realLoading" class="noneData">
                                        <img :src="noDataImg" alt="">
                                        <div>暂无数据</div>
                                      </div>
                                    </div>
                                  </vue-seamless-scroll>
                            </ul>

                            </div>
                        </div>
               
                      </div>
                  </div>

                  <div class="dataOneFloor">
                    <!-- 今日媒体排行 -->
                     <div class="dataFrom">
                        <h2 class="dataCollH2">
                            <span>热点信息</span>
                            <div class="spanImg">
                                <img src="@/assets/images/dataLi.png" alt="">
                            </div>
                        </h2>
                        <div class="dataNewTime dataEmotionEchart" style="height: 23vh !important;" id="dataTodayMedia">
                            <ul id="syScrollHotOne" v-loading="hotLoading" @click="viewKey($event)" element-loading-background="rgba(18, 42, 96,0)"
                   style="height:100%;">
                                 <vue-seamless-scroll
                                    :step="0.1"
                                    hover="true"
                                    :data="hotData"
                                    :class-option="defaultOption(hotData)"
                                    class="seamless-warp">
                                    <div class="trend-wrap">
                                      <div v-for="(item,index) in hotData" :key="index" class="trend-main">
                                          <div class="text-title">
                                            <a target="_blank" :href="item.url">
                                              <span v-html="item.title"></span>
                                            </a>
                                          </div>
                                          <div class="text-name">
                                            <div class="source">{{item.author}}</div>
                                            <div class="text-time">
                                               <img src="@/assets/images/time.png" alt="">
                                               {{item.publishTime}}
                                            </div>
                                          </div>
                                      </div>
                                      <div v-if="hotData.length ==0 && !hotLoading" class="noneData">
                                        <img :src="noDataImg" alt="">
                                        <div>暂无数据</div>
                                      </div>
                                    </div>
                                  </vue-seamless-scroll>
                            </ul>
        
                        </div>
                     </div>
                     <div class="dataOneDayTrend">
                        <h2 class="dataCollH2">
                            <span>一周走势图</span>
                            <div>
                                <img src="@/assets/images/dataLi.png" alt="">
                            </div>
                        </h2>
                         <div class="dataOneDataChart1" id="dataTodayTrend" v-loading="lineLoading"
                 element-loading-background="rgba(18, 42, 96,0)">
                           <line-trend style="height:100%" :chartData="lineData"></line-trend>
                         </div>
                     </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
</template>

<script>

import dayjs from "dayjs";
import {beforeTime} from '@/utils/time.js'
import MoniFlipper from '@/views/homeScreen/components/MoniFlipper.vue'
import LightEmtion from '@/views/home/<USER>/LightEmtion.vue'
import PieEmotion from '@/views/homeScreen/components/PieEmotion.vue'
import ChinaMap from '@/views/homeScreen/components/ChinaMap.vue'
import CloudChart from '@/views/homeScreen/components/CloudChart.vue';
import LineTrend from '@/views/homeScreen/components/LineTrend.vue'

import { getPlans,getTotal,getEmotion,getWords,getMap,getRealTime,getHotInfo,getWeekType } from '@/api/screen/index.js'

export default {
  name: "index",
  components: { MoniFlipper,LightEmtion,PieEmotion ,ChinaMap,CloudChart,LineTrend },
  data() {
    const now = dayjs(); // 获取当前时间
    const weekdays = ['日', '一', '二', '三', '四', '五', '六']; // 映射星期几的数字到名称
    const weekdayIndex = now.day(); // 获取星期几的数字（0-6）
    const weekdayName = weekdays[weekdayIndex]; // 获取星期几的名称
    return {
      currentTime: `${now.format("YYYY/MM/DD HH:mm:ss")}   星期${weekdayName}`,
      timer: null, // 计时器 ID，用于清除计时器
      plates: [
        // { name: 'A舆情方案',id: 1 },
        // { name: 'B舆情方案',id: 2 },
        // { name: 'C舆情方案',id: 3 },
        // { name: 'D舆情方案',id: 4 },
        // { name: 'E舆情方案',id: 5 },
      ],
      currentIndex: '',
      allcountData: 0,
      queryParams: {
        // startTime: beforeTime.oneDay.split(',')[0],
        // endTime: beforeTime.oneDay.split(',')[1],
        planId: ''
      },
      pieData:[],
      cloudData: [],
      cloudLoading: false,
      realLoading: false,
      emtionLoading: false,
      lineLoading: false,
      lineData: {},
      keyDymaticList: [],
      realData: [],
      realTime: '-1',
      hotData: [],
      hotLoading: false,
      mapLoading: false,
      mapData: {},
      topData: [],
      newParams: {},
      
    };
  },
  computed:{
     defaultOption() {
      return (data) => {
        return {
          step: 0.3, // 数值越大速度滚动越快
          limitMoveNum: 8, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 0 // 单步运动停止的时间(默认值1000ms)
        }
      }
    },
    // 缺省图
    noDataImg() {
      return require('@/assets/images/nodata.png') 
    }
  },
  mounted() {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    // 在组件挂载后设置计时器
    this.timer = setInterval(() => {
      const now = dayjs();
      const weekdayIndex = now.day();
      const weekdayName = weekdays[weekdayIndex];
      this.currentTime = `${dayjs().format("YYYY/MM/DD HH:mm:ss")}   星期${weekdayName}`;
    }, 1000);
  },
  created() {
    this.getPlans()
  },
  beforeDestroy() {
    // 在组件销毁前清除计时器和移除事件监听器
    clearInterval(this.timer);
  },
  methods: {
    // 获取方案
    async getPlans(){
      const res = await getPlans()
      this.plates = res.data
      this.currentIndex = this.plates[0].planId
      this.queryParams.planId = this.plates[0].planId
      this.getAllCharts()
    },
    goPage(val) {
        this.$router.push({path:'/homeScreen/screenData'})
    },
    changePlate(planId){
      this.currentIndex = planId
      this.queryParams.planId = planId
      this.getAllCharts()
    },
    viewKey(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /keyItem/.test(r.className));
      if (target.length) {
        target = target[0];
        const data = JSON.parse(target.getAttribute("data")); // 单项数据详情，点击那行数据的所有数据
        // 跳转详情页面
        // this.goOrigin(data)
      }
      let dymatic = path.filter((r) => /dymaticItem/.test(r.className));
      if (dymatic.length) {
        dymatic = dymatic[0]
        const dymaticData = JSON.parse(dymatic.getAttribute("dymatic"));
        // this.goToExpendDetail('重点账号动态', dymaticData.name, this.allcountData, dymaticData.type, dymaticData.videoHost, 0)
      }
    },
    getAllCharts(){
      this.getCountData()
      this.getPieData()
      this.getCloudData()
      this.getMapData()
      this.getRealTime()
      this.getHotInfo()
      this.getLineData()
    },
    // 获取数据总量
    async getCountData() {
      const res = await getTotal(this.queryParams)
      this.allcountData = res.data
      this.$refs.flipperRef?.toOrderNum(parseInt(this.allcountData))
    },
    //地域分布图
    async getMapData() {
      this.mapLoading = true
      const res = await getMap(this.queryParams)
      this.mapData = { data:res.data }
      this.topData = res.data.slice(0,9)
      this.topData = this.topData.map(item => {
        const name = item.name.replace("特别行政区", "").replace("维吾尔自治区", "").replace("壮族自治区", "").replace("自治区", "").replace("省", "").replace("市", "")
          return {
            ...item,
              name
          };
      });

      this.mapLoading = false
    },
    // 情感分布
    async getPieData() {
      this.emtionLoading = true
      const res = await getEmotion(this.queryParams)
      this.pieData = res.data || []
      this.emtionLoading = false
    },
     // 热词云
    async getCloudData() {
      this.cloudData = []
      this.cloudLoading = true
      const res = await getWords(this.queryParams)
      this.cloudLoading = false
      this.cloudData = res.data || []
    
    },
    // 实时舆情
    async getRealTime() {
      try {
        this.realLoading = true
        const params = { ...this.queryParams,offset: this.realTime}
        let res = await getRealTime(params)
        this.realData = res.data || []
      } finally {
        this.realLoading = false
      }
    },
    RealTimeData(val){
      this.realTime = val
      this.getRealTime()
    },
    // 热点信息
    async getHotInfo(){
      try {
        this.hotLoading = true
        let res = await getHotInfo(this.queryParams)
        this.hotData = res.data || []
      } finally {
        this.hotLoading = false
      }
    },
     // 趋势图
    async getLineData() {
      this.lineLoading = true
      this.lineData = {}
      const res = await getWeekType(this.queryParams)
      this.lineData = res.data
      this.lineLoading = false
    },
  }
};
</script>
<style> 
.text-title em {
  color: red;
}
</style>
<style scoped lang="scss">
@import './screen.scss';
/* reset */

html,
body,
h1,
h2,
h3,
h4,
h5,
h6,
div,
dl,
dt,
dd,
ul,
ol,
li,
p,
blockquote,
pre,
hr,
figure,
table,
caption,
th,
td,
form,
fieldset,
legend,
input,
button,
textarea,
menu {
    margin: 0;
    padding: 0;
}

header,
footer,
section,
article,
aside,
nav,
hgroup,
address,
figure,
figcaption,
menu,
details {
    display: block;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

caption,
th {
    text-align: left;
    font-weight: normal;
}

html,
body,
fieldset,
img,
iframe,
abbr {
    border: 0;
}

i,
cite,
em,
var,
address,
dfn {
    font-style: normal;
}

[hidefocus],
summary {
    outline: 0;
}

li {
    list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6,
small {
    font-size: 100%;
}

sup,
sub {
    font-size: 83%;
}

pre,
code,
kbd,
samp {
    font-family: inherit;
}

q:before,
q:after {
    content: none;
}

textarea {
    overflow: auto;
    resize: none;
}

label,
summary {
    cursor: default;
}

a,
button {
    cursor: pointer;
}

h1,
h2,
h3,
h4,
h5,
h6,
em,
strong,
b {
    font-weight: bold;
}

del,
ins,
u,
s,
a,
a:hover {
    text-decoration: none;
}

body,
textarea,
input,
button,
select,
keygen,
legend {
    font: 12px/1.14 arial, \5b8b\4f53;
    color: #333;
    outline: 0;
}

body {
    background: #fff;
}

a,
a:hover {
    color: #333;
}


.tabbox {
    position: absolute;
    top: 2rem;
    left: 2rem;
    width: 30rem;
    height: 2rem;
    line-height: 2rem;
    font-size: 0.15rem;
    display: flex;
    flex-direction: row;
    margin-top: 2rem;
}

.tabbox .all {
    width: 0.6rem;
    text-align: center;
    color: #1eb7d7;
    text-shadow: 0px 1px 0px #0d5ef5, 0px 1px 0px #0d5ef5, 0px 1px 0px #0d5ef5, 0px 1px 0px #0d5ef5, 0px 1px 1px rgba(10, 75, 198, 0.6);
    cursor: pointer;
}

.tabbox .tablist {
    width: 30rem;
    margin-left: 0.3rem;
}

.tabbox .tablist .swiper-wrapper .tab-swiper {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.tabbox .tablist .swiper-wrapper .tab-swiper span {
    cursor: pointer;
    color: #a4a5a7;
    display: inline-block;
    padding: 0 0.1rem;
    font-size: 1rem;
}

.tabbox .tablist .swiper-wrapper .tab-swiper span:hover,
.tabbox .tablist .swiper-wrapper .tab-swiper span.tabActive {
    color: #ffffff;
    font-size: 1.2rem;
    /* background: url("./../images/lingt.png") no-repeat;
    background-size: cover; */
    text-shadow: 0.01rem 0.01rem 0.01rem rgba(255, 255, 255, 0.5), 0 0 0.1rem rgba(255, 255, 255, 0.5), 0 0 0.3rem rgba(255, 255, 255, 0.5), 0 0 0.3rem rgb(163, 155, 155), 0 0 0.4rem rgba(13, 94, 245, 1), 0 0 0.7rem rgba(13, 94, 245, 1), 0 0 0.8rem rgba(13, 94, 245, 1), 0 0 0.9rem rgba(13, 94, 245, 1), 0 0 1.5rem rgba(13, 94, 245, 1);
}

.tabbox .tab-prev {
    /* position: absolute;
  left: 0.6rem;
  content: "<"; */
    // background: url("../../assets/images/left-arrow.png") no-repeat;
    // background-size: cover;
    // width: 1rem;
    // cursor: pointer;
    // position: relative;
    // top: 0.1rem;
}

.tabbox .tab-next {
    /* position: absolute;
 right: 0.3rem; */
    // background: url("../../assets/images/right-arow.png") no-repeat;
    // background-size: cover;
    // width: 1rem;
    // height: 1rem;
    // cursor: pointer;
    // position: relative;
    // top: 0.5rem;
}
</style>
