<template>
  <div ref="emtionChart" class="chart"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'emtion<PERSON><PERSON>',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    showLoading: {
      type: Boolean,
      default: true
    },
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData() {
      this.$nextTick(() => {
        this.initChart()
      })
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  mounted() {
    this.initChart()
    if (this.chart) {
      this.chart.on('click', (params) => {
        const newName = params.name ? params.name : '全部'
        this.$emit('goToExpendDetail', '情感分布', newName, params.value)
      })
    }
  },
  methods: {
    initChart() {
      // 基于准备好的dom，初始化echarts实例
      this.chart = echarts.init(this.$refs.emtionChart)
      //   this.chart.showLoading()
      const namesArray = this.chartData?.map(item => item.name);
      // const index = this.data.findIndex(obj => obj.value == 0) //找到value为0的对象的索引
      // if (index !== -1) {
      //   this.data[index].value = null //将value为0的对象的值改为null
      // }
      var option = {
        color: ['#FF7F2D', '#FF0000', '#50b0f9'],
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          icon: 'circle',
          orient: 'vertical', // vertical horizontal
          top: '0%',
          left: '0%',
          data: namesArray,
          align: 'left',
          itemHeight: 8,
          itemWidth: 8,
          itemGap: 5,
          fontSize: '0.12rem',
          textStyle: {
            color: "#fff"
          },
        },
        series: [{
          name: '标题',
          type: 'pie',
          center: ['50%', '50%'],
          radius: ['35%', '60%'],
          //饼图的扇区是否是顺时针排布
          // avoidLabelOverlap: false, // 去除可以解决数据是0不显示问题和数据重叠不显示问题
          label: {
            color: 'inherit',
            formatter: `{b}:{c}`,
          },
          labelLine: {
            show: true,
            smooth: false,
          },
          fontSize: '0.12rem',
          data: this.chartData
        }]
      }
      //   if (!this.showLoading) {
      //     this.chart.hideLoading()
      //   }
      // 使用刚指定的配置项和数据显示图表。
      this.chart.setOption(option, true)
      this.chart.resize()
    }
  }
}
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
}
</style>

