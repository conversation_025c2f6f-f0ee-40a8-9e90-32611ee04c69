<template>
    <div ref="piechart" style="width: 100%; height: 100%"></div>
</template>
<script>
  import * as echarts from "echarts";
  
  export default {
    name: "piechart",
    props: {
      data: {
        type: Object,
        default: () => {
        },
      },
      showLoading: {
        type: Boolean,
        default: true,
      },
      allCount: {
        type: Number,
        default: 0,
      },
      toolName: {
        type: String,
        default: "饼图",
      },
      radius: {
        type: Array,
        default: () => ["25%", "50%"],
      },
      color: {
        type: Array,
        default: () => ["#F4A259", "#F07167", "#00B4D8"],
      },
      isToImg: {
        type: String,
        default: '',
      },
      isDown: {
        type: Boolean,
        default: false
      },
      isDataView: {
        type: Boolean,
        default: false
      },
      chartStyle: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        chart: null,
      };
    },
    watch: {
      data() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
    },
    beforeDestroy() {
      if (!this.chart) {
        return;
      }
      this.chart.dispose();
      this.chart = null;
      window.removeEventListener("resize", () => {
        if (this.chart) this.chart.resize();
      });
    },
    mounted() {
      // this.initChart();
      if (this.chart) {
        this.chart.on('click', (params) => {
          const newName = params.name ? params.name : '全部'
          this.$emit('goToExpendDetail', this.toolName, newName, params.value)
        })
      }
      window.addEventListener("resize", () => {
        if (this.chart) this.chart.resize();
      });
    },
    methods: {
      initChart() {
        this.chart = echarts.init(this.$refs.piechart);
        this.chart.showLoading();
        // const color = this.color;
        // const radius = this.radius;
        console.log(this.data,'this.data');
        
        if (this.data.data) {
          var NAMES = this.data.data.map((item)=>item.name)
          var data = this.data.data
          console.log(NAMES,'NAMES1111');
          console.log(data,'data1111');
          
        }
        const option = {
                    backgroundColor: "",
                    color: ["#3953C4", "#228BD7", "#8440B8", "#8383D7", "#3BE390", "#63DEC0", "#FFCF5B", "#FFA94F", "#FF5F5C"],
                    grid: {
                        left: -300,
                        top: 50,
                        bottom: 10,
                        right: 10,
                        containLabel: true
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: "{b} : {c} ({d}%)"
                    },
                    legend: {
                        type: "scroll",
                        orient: 'vertical',
                        // x: "right",
                        right: 0,
                        top: 20,
                        bottom: 30,
                        itemWidth: 16,
                        itemHeight: 6,
                        itemGap: 10,
                        textStyle: {
                            color: '#A3E2F4',
                            fontSize: 12,
                            fontWeight: 0
                        },
                        data: NAMES
                    },
                    polar: {},
                    angleAxis: {
                        interval: 1,
                        type: 'category',
                        data: [],
                        z: 10,
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "#0B4A6B",
                                width: 1,
                                type: "solid"
                            },
                        },
                        axisLabel: {
                            interval: 0,
                            show: true,
                            color: "#0B4A6B",
                            margin: 8,
                            fontSize: 16
                        },
                    },
                    radiusAxis: {
                        min: 40,
                        max: 120,
                        interval: 20,
                        axisLine: {
                            show: false,
                            lineStyle: {
                                color: "#0B3E5E",
                                width: 1,
                                type: "solid"
                            },
                        },
                        axisLabel: {
                            formatter: '{value} %',
                            show: false,
                            padding: [0, 0, 20, 0],
                            color: "#0B3E5E",
                            fontSize: 16
                        },
                        splitLine: {
                           show: false,
                            lineStyle: {
                                color: "#0B3E5E",
                                width: 2,
                                type: "solid"
                            }
                        }
                    },
                    calculable: true,
                    series: [
                    {
                        type: 'pie',
                        radius: ["3%", "8%"],
                        center: ['40%', '50%'],
                        hoverAnimation: false,
                        labelLine: {
                            normal: {
                                show: false,
                                length: 10,
                                length2: 35
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        data: [{
                            name: '',
                            value: 0,
                            itemStyle: {
                                normal: {
                                    color: "#0B4A6B"
                                }
                            }
                        }]
                    },
                     {
                        type: 'pie',
                        radius: ["70%", "75%"],
                        center: ['40%', '50%'],
                        hoverAnimation: false,
                        labelLine: {
                            normal: {
                                show: false,
                                length: 10,
                                length2: 35
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        name: "",
                        data: [{
                            name: '',
                            value: 0,
                            itemStyle: {
                                normal: {
                                    color: "#0B4A6B"
                                }
                            }
                        }]
                    },
                     {
                        stack: 'a',
                        type: 'pie',
                        radius: ['7%', '65%'],
                        center: ['40%', '50%'],
                        roseType: 'area',
                      zlevel: 10,
                      label: {
                            normal: {
                                show: true,
                                formatter: "{c}",
                                textStyle: {
                                    color: '#fff', // 文字颜色
                                    textShadowColor: 'transparent', // 文字阴影颜色设置为透明
                                    shadowBlur: 0 // 设置阴影模糊度为0，即去除阴影
                                },
                                // 其他标签样式配置...
                            }
                        },
                        labelLine: {
                            normal: {
                                show: true,
                                length: 10,
                                length2: 30
                            },
                            emphasis: {
                                show: false
                            }
                        },
                        data: data
                    }, ]
                }
        if (!this.showLoading) {
          this.chart.hideLoading();
        }
        this.chart.setOption(option, true);
  
        // setTimeout(() => {
        if (this.chart) {
          this.chart.on('rendered', () => {
            if (this.isToImg) {
              const chartRef = this.isDown ?
                this.chart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
                :
                this.chart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
              this.$emit('chartRef', this.isToImg, chartRef)
            }
          });
  
          // 监听工具栏中数据视图的修改事件。
          this.chart.on('dataViewChanged', (params) => {
            // params中包含了与数据视图修改相关的信息
            console.log('修改后的数据视图相关信息:', params);
            const newData = params.newOption.series[0].data
            this.$emit('modifiedData', this.chartStyle, newData)
          });
          // 监听工具栏中restore 重置 option 事件
          this.chart.on('restore', (params) => {
            this.$emit('modifiedData', this.chartStyle, '')
          });
        }
        // }, 2000);
  
  
        this.$nextTick(() => {
          this.chart.resize();
        });
      },
    },
  };
  </script>
  <style scoped lang="scss">
  ::v-deep {
    textarea {
      line-height: 1.8em !important;
    }
  }
  </style>
  