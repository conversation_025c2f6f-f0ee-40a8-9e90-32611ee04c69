<template>
    <div ref="linechart" class="chart"/>
  </template>
  
  <script>
  import * as echarts from "echarts";
  
  export default {
    name: 'lineChart',
    props: {
      data: {
        type: Object,
        default: () => {
        },
      },
      legendData: {
        type: Array,
        default: () => [],
      },
      showLoading: {
        type: Boolean,
        default: true,
      },
      show: {
        type: Boolean,
        default: true,
      },
      toolName: {
        type: String,
        default: '折线图'
      },
      chartText: {
        type: String,
        default: ""
      },
      legendTop: {
        type: Number,
        default: 0
      },
      legendLeft: {
        type: String,
        default: '5%'
      },
      lineType: {
        type: String,
        default: ""
      },
      gridTop: {
        type: String,
        default: '15%'
      },
      titleTop: {
        type: String,
        default: "30"
      },
      colors: {
        type: Array,
        default: () => ['#F46263', '#3D9FFE', '#F9A968', '#1D80DA', '#02F4FF', '#E3BC2D', '#FF6632', '#A7FFB0', '#8A01E1']
      },
      isShow: {
        type: Boolean,
        default: false
      },
      isToImg: {
        type: String,
        default: '',
      },
      isDown: {
        type: Boolean,
        default: false
      },
      isDataView: {
        type: Boolean,
        default: false
      },
      chartStyle: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        chart: null,
      };
    },
    watch: {
      data() {
        this.$nextTick(() => {
          this.initChart();
        });
      },
    },
    beforeDestroy() {
      if (!this.chart) {
        return;
      }
      this.chart.dispose();
      this.chart = null;
      window.removeEventListener('resize', () => {
        if (this.chart) this.chart.resize();
      });
    },
    mounted() {
      this.initChart()
      if (this.chart) {
        this.chart.on('click', (params) => {
          const seriesName = params.seriesName
          const seriesValue = params.value
          this.$emit('goToExpendDetail', this.toolName, seriesName, seriesValue)
        })
      }
      window.addEventListener('resize', () => {
        if (this.chart) this.chart.resize();
      })
    },
    methods: {
        initChart() {
            var colorlist = [
                ['rgba(241,40,26,1)', 'rgba(241,40,26,0.8)'],
                ['rgba(255,164,164,1)', 'rgba(255,164,164,0.8)'],
                ['rgba(160,46,219,1)', 'rgba(160,46,219,0.8)'],
                ['rgba(195,163,255,1)', 'rgba(195,163,255,0.8)'],
                ['rgba(255,28,248,1)', 'rgba(255,28,248,0.8)'],
                ['rgba(62,42,255,1)', 'rgba(62,42,255,0.8)'],
                ['rgba(61,128,246,1)', 'rgba(61,128,246,0.8)'],
                ['rgba(146,179,254,1)', 'rgba(146,179,254,0.8)'],
                ['rgb(62,212,255)', 'rgba(62,212,249,0.8)'],
                ['rgba(34,180,130,1)', 'rgba(34,180,130,0.8)'],
                ['rgba(25,222,112,1)', 'rgba(25,222,112,0.8)'],
                ['rgba(194,250,85,1)', 'rgba(194,250,85,0.8)'],
                ['rgba(255,168,0,1)', 'rgba(255,168,0,0.8)'],
                ['rgba(34,180,130,1)', 'rgba(34,180,130,0.8)'],
                ['rgba(50, 120, 80, 1)', 'rgba(50, 120, 80, 0.8)'],

            ];
        this.chart = echarts.init(this.$refs.linechart);
        const data = this.data;
        const xData = data.xs;
        this.chart.showLoading();
        const yyData = [];
        data.seriesList?.map((item) => yyData.push(item.data));
        let serieslist = [];
            for (let index in data.seriesList) {
                if (data.seriesList[index].name == '总数') {
                    serieslist.push(
                    {
                                    name: '总数',
                                    type: 'pictorialBar',
                                    xAxisIndex: 1,
                                    barCategoryGap: '-80%',
                                    symbol: 'path://d="M150 50 L130 130 L170 130  Z"',
                                    itemStyle: {
                                        normal: {
                                            color: function (params) {
                                                var colorList = [
                                                    'rgba(13,177,205,0.8)', 'rgba(29,103,182,0.6)',
                                                    'rgba(13,177,205,0.8)', 'rgba(29,103,182,0.6)',
                                                    'rgba(13,177,205,0.8)', 'rgba(29,103,182,0.6)'
                                                ];
                                                return colorList[params.dataIndex];
                                            }
                                        },
                                        emphasis: {
                                            opacity: 1
                                        }
                                    },
                                    data: data.seriesList[index].data
                                }
                    )
                } else {
                    serieslist.push(
                    {
                                    symbolSize: 10,
                                    showAllSymbol: true,
                                    name: data.seriesList[index].name,
                                    type: "line",
                                    yAxisIndex: 1,
                                    data: data.seriesList[index].data,
                                    lineStyle: {
                                        normal: {
                                            color: colorlist[index][0]
                                        }
                                    },
                                    label: {
                                        show: false,
                                        position: 'top',
                                        textStyle: {
                                            color: '#fff'
                                        }
                                    },
                                    itemStyle: {
                                        color: colorlist[index][0],
                                        borderColor: "#fff",
                                        borderWidth: 1
                                    },
                                    itemStyle: {
                                        normal: {
                                            borderWidth: 5,
                                            color: {
                                                colorStops: [{
                                                    offset: 0,
                                                    color: colorlist[index][1]
                                                },

                                                    {
                                                        offset: 1,
                                                        color: colorlist[index][0]
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                }
                    )
                }
          
            }
            let option = {
                        grid: {
                            top: '13%',
                            left: '50',
                            right: '5%',
                            bottom: '-8%',
                            containLabel: true
                        },
                        legend: {
                            orient: 'horizontal',
                            x: 'center',
                            y: 'top',
                            textStyle: {
                                color: '#fff',
                            }
              },
                        
              tooltip: {
                trigger: 'axis',
                enterable:true,
                backgroundColor: 'rgba(50,50,50,0.7)',
                position: function (point, params, dom, rect, size) {
                  //  size为当前窗口大小
                  if ((size.viewSize[0] / 2) >= point[0]) {
                    //其中point为当前鼠标的位置
                    return [point[0] + 30, '3%'];
                  } else {
                    //其中point为当前鼠标的位置
                    return [point[0] - 200, '3%'];
                  }
                },
                textStyle: {
                  fontSize: 10,
                  lineHeight: 12,
                  color: '#fff'
                }
              },
                        xAxis: [{
                            type: 'category',
                            show: false,
                            data: xData,
                            axisLabel: {
                                textStyle: {
                                    color: '#b6b5ab'
                                }
                            }
                        },
                            {
                                type: 'category',
                                position: "bottom",
                                data: xData,
                                boundaryGap: true,
                                axisTick: {
                                    show: false
                                },
                                axisLine: {
                                    show: false
                                },
                                axisLabel: {
                                    textStyle: {
                                        color: '#b6b5ab'
                                    }
                                }
                            }

                        ],
                        yAxis: [{
                            show: true,
                            name: "采集总数",
                            offset: 53,
                            nameTextStyle: {
                                color: '#fff'
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#fff"
                                }
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#fff"
                                }
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: "#fff"
                                }
                            },
                            axisLabel: {
                                show: true,
                                color: '#fff'
                            }
                        }, {
                            show: true,
                            type: "value",
                            offset: 53,
                            name: "采集量",
                            nameTextStyle: {
                                color: '#fff'
                            },
                            splitLine: {
                                show: false,
                                lineStyle: {
                                    color: "#fff"
                                }
                            },
                            axisTick: {
                                show: true,
                                lineStyle: {
                                    color: "#fff"
                                }
                            },
                            axisLine: {
                                show: true,
                                lineStyle: {
                                    color: "#fff"
                                }
                            },
                            axisLabel: {
                                show: true,
                                color: '#fff'
                            }
                        }],
                        series: serieslist
                    };
        if (!this.showLoading) {
          this.chart.hideLoading()
        }
        this.chart.setOption(option, true);
        if (this.chart) {
          // 监听工具栏中数据视图的修改事件。
          this.chart.on('dataViewChanged', (params) => {
            // params中包含了与数据视图修改相关的信息
            console.log('修改后的数据视图相关信息:', params);
            const newData = {seriesList: params.newOption.series, xs: params.newOption.xAxis[0].data}
            this.$emit('modifiedData', this.chartStyle, newData)
          });
          // 监听工具栏中restore 重置 option 事件
          this.chart.on('restore', (params) => {
            this.$emit('modifiedData', this.chartStyle, '')
          });
  
        }
      },
    },
  };
  </script>
  <style scoped lang="scss">
  ::v-deep {
    textarea {
      line-height: 1.8em !important;
    }
  }
  </style>
  