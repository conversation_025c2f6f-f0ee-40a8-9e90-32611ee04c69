<template>
    <div class="startupPage">
    <div class="screenHtml">
        <div class="screenTitle">舆情监测可视化系统</div>
        <div class="screenMenu">
            <ul>
                
                    <li>
                        <div class="screen-amplify">
                            <span>舆情态势感知</span>
                            <div class="screenimg" @click="handleScreen(1)">
                                <img src="@/assets/images/screen1.jpg" alt="">
                            </div>
                        </div>
                    </li>
                
                
                <li>
                    <div class="screen-amplify" @click="handleScreen(2)">
                        <span>数据采集态势</span>
                        <div class="screenimg">
                            <img src="@/assets/images/screen2.jpg" alt="">
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>
</template>
<script>
import {getNoticeLists, getUserSetting, setUserSetting, getUserNotice} from '@/api/float.js'
import {transImage} from '@/utils/index';

export default {
    methods: {
        handleScreen(val) {
            if (val == 2) {
                const fullPath = this.$router.resolve({ path: '/homeScreen/screenData'})
                window.open(fullPath.href, '_blank')
            } else {
                const fullPath = this.$router.resolve({ path: '/homeScreen/screen'})
                window.open(fullPath.href, '_blank')
            }
        }
    }
}
</script>
<style lang="scss" scoped>
.startupPage{
    width: 100vw;
    height: 100vh;
    background: url(../../assets/images/frontbg.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}
.screenHtml {
    width: 100%;
    height: 50%;
}
.screenTitle {
    width: 100%;
    text-align: center;
    font-size: 0.68rem;
    font-weight: bold;
    letter-spacing: 0.14rem;
    background: -webkit-gradient(linear, 0% 15%, 0% 100%, from(#aec1fb), to(#00eeff));
    color: transparent;
    -webkit-text-fill-color: transparent;
    -webkit-background-clip: text;
}
.screenMenu {
    margin-top: 1rem;
    ul {
        display: flex;
        flex-direction: row;
        justify-content: space-around;
        width: 80%;
        margin: auto;
    }
    li{
        width: 33%;
        height: 3.4rem;
        /* background-color: #00eeff; */
        background-size: 100% 100%;
        background-repeat: no-repeat;
        padding: 0.1rem;
        margin-bottom: 30px;
        box-shadow: 0 1px 20px rgba(214, 203, 203, 0.1);
        background: rgba(16, 25, 40, 0.5);
        font-size: 0.2rem;
        span {
            font-size: 0.34rem;
            color: #fff;
            height: 0.7rem;
            line-height: 0.7rem;
            text-align: center;
            display: block;
        }
        .screenimg {
            height: 2.5rem;
            width: 100%;
            overflow: hidden;
        }
        img {
            width: 100%;
            height: 100%;
        }
    }
}
.screen-amplify{
    .screenimg{
        img{
            transition: transform 2s ease;
        }
        &:hover{
            img{
                transform: scale(1.2); 
            }
        }
    }
}
</style>