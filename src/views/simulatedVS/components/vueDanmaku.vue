<template>
  <!-- debounce:弹幕刷新频率(ms)
  top:弹幕垂直间距(px)
  right:弹幕水平间距(px) -->
  <vue-danmaku
    ref="danmaku"
    v-model="danmus"
    :channels="channels"
    :speeds="speeds"
    :randomChannel="randomChannel"
    :performanceMode="performanceMode"
    :loop="loop"
    :debounce="50"
    autoResize
    useSlot
    style="width: 100%; height: 100%;"
  >
    <template v-slot:dm="{ danmu }">
      <div class="danmu-item" :style="{
          color: danmu.color||'#fff',
          fontSize: danmu.size||'16px',
          fontFamily: 'SourceHanSansCN, PingFangSC, sans-serif',
          fontWeight: '600'
        }">
        {{ danmu.text }}
      </div>
    </template>
  </vue-danmaku>
</template>

<script>
import vueDanmaku from 'vue-danmaku'

export default {
  components: { vueDanmaku },
  props: {
    speeds: {//弹幕速度（每秒移动的像素数）
      type: Number,
      default: 200
    },
    channels: {//轨道数量
      type: Number,
      default: 7
    },
    randomChannel: {//随机选择轨道插入
      type: Boolean,
      default: true
    },
    performanceMode: {
      type: Boolean,
      default: true
    },
    loop: {//是否开启弹幕循环
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      danmus: [] // 清空初始数据
    }
  },
  methods: {
    // 兼容原 Barrage 组件的 API
    addMessage(msg) {
      this.addDanmu(msg);
    },
    // 使用 vue-danmaku 推荐的 API
    addDanmu(msg) {
      if (typeof msg === 'string') {
        this.danmus.push({ text: msg || '' });
      } else if (typeof msg === 'object') {
        this.danmus.push(msg);
      }
    },
    // 清空弹幕
    clear() {
      this.danmus = [];
    },
    // 暂停弹幕
    pause() {
      this.$refs.danmaku.pause();
    },
    // 继续播放
    play() {
      this.$refs.danmaku.play();
    },
    // 停止播放并清空
    stop() {
      this.$refs.danmaku.stop();
    }
  },
  mounted() {
    this.$refs.danmaku.play();
  },
  beforeDestroy() {
    this.$refs.danmaku.stop();
  }
}
</script>

<style lang="scss" scoped>
:deep(.danmu-item) {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  white-space: nowrap;
  line-height: 1.5;
  padding: 2px 4px;
  border-radius: 2px;
  /* 添加动画过渡 */
  transition: opacity 0.3s;
}

::v-deep .danmus .dm{
  font-size: 1em;
}
</style>