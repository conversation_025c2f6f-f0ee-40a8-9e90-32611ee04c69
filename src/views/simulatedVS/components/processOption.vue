<template>
    <div class="processOption">
        <!-- <div v-if="nextStep" class="buttonGroup">
            <div class="button-item button-red" @click="cilckOverDrill">
                <img src="@/assets/images/simulatedVS/overDrill.png" alt="">
                结束演练
            </div>
            <div class="button-item button-blue" @click="cilckNextProcess">
                <img src="@/assets/images/simulatedVS/nextProcess.png" alt="">
                下一阶段
            </div>
        </div>
        <div v-else class="buttonGroup">
            <div class="button-item">
                <img src="@/assets/images/simulatedVS/overDrill-grey.png" alt="">
                结束演练
            </div>
            <div class="button-item">
                <img src="@/assets/images/simulatedVS/nextProcess-grey.png" alt="">
                下一阶段
            </div>
        </div> -->
        <div class="buttonGroup">
            <div class="button-item button-red" @click="cilckOverDrill">
                <img src="@/assets/images/simulatedVS/overDrill.png" alt="">
                结束演练
            </div>
            <div v-if="nextStep" class="button-item button-blue" @click="cilckNextProcess">
                <img src="@/assets/images/simulatedVS/nextProcess.png" alt="">
                下一阶段
            </div>
            <div v-else class="button-item">
                <img src="@/assets/images/simulatedVS/nextProcess-grey.png" alt="">
                下一阶段
            </div>
        </div>

        <div class="countdown">
            <div class="teamTab">
                <div :class="[{'blueTeam':currentTab=='2'}]" @click="currentTab='2'">蓝队</div>
                <div :class="[{'redTeam':currentTab=='1'}]" @click="currentTab='1'" style="border-left: 0.01em solid #ccc;border-right: 0.01em solid #ccc;">红队</div>
                <div :class="[{'redBlueTeam':currentTab=='3'}]" @click="currentTab='3'">红队+蓝队</div>
            </div>
            <div class="countdown-main">
                <el-input-number v-model="countdownNum" controls-position="right" :min="1"
                    style="width: 120px;"></el-input-number>
                <div class="countdown-text" style="flex: 1;">（分钟）倒计时</div>
                <!-- <el-button :disabled="!(currentStep&&nextStep)" type="primary" @click="startCountdown">开始</el-button> -->
                <el-button :disabled="!currentStep||detailInfo.status==3" type="primary" @click="startCountdown">开始</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import { drillStageNextApi, drillProcessLeaveApi, drillTimerStartApi, drillProcessEndApi } from "@/api/simulatedVS/index.js";
export default {
    props: {
        detailInfo: {
            type: Object,
            default: () => {
                return {
                    drillTaskId: '',
                }
            }
        },
        currentStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        nextStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
    },
    data() {
        return {
            currentTab: '2',
            countdownNum: 1,
        }
    },
    mounted() {
    },
    methods: {
        // 结束演练
        cilckOverDrill() {
            let params = {
                drillTaskId: this.detailInfo.drillTaskId
            }
            drillProcessEndApi(params).then(res => {
                if (res.code == '200') {
                    this.$router.push({
                        path: '/simulatedVS/index',
                    })
                }
            })
        },
        // 下一阶段
        cilckNextProcess() {
            let redSeconds = this.$parent.$parent.$refs['miniVsScreen'].redSeconds
            let blueSeconds = this.$parent.$parent.$refs['miniVsScreen'].blueSeconds

            if(redSeconds||blueSeconds){
                return this.$message.warning('倒计时未结束，无法进入下一阶段！');
            }

            this.$confirm('确定要进入下一阶段吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
            }).then(() => {
                let params = {
                    currentStageId: this.currentStep,
                    nextStageId: this.nextStep,
                    drillTaskId: this.detailInfo.drillTaskId
                }
                drillStageNextApi(params).then(res => {
                    if (res.code == 200) {
                        this.$emit('setStep', { currentStep: res.data.currentStageId, nextStep: res.data.nextStageId })
                        this.$message({
                            type: 'success',
                            message: '操作成功'
                        });
                    }
                })
            }).catch(() => {
            });

        },
        // 开始倒计时
        startCountdown() {
            console.log('currentTab,countdownNum', this.currentTab, this.countdownNum)
            let params = {
                drillTaskId: this.detailInfo.drillTaskId,
                processStageId: this.currentStep,
                timerDuration: this.countdownNum * 60, // 倒计时秒数
                remainTimerDuration: "" // 剩余秒数
            }

            if (this.currentTab == '3') {
                params.timerType = '1' // 1:同时计时 2:分开计时

            }else{
                params.timerType = '2' // 1:同时计时 2:分开计时
                params.teamType = this.currentTab // 1:红队 2:蓝队
            }
            drillTimerStartApi(params).then(res => {
                if (res.code == 200) {
                    this.$message.success('倒计时开始')
                } else {
                    this.$message.warning(res.data)
                }
            })
        },
    }
}
</script>
<style scoped lang="scss">
.processOption {

    .buttonGroup {
        display: flex;
        justify-content: space-around;
        margin-bottom: 40px;

        .button-item {
            padding: 10px 20px;
            color: #CCCCCC;
            border: 1px solid #CCCCCC;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;

            img {
                height: 16px;
                margin-right: 10px;
            }
        }

        .button-red {
            color: #F60000;
            border: 1px solid #F60000;
        }

        .button-blue {
            color: #247CFF;
            border: 1px solid #247CFF;
        }
    }

    .countdown {
        .teamTab {
            width: fit-content;
            display: flex;
            margin-bottom: 20px;
            border: 1px solid #CCCCCC;
            border-radius: 5px;

            >div {
                padding: 10px 20px;
                color: #666666;
                cursor: pointer;
            }

            .blueTeam {
                color: #FFFFFF;
                background-color: #247CFF;
                border-bottom-left-radius: 5px;
                border-top-left-radius: 5px;
            }

            .redTeam {
                color: #FFFFFF;
                background-color: #F60000;
            }

            .redBlueTeam{
                color: #FFFFFF;
                background-color: #A505C6;
                border-bottom-right-radius: 5px;
                border-top-right-radius: 5px;
            }
        }

        .countdown-main {
            display: flex;
            align-items: center;
        }
    }
}

/* 手机横屏模式优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
    .processOption {
        .buttonGroup {
            margin-bottom: 20px;

            .button-item {
                padding: 6px 12px;
                font-size: 12px;
                border-radius: 15px;

                img {
                    height: 12px;
                    margin-right: 5px;
                }
            }
        }

        .countdown {
            .teamTab {
                margin-bottom: 10px;

                > div {
                    padding: 5px 10px;
                    font-size: 12px;
                }
            }

            .countdown-main {
                flex-wrap: wrap;
                .countdown-text{
                    font-size: 11px;
                }

                .el-input-number {
                    width: 100px !important;
                    margin-right: 5px;
                    line-height: 28px;

                    ::v-deep .el-input__inner {
                        height: 30px;
                        line-height: 30px;
                        font-size: 12px;
                    }

                    ::v-deep .el-input-number__decrease,
                    ::v-deep .el-input-number__increase {
                        width: 25px;
                        height: 15px;
                        line-height: 15px;
                    }
                }

                .el-button {
                    margin-top: 10px;
                    padding: 5px 10px;
                    font-size: 12px;
                }
            }
        }
    }
}
</style>