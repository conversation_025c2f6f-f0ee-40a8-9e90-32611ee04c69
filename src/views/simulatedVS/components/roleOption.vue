<template>
    <div class="roleOption">
        <div v-if="detailInfo.roleInfo=='moderator'" class="role-title">
            <div class="main-role">
                <img style="height: 17px;" src="@/assets/images/simulatedVS/compere.png" alt="">
                主持人
            </div>
        </div>
        <div v-if="detailInfo.roleInfo=='redCap'" class="role-title">
            <div class="main-role">
                <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                红队队长：{{detailInfo.redCaptainUser.nickName||''}}
            </div>
            <div class="other-role">
                队员：{{getMemberName(detailInfo.redMemberUser)||''}}
            </div>
        </div>
        <div v-if="detailInfo.roleInfo=='redMem'" class="role-title">
            <div class="main-role">
                <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                队员：{{nickName||''}}
            </div>
            <div class="other-role">
                红队队长：{{detailInfo.redCaptainUser.nickName||''}}
            </div>
        </div>
        <div v-if="detailInfo.roleInfo=='blueCap'" class="role-title">
            <div class="main-role">
                <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                蓝队队长：{{detailInfo.blueCaptainUser.nickName||''}}
            </div>
            <div class="other-role">
                队员：{{getMemberName(detailInfo.blueMemberUser)||''}}
            </div>
        </div>
        <div v-if="detailInfo.roleInfo=='blueMem'" class="role-title">
            <div class="main-role">
                <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                队员：{{nickName||''}}
            </div>
            <div class="other-role">
                蓝队队长：{{detailInfo.blueCaptainUser.nickName||''}}
            </div>
        </div>
        <div v-if="detailInfo.roleInfo=='spectator'" class="role-title">
            <div class="main-role">
                观众
            </div>
        </div>
        <div v-if="detailInfo.roleInfo=='expert'" class="role-title">
            <div class="main-role">
                <img style="height: 17px;" src="@/assets/images/simulatedVS/expert.png" alt="">
                专家
            </div>
        </div>
        <div class="option-Tab">
            <div :class="['tab-item', { 'active-tab': item.value === activeTab }]" v-for="item in tabsList"
                :key="item.value" @click="tabClick(item)">
                {{ item.label }}
            </div>
        </div>
        <div class="option-Main">
            <!-- 流程操作 -->
            <ProcessOption v-show="activeTab === '1'" :detailInfo="detailInfo" :current-step="currentStep"
                :next-step="nextStep" @setStep="setStep"></ProcessOption>

            <!-- 人员信息 -->
            <div v-show="activeTab === '2'" class="personInfo">
                <div class="team-block" style="margin-bottom: 20px;">
                    <div class="team-title">
                        <img src="@/assets/images/simulatedVS/blueRectangle.png" alt="">
                        蓝队
                    </div>
                    <div class="person-list">
                        <div style="margin-bottom: 10px;">队长：{{detailInfo.blueCaptainUser.nickName||''}}</div>
                        <div>队员：{{getMemberName(detailInfo.blueMemberUser)||''}}</div>
                    </div>
                </div>
                <div class="team-block">
                    <div class="team-title">
                        <img src="@/assets/images/simulatedVS/redRectangle.png" alt="">
                        红队
                    </div>
                    <div class="person-list">
                        <div style="margin-bottom: 10px;">队长：{{detailInfo.redCaptainUser.nickName||''}}</div>
                        <div>队员：{{getMemberName(detailInfo.redMemberUser)||''}}</div>
                    </div>
                </div>
            </div>

            <!-- 发布指令 -->
            <div v-show="activeTab === '3'">
                <el-input type="textarea" maxlength="100" show-word-limit :autosize="{ minRows: 8, maxRows: 10}"
                    placeholder="请输入指令" v-model.trim="instruction">
                </el-input>
                <div style="display: flex; justify-content: flex-end;margin-top: 20px;">
                    <el-button size="small" @click="instruction=''">置空</el-button>
                    <el-button :disabled="submitDisable" size="mini" type="primary"
                        @click="submitInstruction">发布</el-button>
                </div>
            </div>

            <!-- 发布评论 -->
            <div v-show="activeTab === '4'">
                <PublicComment :detailInfo="detailInfo" :current-step="currentStep" :next-step="nextStep">
                </PublicComment>
            </div>

            <!-- 专家点评 -->
            <div v-show="activeTab === '5'">
                <!-- 蓝队点评 -->
                <CommentTeam :detailInfo="detailInfo" :current-step="currentStep" :next-step="nextStep" :teamType="2"></CommentTeam>
            </div>
            <div v-show="activeTab === '6'">
                <!-- 红队点评 -->
                <CommentTeam :detailInfo="detailInfo" :current-step="currentStep" :next-step="nextStep" :teamType="1"></CommentTeam>
            </div>
        </div>
    </div>
</template>

<script>
import ProcessOption from '@/views/simulatedVS/components/processOption.vue'
import PublicComment from '@/views/simulatedVS/components/publicComment.vue'
import CommentTeam from '@/views/simulatedVS/components/commentTeam.vue'
import { drillStageNextApi, drillCommentPublishApi } from "@/api/simulatedVS/index.js";
export default {
    components: { ProcessOption, PublicComment, CommentTeam },
    props: {
        currentStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        nextStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        steps: {
            type: Array,
            default() {
                return []
            }
        },
        detailInfo: {
            type: Object,
            default() {
                return {
                    redCaptainUser: {},
                    blueCaptainUser: {}
                }
            }
        },
    },
    data() {
        return {
            activeTab: '1',
            instruction: '',
            nowTeamType: 1, // 当前队伍
        }
    },
    computed: {
        tabsList() {
            if (this.detailInfo.roleInfo == 'moderator') {
                return [
                    { label: '流程操作', value: '1' },
                    { label: '人员信息', value: '2' },
                ]
            } else if (this.detailInfo.roleInfo == 'expert') {
                return [
                    { label: '蓝队点评', value: '5' },
                    { label: '红队点评', value: '6' },
                ]
            } else if (this.detailInfo.roleInfo == 'redCap' || this.detailInfo.roleInfo == 'blueCap') {
                return [
                    { label: '发布指令', value: '3' },
                    { label: '发布评论', value: '4' },
                ]
            } else if (this.detailInfo.roleInfo == 'redMem' || this.detailInfo.roleInfo == 'blueMem') {
                return [
                    { label: '发布评论', value: '4' },
                ]
            } else {
                return [
                    { label: '人员信息', value: '2' }
                ]
            }
        },
        submitDisable() { // 发布按钮的disable
            // return (!this.currentStep || !this.nextStep) || this.selfSeconds <= 0
            return (!this.currentStep) || this.selfSeconds <= 0
        },
        selfSeconds() { // 当前队伍的倒计时秒数
            if (this.nowTeamType == 1) {
                return this.$parent.$refs['miniVsScreen'].redSeconds
            } else {
                return this.$parent.$refs['miniVsScreen'].blueSeconds
            }
        },
        nickName() {
            console.log('this.$store.state.user :>> ', this.$store.state.user);
            return this.$store.state.user.user.nickName
        },
    },
    watch: {
        tabsList: {
            immediate: true,
            handler(newVal) {
                if (newVal.length > 0) {
                    this.activeTab = newVal[0]?.value;
                }
            }
        },
        detailInfo: {
            immediate: true,
            deep: true,
            handler(newVal) {
                this.nowTeamType = (newVal.roleInfo == 'redCap' || newVal.roleInfo == 'redMem') ? 1 : 2
            },
        },
    },
    mounted() {
    },
    methods: {
        tabClick(item) {
            this.activeTab = item.value
        },
        // 发布指令
        submitInstruction() {
            if (!this.instruction) {
                this.$message.error('请输入指令')
                return
            }
            let params = {
                drillTaskId: this.detailInfo.drillTaskId,//演练任务ID
                processStageId: this.currentStep,//所属阶段ID
                commentType: '6',//评论类型 1:热搜榜 2:热议话题 3:普通评论 4:情况通报 5:警情通报 6:指令
                roleInfo: this.detailInfo.roleInfo,//角色类型
                isCaptain: true,//是否队长发布（0:否 1:是）
                content: this.instruction,//评论内容
            }
            drillCommentPublishApi(params).then(res => {
                this.$message.success('发布成功')
                this.instruction = ''
            })
        },
        getMemberName(list = []) {
            return list.map(item => item.nickName).join('，')
        },
        // 传递setStep方法
        setStep(value) {
            this.$emit('setStep', value)
        }
    }
}
</script>

<style scoped lang="scss">
.roleOption {
    .role-title {
        background-color: #FFFFFF;
        padding: 0 20px 20px 0px;

        img {
            height: 12px;
            margin-right: 10px;
        }

        .main-role {
            display: flex;
            align-items: center;
            font-size: 17px;
            font-weight: 600;
        }

        .other-role {
            margin: 15px 0 0 42px;
            font-size: 14px;
        }
    }

    .option-Tab {
        margin: 0 -20px;
        padding-top: 10px;
        background-color: #F4FBFF;
        display: flex;
        justify-content: space-around;
        align-content: center;

        .tab-item {
            font-weight: 600;
            cursor: pointer;
            font-size: 17px;
            padding-bottom: 10px;

            &:hover {
                color: #247CFF;
            }
        }

        .active-tab {
            color: #247CFF;
            border-bottom: 4px solid #247CFF;
        }
    }

    .option-Main {
        padding: 20px 0;

        .personInfo {
            .team-block {
                .team-title {
                    font-weight: 600;
                    font-size: 17px;

                    img {
                        height: 12px;
                        margin-right: 10px;
                    }
                }

                .person-list {
                    margin-top: 15px;
                    margin-left: 45px;
                    font-size: 14px;
                }
            }
        }
    }
}

/* 手机横屏模式优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
    .roleOption {
        /* 减小整体内边距 */
        padding: 5px;

        .role-title {
            padding: 0 10px 10px 0px;

            img {
                height: 10px;
                margin-right: 5px;
            }

            .main-role {
                font-size: 14px;
            }

            .other-role {
                margin: 8px 0 0 25px;
                font-size: 12px;
            }
        }

        .option-Tab {
            margin: 0 -10px;
            padding-top: 5px;
            flex-wrap: wrap; /* 允许在需要时换行 */

            .tab-item {
                font-size: 13px;
                padding: 5px 8px;
                margin-bottom: 5px;
                white-space: nowrap; /* 防止文本换行 */

                /* 减小底部边框宽度 */
                &.active-tab {
                    border-bottom-width: 2px;
                }
            }
        }

        .option-Main {
            padding: 10px 0;

            /* 调整人员信息区域 */
            .personInfo {
                .team-block {
                    margin-bottom: 10px !important;

                    .team-title {
                        font-size: 14px;

                        img {
                            height: 10px;
                            margin-right: 5px;
                        }
                    }

                    .person-list {
                        margin-top: 8px;
                        margin-left: 25px;
                        font-size: 12px;

                        div {
                            margin-bottom: 5px !important;
                        }
                    }
                }
            }

            /* 调整文本区域大小 */
            .el-textarea {
                ::v-deep .el-textarea__inner {
                    font-size: 12px;
                    padding: 5px;
                }
            }

            /* 调整按钮大小和间距 */
            .el-button {
                padding: 5px 10px;
                font-size: 12px;
                margin-left: 5px;
            }
        }
    }
}
</style>