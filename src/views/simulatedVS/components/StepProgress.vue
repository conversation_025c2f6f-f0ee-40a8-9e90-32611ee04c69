<template>
    <div class="step-progress">
        <div id="step-box">
            <div v-for="(item, index) in steps" :key="index"
                :class="['stepBar', { 'active-ba': isEqual(item.step, currentStep), 'last-step': index === steps.length - 1 }]"
                @click="handleStepClick(item)">
                <span v-show="numCirle" class="cirle">{{ item.step }}</span>
                <span class="font-sign">{{ item.title }}</span>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'StepProgress',
    props: {
        steps: {
            type: Array,
            required: true,
            validator: value => value.every(item =>
                Object.prototype.hasOwnProperty.call(item, 'step') &&
                Object.prototype.hasOwnProperty.call(item, 'title')
            )
        },
        currentStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        numCirle: {
            type: Boolean,
            default: false
        }
    },
    methods: {
        isEqual(a, b) {
            return JSON.stringify(a) === JSON.stringify(b);
        },
        handleStepClick(step) {
            this.$emit('clickStep', step);
        }
    }
}
</script>

<style scoped lang="scss">
#step-box {
    display: flex;
    justify-content: center;

    .stepBar {
        flex: 1;
        max-width: 164px;
        height: 60px;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        background-image: url("../../../assets/images/simulatedVS/step-arrow.png");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;

        // &:not(.last-step)::before {
        //     content: "";
        //     position: absolute;
        //     right: -68px;
        //     top: 0;
        //     z-index: 1;
        //     border: solid transparent;
        //     border-width: 40px 40px 40px 28px;
        //     border-left-color: inherit;
        // }
    }

    .active-ba {
        background-image: url("../../../assets/images/simulatedVS/step-arrow-active.png");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;
        /* 宽高都设置为100% */
    }

    .cirle {
        display: inline-block;
        border-radius: 50%;
        background: #fff;
        width: 30px;
        margin-right: 10px;
        height: 30px;
        color: #247CFF;
        line-height: 30px;
        text-align: center;
    }
}

/* 手机横屏模式优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
    #step-box {
        flex-wrap: wrap;
        justify-content: flex-start;

        .stepBar {
            // width: 110px;
            height: 25px;
            font-size: 10px;
            margin: 2px;

            .cirle {
                width: 20px;
                height: 20px;
                line-height: 20px;
                margin-right: 5px;
            }

            .font-sign {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 80px;
            }
        }
    }
}

/* 手机竖屏模式优化 */
@media screen and (max-width: 768px) and (orientation: portrait) {
    #step-box {
        flex-wrap: nowrap; /* 不换行 */
        justify-content: space-between; /* 均匀分布 */
        overflow-x: auto; /* 允许水平滚动 */
        padding: 5px 0;

        /* 添加滚动条样式 */
        &::-webkit-scrollbar {
            height: 4px;
        }

        &::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 2px;
        }

        .stepBar {
            flex: 0 0 auto; /* 不伸缩，保持原始大小 */
            min-width: 120px; /* 最小宽度 */
            max-width: none; /* 取消最大宽度限制 */
            height: 40px;
            font-size: 12px;
            margin: 0 5px;

            .cirle {
                width: 24px;
                height: 24px;
                line-height: 24px;
                margin-right: 5px;
            }

            .font-sign {
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 80px;
            }
        }
    }
}
</style>