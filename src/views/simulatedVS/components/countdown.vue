<template>
    <div class="countdown">
        <!-- 分钟 -->
        <div class="time-block">
            <span v-for="(digit, index) in splitDigits(paddedMinutes)" :key="'m' + index"
                :class="['digit-box',{'redTheme':themeColor=='red'}]">
                {{ digit }}
            </span>
            <span class="separator">'</span>
        </div>

        <!-- 秒钟 -->
        <div class="time-block">
            <span v-for="(digit, index) in splitDigits(paddedSeconds)" :key="'s' + index"
                :class="['digit-box',{'redTheme':themeColor=='red'}]">
                {{ digit }}
            </span>
            <span class="separator">"</span>
        </div>
    </div>
</template>

<script>
export default {
    model: {
        prop: 'value',
        event: 'input'
    },
    props: {
        value: {
            type: Number,
            required: true
        },
        themeColor: {
            type: String,
            default: "blue"
        },
    },
    data() {
        return {
            remainingTime: this.value,
            timer: null
        };
    },
    computed: {
        // 计算分钟，并确保始终是2位数（如 02, 12）
        paddedMinutes() {
            return String(Math.floor(this.remainingTime / 60)).padStart(2, "0");
        },
        // 计算秒数，并确保始终是2位数（如 02, 45）
        paddedSeconds() {
            return String(this.remainingTime % 60).padStart(2, "0");
        }
    },
    watch: {
        value: {
      immediate: true,
      handler(newVal) {
        this.resetCountdown(newVal);
      }
    }
    },
    methods: {
        startCountdown() {
      if (this.timer) clearInterval(this.timer);
      this.timer = setInterval(() => {
        if (this.remainingTime > 0) {
          this.remainingTime--;
          this.$emit('input', this.remainingTime); // 新增数据同步
        } else {
          clearInterval(this.timer);
          this.$emit("countdown-finished");
        }
      }, 1000);
    },
    resetCountdown(newTime) {
      this.remainingTime = newTime;
      this.$emit('input', this.remainingTime); // 新增数据同步
      this.startCountdown();
    },
        splitDigits(numberString) {
            return numberString.split(""); // 拆分成单个字符数组
        },
    },
    beforeDestroy() {
        if (this.timer) clearInterval(this.timer);
    }
};
</script>

<style scoped lang="scss">
.countdown {
    display: flex;
    align-items: center;
    font-size: 1em;
    padding-top: 0.5em;
    color: #FFFFFF;
    font-weight: bold;

    .time-block {
        display: flex;
        align-items: center;
        margin-right: 1em;

        .digit-box {
            display: inline-block;
            padding: 0.15em 0.4em;
            background-repeat: no-repeat;
            background-position: center;
            background-size: 100% 100%;
            text-align: center;
            font-size: 1.5em;
            font-weight: 500;
            margin: 0 0.1em;
            background-image: url('../../../assets/images/simulatedVS/countdown-bg-blue.png');
            color: #66FFFF;
        }

        .redTheme {
            background-image: url('../../../assets/images/simulatedVS/countdown-bg-red.png');
            color: #FFFFFF;
        }

        .separator {
            font-size: 1.8em;
            position: relative;
            top: -0.4em;
        }
    }
}
</style>