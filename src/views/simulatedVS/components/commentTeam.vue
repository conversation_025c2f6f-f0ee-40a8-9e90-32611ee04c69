<template>
    <div class="commentTeam">
        <!-- 阶段切换 -->
        <div class="topProcess">
            <div v-for="(item, index) in steps" :key="index"
                :class="['stepBar', { 'active-ba': isEqual(item.step, form.scoreProcessStageId) }]"
                @click="handleStepClick(item)">
                <div class="stepName">{{ item.title }}</div>
                <div class="bottom-block">
                    <div class="grey-line" v-if="index !== 0"></div>
                    <div class="grey-line-none" v-else></div>
                    <div class="point"></div>
                    <div class="grey-line" v-if="index !== steps.length - 1"></div>
                    <div class="grey-line-none" v-else></div>
                </div>
            </div>
        </div>
        <!-- 操作统计 -->
        <div class="optionsCount">

            <div v-show="teamType=='2'" class="optionsCount-item">
                <div class="optionsCount-title">热搜：</div>
                <div class="optionsCount-num">{{ scoreStatistics.hotRankCount||0 }}</div>
            </div>

            <div v-show="teamType=='2'" class="optionsCount-item">
                <div class="optionsCount-title">爆料贴文：</div>
                <div class="optionsCount-num">{{ scoreStatistics.hotTopicCount||0 }}</div>
            </div>
            <div v-show="teamType=='1'" class="optionsCount-item">
                <div class="optionsCount-title">情况通报：</div>
                <div class="optionsCount-num">{{ scoreStatistics.situationCommentCount||0 }}</div>
            </div>
            <div v-show="teamType=='1'" class="optionsCount-item">
                <div class="optionsCount-title">分析报告：</div>
                <div class="optionsCount-num">{{ scoreStatistics.policeCommentCount||0 }}</div>
            </div>
            <div class="optionsCount-item">
                <div class="optionsCount-title">评论数：</div>
                <div class="optionsCount-num">{{ scoreStatistics.replyCount||0 }}</div>
            </div>
            <div class="optionsCount-item">
                <div class="optionsCount-title">点赞数：</div>
                <div class="optionsCount-num">{{ scoreStatistics.likeCount||0 }}</div>
            </div>
        </div>
        <!-- 专家打分 -->
        <div class="expertScoring">
            <div class="block-title">专家打分：</div>
            <div class="expertScoring-item" v-for="(item, index) in scoringRules" :key="index">
                <div class="scoringRules-score">{{ item.score }}分</div>
                <div class="scoringRules-desc">{{ item.desc }}</div>
                <div class="scoringRules-selectbox" @click="handleScoreSelect(item)">
                    <div :class="['selectboxInside', { 'selected': isEqual(item.score, form.score) }]"></div>
                </div>
            </div>
        </div>
        <!-- 点评内容 -->
        <div class="commentContent">
            <div class="block-title">点评内容：</div>
            <el-input type="textarea" v-model="form.content" maxlength="1000" show-word-limit placeholder="请输入点评内容"
                :autosize="{ minRows: 6, maxRows: 8}" style="width: 100%" />
        </div>
        <div class="buttonGroup">
            <el-button :disabled="isScored||overStage" type="primary" @click="handleSubmit">提交</el-button>
            <el-button style="margin-left: 10px;" @click="handleCancel">取消</el-button>
        </div>
    </div>
</template>

<script>
import { drillScoreQueryApi, drillScoreStatisticsApi, drillCommentPublishApi } from "@/api/simulatedVS/index.js";
export default {
    props: {
        detailInfo: {
            type: Object,
            default: () => {
                return {
                    drillTaskId: '',
                }
            }
        },
        currentStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        nextStep: {
            type: [Number, String], // 支持数字和字符串
            required: false
        },
        teamType: {
            type: Number, // 支持数字
            required: true
        },
    },
    computed: {
        // 阶段
        steps() {
            let steps = []
            if (this.detailInfo && this.detailInfo.drillProcessStageRes && this.detailInfo.drillProcessStageRes.length > 0) {
                steps = this.detailInfo.drillProcessStageRes.filter(item => item.stageType != 2).map(item => ({ step: item.processStageId, title: item.stageName }))
            }

            return steps
        },
    },
    data() {
        return {
            scoringRules: [
                { score: '5', desc: '基本操作完成、符合逻辑、语言简练' },
                { score: '8', desc: '基本操作流畅、图文并茂、逻辑清晰、语言简练' },
                { score: '10', desc: '基本操作流畅、图文并茂、逻辑清晰、语言简练、有煽动性' },
                { score: '15', desc: '基本操作流畅、图文并茂、逻辑清晰、语言简练、有煽动性、具有爆点' },
            ],

            form: {
                score: '',
                content: '',
                drillTaskId: '',
                scoreProcessStageId: '',// 当前选中的阶段
            },

            scoreStatistics: {},// 当前阶段统计

            isScored: false,//当前阶段是否已打分
            overStage:false,//选中的阶段是否还未进行到
        }
    },
    watch: {
        steps: {
            handler(newValue, oldValue) {
                if (newValue && newValue.length > 0 && oldValue != newValue) {
                    // this.form.scoreProcessStageId = newValue[0]?.step
                    this.handleStepClick({ step: newValue[0]?.step })
                }
            },
        },
        'form.scoreProcessStageId': {
            immediate: true,
            handler(newStageId, oldStageId) {
                if (newStageId === oldStageId) return

                // 创建阶段映射表提升查找性能
                const stageMap = this.detailInfo.drillProcessStageRes?.reduce((acc, cur) => {
                    acc[cur.processStageId] = cur
                    return acc
                }, {}) || {}

                // 添加安全校验
                const currentStage = stageMap[this.currentStep]
                const selectedStage = stageMap[newStageId]

                if (!currentStage || !selectedStage) {
                    // console.error('存在空值')
                    return this.overStage = false
                }

                // 优化逻辑判断
                this.overStage = selectedStage.stageOrder > currentStage.stageOrder
            }
        }
    },
    mounted() {
    },
    methods: {
        isEqual(a, b) {
            return JSON.stringify(a) === JSON.stringify(b);
        },

        handleStepClick(item) {
            if (this.form.scoreProcessStageId == item.step) return
            this.form.scoreProcessStageId = item.step
            this.getScoreStatistics()
            let params = {
                drillTaskId: this.detailInfo.drillTaskId,
                scoreProcessStageId: item.step,
                teamType: this.teamType,
            }
            drillScoreQueryApi(params).then(res => {
                if (res.code == 200) {
                    if (!res.data) {
                        this.form = {
                            score: '',
                            content: '',
                            drillTaskId: this.detailInfo.drillTaskId,
                            scoreProcessStageId: item.step,
                        }
                        this.isScored = false
                    } else {
                        this.form = res.data
                        this.isScored = true
                    }
                }
            })
        },
        handleScoreSelect(item) {
            // if(){
            //     return console.log('已打分')
            // }
            console.log('item', item)
            this.form.score = item.score
        },
        handleSubmit() {
            if (!this.form.score) return this.$message.error('请选择分数')
            if (!this.form.scoreProcessStageId) return this.$message.error('请选择需要点评的阶段')
            if (!this.form.content) return this.$message.error('请输入点评内容')
            let params = {
                ...this.form,
                teamType: this.teamType,
                drillTaskId: this.detailInfo.drillTaskId,
                processStageId: this.currentStep,
                commentType: '8',//评论类型 1:热搜榜 2:热议话题 3:普通评论 4:情况通报 5:警情通报 6:指令 7:回复评论 8：专家点评
                roleInfo: this.detailInfo.roleInfo,//角色类型
                isCaptain: 0,//是否队长发布（0:否 1:是）

            }
            console.log('this.form', this.form, params)

            drillCommentPublishApi(params).then(res => {
                if (res.code == 200) {
                    this.$message.success('发布成功')
                    this.isScored = true
                }
            })
        },
        handleCancel() {
            this.form.content = ''
            // if(){ // 未打分则清空打分
            //     this.form.score = ''
            // }
        },

        getScoreStatistics() {
            let params = {
                drillTaskId: this.detailInfo.drillTaskId,
                scoreProcessStageId: this.form.scoreProcessStageId,
                teamType: this.teamType,
            }
            drillScoreStatisticsApi(params).then(res => {
                if (res.code == 200) {
                    this.scoreStatistics = res.data
                }
            })
        },
    }
}
</script>
<style scoped lang="scss">
.commentTeam {
    font-family: PingFangSC, PingFang SC;

    .topProcess {
        display: flex;
        justify-content: normal;
        align-items: center;

        .stepBar {
            flex: 1;
            min-width: 0;
            text-align: center;
            cursor: pointer;

            .stepName {
                font-size: 12px;
                height: 2.8em;
                padding: 0 0.5em;
            }

            .bottom-block {
                display: flex;
                justify-content: space-evenly;
                align-items: center;

                .grey-line {
                    width: 100%;
                    border-top: 1px solid #DDDDDD;
                }

                .grey-line-none {
                    width: 100%;
                }

                .point {
                    width: 13px;
                    height: 13px;
                    background-color: #FFFFFF;
                    border-radius: 50%;
                    border: 2px solid #DDDDDD;
                    flex-shrink: 0;
                }
            }
        }

        .active-ba {
            .stepName {
                color: #247CFF;
            }

            .bottom-block {
                .point {
                    border: 2px solid #247CFF;
                }
            }
        }
    }

    .optionsCount {
        margin-top: 1.5em;
        background-color: #EFF8FF;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5em 1em;

        .optionsCount-item {
            display: flex;
            align-items: center;

            .optionsCount-title,
            .optionsCount-num {
                color: #247CFF;
            }
        }
    }

    .expertScoring {
        margin-top: 20px;

        .expertScoring-item {
            background: #E3EEFF;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 7px;
            padding: 10px 10px;

            .scoringRules-score {
                font-size: 18px;
                color: #247CFF;
                width: 3em;
            }

            .scoringRules-desc {
                flex: 1;
            }

            .scoringRules-selectbox {
                border: 1px solid #979797;
                padding: 2px;
                height: min-content;
                margin-left: 5px;
                cursor: pointer;

                .selectboxInside {
                    width: 8px;
                    height: 8px;
                }

                .selected {
                    background: #247CFF;
                }
            }

        }

    }

    .commentContent {
        margin-top: 20px;
    }

    .buttonGroup {
        margin-top: 20px;
        text-align: center;
    }

    .block-title {
        font-weight: 600;
        margin-bottom: 5px;
    }
}

/* 手机横屏模式优化 */
@media screen and (max-width: 992px) and (orientation: landscape) {
    .commentTeam {
        /* 阶段切换优化 */
        .topProcess {
            .stepBar {
                .stepName {
                    font-size: 10px;
                    height: 2.5em;
                    padding: 0 0.3em;
                }

                .bottom-block {
                    .point {
                        width: 10px;
                        height: 10px;
                    }
                }
            }
        }

        /* 操作统计优化 */
        .optionsCount {
            margin-top: 1em;
            padding: 0.3em 0.5em;
            flex-wrap: wrap;
            font-size: 12px;

            .optionsCount-item {
                margin: 2px 5px;
                white-space: nowrap;
            }
        }

        /* 专家打分优化 */
        .expertScoring {
            margin-top: 10px;

            .expertScoring-item {
                margin-top: 5px;
                padding: 5px;

                .scoringRules-score {
                    font-size: 14px;
                    width: 2.5em;
                }

                .scoringRules-desc {
                    font-size: 12px;
                    padding: 0 5px;
                }

                .scoringRules-selectbox {
                    padding: 1px;

                    .selectboxInside {
                        width: 6px;
                        height: 6px;
                    }
                }
            }
        }

        /* 点评内容优化 */
        .commentContent {
            margin-top: 10px;

            ::v-deep .el-textarea__inner {
                font-size: 12px;
                padding: 5px;
            }
        }

        /* 按钮组优化 */
        .buttonGroup {
            margin-top: 10px;

            .el-button {
                padding: 5px 10px;
                font-size: 12px;
            }
        }

        .block-title {
            font-size: 12px;
            margin-bottom: 3px;
        }
    }
}
</style>