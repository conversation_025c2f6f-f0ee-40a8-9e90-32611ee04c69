<template>
  <div class="instruction-body">
    <div class="instruction-Num">{{`指令${commentOrder||''}:`}}</div>
    <div class="instruction-content">{{ content }}</div>
  </div>
</template>

<script>
export default {
  name: 'InstructionTemplate',
  props: {
    commentOrder: {
      type: [String, Number],
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="scss">
.instruction-body {
  font-size: 13em;
  padding: 0.5em 0.5em;
  display: flex;
  align-items: baseline;

  .instruction-Num {
    color: #66FFFF;
    flex-shrink: 0;
    margin-right: 1em;
  }

  .instruction-content {
    overflow-wrap: break-word;
    flex: 1;
    width: 38em;
  }
}
</style>
