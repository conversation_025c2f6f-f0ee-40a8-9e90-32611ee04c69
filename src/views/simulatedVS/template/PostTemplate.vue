<template>
  <div class="template-bltw">
    <div class="post-block">
      <img :src="getAvatar(avatar)" alt="用户头像">
      <div class="main-content">
        <div>
          <div class="user-name">
            <span class="text">{{ nickName }}</span>
            <img src="@/assets/images/simulatedVS/vip7.png" alt="">
            <span style="flex: 1;"></span>
            <img v-if="teamType==2" src="@/assets/images/simulatedVS/teamPointBlue.png" alt="">
            <img v-if="teamType==1" src="@/assets/images/simulatedVS/teamPointRed.png" alt="">
          </div>
          <div class="createTime">{{createTime}}</div>
          <div class="content">{{ content }}</div>
        </div>
        <template v-if="fileList && fileList.length>0">
          <div class="file-list" v-for="itemInside in fileList" :key="itemInside.key"
            :style="{'display': isVideoType(itemInside.type)?'block':'inline-block'}">
            <!-- 图片类型 -->
            <template v-if="isImageType(itemInside.type)">
              <el-image class="img-preview" style="width: 100px; height: 100px" :src="buildFileUrl(itemInside.url)"
                :preview-src-list="[buildFileUrl(itemInside.url)]">
              </el-image>
            </template>

            <!-- 视频类型 -->
            <template v-else-if="isVideoType(itemInside.type)">
              <video style="width: 100%; height: auto;max-height: 20em;" controls>
                <source :src="buildFileUrl(itemInside.url)" :type="getVideoMimeType(itemInside.type)">
                您的浏览器不支持 video 标签
              </video>
            </template>

            <!-- 其他类型 -->
            <template v-else>
              <div class="file-preview">
                <i class="el-icon-document"></i>
                <span>{{ itemInside.name }}</span>
              </div>
            </template>
          </div>
        </template>
        <div class="bottom-icon">
          <div class="icon-item">
            <img src="@/assets/images/simulatedVS/shareIcon.png" alt="">
            <span class="text" style="filter: blur(0.15em);">99999</span>
          </div>
          <div class="icon-item">
            <img src="@/assets/images/simulatedVS/commentIcon.png" alt="" @click="$emit('comment', item)">
            <span class="text" v-if="drillCommentReplyRes">{{ drillCommentReplyRes.length||0 }}</span>
            <span class="text" v-else>{{ 0 }}</span>
          </div>
          <div class="icon-item">
            <img :src="require(isLike?'@/assets/images/simulatedVS/likedIcon.png':'@/assets/images/simulatedVS/likeIcon.png')"
              @click="$emit('like', item)">
            <span class="text">{{ likeCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论区 -->
    <div v-if="drillCommentReplyRes" class="comment-block">
      <div class="comment-block-top">
        <div class="top-left">评论{{ drillCommentReplyRes.length }}</div>
      </div>
      <!-- v-for 循环评论 (按点赞数从大到小排序) -->
      <div class="comment-item" v-for="(replyItem,replyIndex) in sortedComments" :key="replyIndex">
        <img :src="getAvatar(replyItem.avatar)" alt="用户头像">
        <div class="main-content">
          <div>
            <div class="user-name">{{ replyItem.nickName }}</div>
            <div class="content">:&nbsp;{{ replyItem.content }}</div>
          </div>

          <template v-if="replyItem.fileList && replyItem.fileList.length>0">
            <div class="file-list" v-for="itemInside in replyItem.fileList" :key="itemInside.key"
              :style="{'display': isVideoType(itemInside.type)?'block':'inline-block'}">
              <!-- 图片类型 -->
              <template v-if="isImageType(itemInside.type)">
                <el-image class="img-preview" style="width: 100px; height: 100px" :src="buildFileUrl(itemInside.url)"
                  :preview-src-list="[buildFileUrl(itemInside.url)]">
                </el-image>
              </template>

              <!-- 视频类型 -->
              <template v-else-if="isVideoType(itemInside.type)">
                <video style="width: 100%; height: auto;max-height: 20em;" controls>
                  <source :src="buildFileUrl(itemInside.url)" :type="getVideoMimeType(itemInside.type)">
                  您的浏览器不支持 video 标签
                </video>
              </template>

              <!-- 其他类型 -->
              <template v-else>
                <div class="file-preview">
                  <i class="el-icon-document"></i>
                  <span>{{ itemInside.name }}</span>
                </div>
              </template>
            </div>
          </template>
          <div class="comment-bottom">
            <div class="createTime">{{replyItem.createdTime}}</div>
            <div class="option-icon">
              <img :src="require(replyItem.isLike?'@/assets/images/simulatedVS/likedIcon.png':'@/assets/images/simulatedVS/likeIcon.png')"
                alt="" @click="$emit('like', replyItem)">
              <span class="text">{{ replyItem.likeCount||0 }}</span>
            </div>
          </div>
        </div>

        <img class="team-point" v-if="replyItem.teamType==2" src="@/assets/images/simulatedVS/teamPointBlue.png" alt="">
        <img class="team-point" v-else-if="replyItem.teamType==1" src="@/assets/images/simulatedVS/teamPointRed.png" alt="">
        <div class="team-point" v-else></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PostTemplate',
  computed: {
    // 按点赞数从大到小排序评论
    sortedComments() {
      if (!this.drillCommentReplyRes || this.drillCommentReplyRes.length === 0) {
        return [];
      }
      // 创建数组副本并按点赞数排序
      return [...this.drillCommentReplyRes].sort((a, b) => {
        // 将可能的undefined或null转换为0
        const likeCountA = a.likeCount || 0;
        const likeCountB = b.likeCount || 0;
        // 降序排列（从大到小）
        return likeCountB - likeCountA;
      });
    }
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    avatar: {
      type: String,
      default: ''
    },
    nickName: {
      type: String,
      default: ''
    },
    createTime: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    teamType: {
      type: [Number, String],
      default: 0
    },
    fileList: {
      type: Array,
      default: () => []
    },
    drillCommentReplyRes: {
      type: Array,
      default: () => []
    },
    isLike: {
      type: Boolean,
      default: false
    },
    likeCount: {
      type: [Number, String],
      default: 0
    },
    imageTypes: {
      type: Array,
      default: () => ['png', 'jpeg', 'jpg']
    },
    videoTypes: {
      type: Array,
      default: () => ['mp4', 'avi', 'rmvb', 'mov', 'mkv']
    }
  },
  methods: {
    // 判断是否是当前允许的图片类型
    isImageType(fileType) {
      return this.imageTypes.includes(fileType)
    },
    // 判断是否是当前允许的视频类型
    isVideoType(fileType) {
      return this.videoTypes.includes(fileType)
    },
    // 获取视频MIME类型
    getVideoMimeType(ext) {
      const types = {
        mp4: 'video/mp4',
        avi: 'video/x-msvideo',
        mov: 'video/quicktime',
        mkv: 'video/x-matroska',
        rmvb: 'application/vnd.rn-realmedia-vbr'
      }
      return types[ext] || 'video/*'
    },
    buildFileUrl(url) {
      return process.env.VUE_APP_BASE_API + url;
    },
    // 添加头像方法
    getAvatar(avatar) {
      return avatar ? process.env.VUE_APP_BASE_API + avatar : require("@/assets/images/profile.jpg");
    }
  }
}
</script>

<style scoped lang="scss">
.template-bltw {
  // font-size: 0.8em;
  font-size: 1.1em;
  background-color: #FFFFFF;

  .post-block {
    font-size: 12em;
    display: flex;
    padding: 1em;
    padding-bottom: 0;

    >img {
      width: 3em;
      height: 3em;
      aspect-ratio: 1;
      flex-shrink: 0;
      margin-right: 1em;
      border-radius: 50%;
    }

    .main-content {
      color: #000000;
      width: calc(100% - 6em);

      .user-name {
        // margin-bottom: 0.5em;
        display: flex;
        align-items: center;

        .text {
          font-weight: 600;
          // filter: blur(0.15em);
          margin-right: 1em;
        }

        img {
          width: 1.5em;
          height: 1.5em;
          aspect-ratio: 1;
          flex-shrink: 0;
        }
      }

      .content {
        margin-bottom: 0.5em;
        word-break: break-word;
        white-space: pre-wrap;
      }

      .file-list {
        display: inline-block;

        .img-preview {
          width: 10em;
          height: 10em;
          margin-right: 1em;
        }
      }

      .bottom-icon {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 1em;
        margin-bottom: 1em;

        .icon-item {
          display: flex;
          align-items: center;

          span {
            color: #666666;
          }

          img {
            width: 1.4em;
            margin-right: 0.4em;
            cursor: pointer;
          }
        }
      }
    }
  }

  .comment-block {
    .comment-block-top {
      width: 100%;
      border-bottom: 0.01em solid #EEEEEE;

      .top-left {
        color: #333333;
        font-size: 14em;
        font-weight: 600;
        border-bottom: 0.2em solid #EB7350;
        padding: 0.4em;
        margin-bottom: 0.1em;
        margin-left: 3em;
        width: fit-content;
      }
    }

    .comment-item {
      font-size: 12em;
      background-color: #FFFFFF;
      display: flex;
      padding: 0.5em 1em;

      >img {
        width: 3em;
        height: 3em;
        aspect-ratio: 1;
        flex-shrink: 0;
        border-radius: 50%;
      }

      .main-content {
        color: #000000;
        padding: 0 1em;
        flex: 1;

        .user-name {
          // color: #646464;
          color: #eb7350;
          // filter: blur(0.15em);
          display: inline-block;
          margin-right: 1em;
        }

        .content {
          margin-bottom: 0.5em;
          word-break: break-word;
          white-space: pre-wrap;
          display: inline;
        }

        .file-list {
          display: inline-block;

          .img-preview {
            width: 10em;
            height: 10em;
            margin-right: 1em;
          }
        }

        .comment-bottom {
          display: flex;
          align-items: end;
          justify-content: space-between;

          .option-icon {
            display: flex;
            align-items: center;

            >img {
              width: 1.3em;
              margin-right: 0.5em;
              cursor: pointer;
            }

            .text {
              color: #999999;
            }
          }
        }
      }

      .team-point {
        width: 1.5em;
        height: 1.5em;
        aspect-ratio: 1;
        flex-shrink: 0;
        margin-right: 1em;
      }
    }
  }

  .createTime {
    font-family: SourceHanSansCN, SourceHanSansCN;
    font-size: 0.9em;
    color: #999999;
  }
}
</style>
