<template>
  <div class="template3">
    <img :src="getAvatar(avatar)" alt="用户头像">
    <div class="main-content">
      <div>
        <div class="user-name">{{ nickName }}</div>
        <div class="content">:{{ content }}</div>
      </div>
      <template v-if="fileList && fileList.length>0">
        <div class="file-list" v-for="itemInside in fileList" :key="itemInside.key"
          :style="{'display': isVideoType(itemInside.type)?'block':'inline-block'}">
          <!-- 图片类型 -->
          <template v-if="isImageType(itemInside.type)">
            <el-image class="img-preview" style="width: 100px; height: 100px" :src="buildFileUrl(itemInside.url)"
              :preview-src-list="[buildFileUrl(itemInside.url)]">
            </el-image>
          </template>

          <!-- 视频类型 -->
          <template v-else-if="isVideoType(itemInside.type)">
            <video style="width: 100%; height: auto;max-height: 20em;" controls>
              <source :src="buildFileUrl(itemInside.url)" :type="getVideoMimeType(itemInside.type)">
              您的浏览器不支持 video 标签
            </video>
          </template>

          <!-- 其他类型 -->
          <template v-else>
            <div class="file-preview">
              <i class="el-icon-document"></i>
              <span>{{ itemInside.name }}</span>
            </div>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommentTemplate',
  props: {
    avatar: {
      type: String,
      default: ''
    },
    nickName: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    fileList: {
      type: Array,
      default: () => []
    },
    imageTypes: {
      type: Array,
      default: () => ['png', 'jpeg', 'jpg']
    },
    videoTypes: {
      type: Array,
      default: () => ['mp4', 'avi', 'rmvb', 'mov', 'mkv']
    }
  },
  methods: {
    // 判断是否是当前允许的图片类型
    isImageType(fileType) {
      return this.imageTypes.includes(fileType)
    },
    // 判断是否是当前允许的视频类型
    isVideoType(fileType) {
      return this.videoTypes.includes(fileType)
    },
    // 获取视频MIME类型
    getVideoMimeType(ext) {
      const types = {
        mp4: 'video/mp4',
        avi: 'video/x-msvideo',
        mov: 'video/quicktime',
        mkv: 'video/x-matroska',
        rmvb: 'application/vnd.rn-realmedia-vbr'
      }
      return types[ext] || 'video/*'
    },
    buildFileUrl(url) {
      return process.env.VUE_APP_BASE_API + url;
    },
    // 添加头像方法
    getAvatar(avatar) {
      return avatar ? process.env.VUE_APP_BASE_API + avatar : require("@/assets/images/profile.jpg");
    }
  }
}
</script>

<style scoped lang="scss">
.template3 {
  font-size: 12em;
  background-color: #FFFFFF;
  display: flex;
  padding: 1em;

  >img {
    width: 5em;
    height: 5em;
    aspect-ratio: 1;
    flex-shrink: 0;
    margin-right: 1em;
    border-radius: 50%;
  }

  .main-content {
    color: #000000;
    width: calc(100% - 6em);

    .user-name {
      // color: #646464;
      color: #eb7350;
      // filter: blur(0.15em);
      display: inline-block;
      margin-right: 1em;
    }

    .content {
      margin-bottom: 0.5em;
      word-break: break-word;
      white-space: pre-wrap;
      display: inline;
    }

    .file-list {
      display: inline-block;

      .img-preview {
        width: 10em;
        height: 10em;
        margin-right: 1em;
      }
    }
  }
}
</style>
