<template>
  <div class="app-container home" :style="backgroundStyle" id="fullscreenContent" ref="dataScreenRef">
    <div class="home-content">
      <div class="home-head">
        <img :src="headBgImage" alt="">
        <div class="head-title"> {{titleName||'博约舆情'}}舆情态势总览</div>
        <div class="date">{{currentTime}}</div>
        <div class="switch">
          <el-switch
            style="display: block"
            v-model="switchTheme"
            @change="changeTheme"
            active-color="#57CDFF"
            inactive-color="#247CFF">
          </el-switch>
        </div>
        <img
          v-if="isFull"
          class="full-icon"
          src="@/assets/images/full-screen.png"
          alt="" @click="toggleFullScreen">
        <img v-else class="full-icon" src="@/assets/images/out-full-screen.png" alt="" @click="outFullScreen">
        <div :class="switchTheme?'timeRange':'time-light-range'">
          时间：
          <el-select @change="dataSearch" :popper-class="switchTheme?'timeSelect':''" v-model="queryParams.timeType"
                     style="width:90px" size="mini" placeholder="请选择">
            <el-option
              v-for="item in dateArr"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-radio-group v-model="queryParams.timeType" size="small"  @change="changeDate">
            <el-radio-button
            class="btn"
            v-for="(item,index) in dateArr"
            :key="index" :label="item.value">{{ item.label }}</el-radio-button>
          </el-radio-group> -->
          <!-- <el-button class="search-btn" size="mini" @click="dataSearch">查询</el-button> -->
        </div>
      </div>
      <!-- 内容 -->
      <div class="home-contain">
        <!-- 左 -->
        <div class="home-lf">
          <!-- 预警信息 -->
          <div class="focus-case alert-message">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">预警信息</span>
                <span class="light-text" v-else>预警信息</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'"
                    @click="handleDetail('alert')">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg">
              <div class="deal-title">
                <span>序号</span>
                <span style="width:66%">预警信息</span>
                <span>来源</span>
              </div>
              <div v-loading="warnLoading" @click="viewRecord($event)" element-loading-background="rgba(18, 42, 96,0)"
                   style="height:100%;">
                <vue-seamless-scroll
                  :step="0.1"
                  hover="true"
                  :data="warnList"
                  :class-option="defaultOption(warnList)"
                  class="warnless-warp">
                  <div class="deal-wrap">
                    <div v-for="(item,index) in warnList" :key="index" class="deal-main">
                      <div class="deal-title-pre">
                        <span>{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                      </div>
                      <el-tooltip placement="top" effect="light" :content="item.title" :raw-content="true">
                        <span :data="JSON.stringify(item)" class="deal-text-left warnItem">{{item.title}}</span>
                      </el-tooltip>
                      <span>{{ item.typeName }}</span>
                    </div>
                    <div v-if="warnList.length ==0 && !warnLoading" class="noneData">
                      <img :src="noDataImg" alt="">
                      <div>暂无数据</div>
                    </div>
                  </div>
                </vue-seamless-scroll>
              </div>
            </div>
          </div>
          <!-- 全国政法热点 -->
          <div class="focus-case">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">全国政法热点</span>
                <span class="light-text" v-else>全国政法热点</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'" @click="handleMore(0)">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg">
              <div class="deal-title">
                <span>序号</span>
                <span style="width:66%">热点内容</span>
                <span>热度</span>
              </div>
              <div class="deal-wrap" v-loading="hotLoading" element-loading-background="rgba(18, 42, 96,0)">
                <div v-for="(item,index) in countryList" :key="index" class="deal-main">
                  <div class="deal-title-pre">
                    <span>{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                  </div>
                  <el-tooltip :content="item.title" placement="top" effect="light" :raw-content="true">
                    <span @click="goOrigin(item)" class="deal-text-left">{{item.title}}</span>
                  </el-tooltip>
                  <el-tooltip :content="String(item.indexNum)" placement="top" effect="light" :raw-content="true">
                          <span
                            :style="index + 1 > 3 ? { paddingLeft: '0.23rem' } : { textAlign: 'center', padding:'0 0.05rem' }">
                            <img v-if="index+1 == 1" src="@/assets/images/hot.png" alt="" class="fire">
                            <img v-if="index+1 == 2" src="@/assets/images/warm.png" alt="" class="fire">
                            <img v-if="index+1 == 3" src="@/assets/images/cold.png" alt="" class="fire">
                            {{ item.indexNum }}
                          </span>
                  </el-tooltip>
                </div>
                <!--  -->
                <div v-if="countryList.length ==0 && !hotLoading" class="noneData">
                  <img :src="noDataImg" alt="">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 浙江政法热点 -->
          <div class="focus-space"></div>
          <div class="focus-case focus-ct">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">浙江政法热点</span>
                <span class="light-text" v-else>浙江政法热点</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <!-- <span :class="switchTheme?'home-title-more':'home-title-more-light'" @click="handleDetail('law')">更多>></span> -->
              <span :class="switchTheme?'home-title-more':'home-title-more-light'" @click="handleMore(11)">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg">
              <div class="deal-title">
                <span>序号</span>
                <span style="width:66%">热点内容</span>
                <span>热度</span>
              </div>
              <div class="deal-wrap" v-loading="zjLoading" element-loading-background="rgba(18, 42, 96,0)">
                <div v-for="(item,index) in zjList" :key="index" class="deal-main">
                  <div class="deal-title-pre">
                    <span>{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                  </div>
                  <el-tooltip placement="top" effect="light" :raw-content="true">
                    <div slot="content"><span v-html="item.title"></span></div>
                    <span class="deal-text-left" @click="goOrigin(item)" v-html="item.title"></span>
                  </el-tooltip>
                  <el-tooltip :content="String(item.indexNum)" placement="top" effect="light" :raw-content="true">
                        <span
                          :style="index + 1 > 3 ? { paddingLeft: '0.23rem' } : { textAlign: 'center', padding:'0 0.05rem' }">
                          <img v-if="index+1 == 1" src="@/assets/images/hot.png" alt="" class="fire">
                          <img v-if="index+1 == 2" src="@/assets/images/warm.png" alt="" class="fire">
                          <img v-if="index+1 == 3" src="@/assets/images/cold.png" alt="" class="fire">
                          {{ item.indexNum }}
                        </span>
                  </el-tooltip>
                </div>
                <div v-if="zjList.length ==0 && !zjLoading" class="noneData">
                  <img :src="noDataImg" alt="">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 微博热搜 -->
          <div class="focus-case focus-ct">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">微博热搜</span>
                <span class="light-text" v-else>微博热搜</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'" @click="handleMore(4)">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg">
              <div class="deal-title">
                <span>排名</span>
                <span style="width:66%">文章名称</span>
                <span>指数</span>
              </div>
              <div class="deal-wrap" v-loading="weiboLoading" element-loading-background="rgba(18, 42, 96,0)">
                <div v-for="(item,index) in newsData" :key="index" class="deal-main">
                  <div class="deal-title-pre">
                    <img v-if="index+1 == 1" src="@/assets/images/champion.png" alt="" class="fire">
                    <img v-if="index+1 == 2" src="@/assets/images/runner.png" alt="" class="fire">
                    <img v-if="index+1 == 3" src="@/assets/images/second-runner.png" alt="" class="fire">
                    <span v-if="index+1>3">{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                  </div>
                  <el-tooltip :content="item.title" placement="top" effect="light" :raw-content="true">
                    <span class="deal-text-left" @click="goOrigin(item)">{{item.title}}</span>
                  </el-tooltip>
                  <el-tooltip :content="String(item.indexNum)" placement="top" effect="light" :raw-content="true">
                        <span :style="{ textAlign: 'center', padding:'0 0.05rem' }">
                          {{ item.indexNum }}
                        </span>
                  </el-tooltip>
                </div>
                <div v-if="newsData.length ==0 && !weiboLoading" class="noneData">
                  <img :src="noDataImg" alt="">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
          <!-- 情感分布 -->
          <!-- <div class="emtion-style">
            <div class="section-title" :style="subTitleBg">
              <span class="light-spot" v-if="!switchTheme"></span>
              <span class="text" v-if="switchTheme" @click="goToExpendDetail('情感分布','全部',undefined)">情感分布</span>
              <span class="light-text" v-else @click="goToExpendDetail('情感分布','全部',undefined)">情感分布</span>
              <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
            </div>
            <div class="emtion-chart" :style="emtionBg" v-loading="emtionLoading" element-loading-background="rgba(18, 42, 96,0)">
              <emtion-move v-if="switchTheme" :chartData="pieData" @goToExpendDetail="goToExpendDetail"></emtion-move>
              <light-emtion v-else :chartData="pieData" @goToExpendDetail="goToExpendDetail"></light-emtion>
            </div>
          </div> -->
        </div>
        <!-- 中 -->
        <div class="home-ct">
          <!-- 数据总量 -->
          <div class="databaseTotol" v-if="switchTheme">
              <span class="databaseTotol_title">
                涉{{titleName||'博约'}}
                <br>
                舆情信息量
                </span>
            <span class="databaseTotol_num"
                  @click="goToExpendDetail(`涉${titleName||'博约'}舆情信息量`,'全部',allcountData)">
                <Flipper ref="flipperRef"></Flipper>
              </span>
          </div>
          <!-- light-数据总量 -->
          <div class="light-Total" v-else>
            <span>涉{{titleName||'博约'}}舆情信息量：</span>
            <div @click="goToExpendDetail(`涉${titleName||'博约'}舆情信息量`,'全部',allcountData)"><span
              class="total-number">{{ allcountData || 0}}</span></div>
          </div>

          <!-- 重点关注事件 -->
          <div :class="switchTheme?'point-case point-case-black':'point-case point-case-light'">
            <div class="case-box">
              <p>重点关注事件数量<span class="case-point-num" v-if="provinceList"
                                       @click="goToExpendDetail('关注',provinceList[0].name,provinceList[0].count,undefined,undefined,undefined,provinceList[0].historyFlag)">{{provinceList.length>0?provinceList[0].count:0}}</span>
                <span class="case-unit">件</span>
              </p>
              <img v-if="switchTheme" src="@/assets/images/point.png" alt="">
              <img v-else src="@/assets/images/point-light.png" alt="">
            </div>
          </div>
          <!-- 信息趋势图 -->
          <div class="focus-case">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">信息趋势图</span>
                <span class="light-text" v-else>信息趋势图</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'"
                    @click="goToExpendDetail('信息趋势','全部')">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg" v-loading="lineLoading"
                 element-loading-background="rgba(18, 42, 96,0)">
              <week-public class="line-style" style="height:100%" :switchTheme="switchTheme" :chartData="lineData"
                           @goToExpendDetail="goToExpendDetail"></week-public>
            </div>
          </div>
          <!-- 活跃账号 -->
          <!-- <div class="home-space-two"></div>
          <div class="active-account">
            <div class="section-title" :style="subTitleBg">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme" @click="handleDetail('account')">活跃账号</span>
                <span class="light-text" v-else @click="handleDetail('account')">活跃账号</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
            </div>
            <div class="account-chart" :style="activeBg" v-loading="accountLoading" element-loading-background="rgba(18, 42, 96,0)" >
              <active-account style="height:100%;" :switchTheme="switchTheme" :chartData="accountData" @goToExpendDetail="goToExpendDetail"></active-account>
            </div>
          </div> -->
          <!-- 热词云 -->
          <div class="cloud-data">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">热词云</span>
                <span class="light-text" v-else>热词云</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'"
                    @click="handleDetail('words')">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg" v-loading="cloudLoading"
                 element-loading-background="rgba(18, 42, 96,0)">
              <!-- {{cloudData}} -->
              <cloud-chart style="height:100%;" ref="cloud" :switchTheme="switchTheme" :chartData="cloudData"
                           @filterCloud="filterLists" @goToExpendDetail="goToExpendDetail"></cloud-chart>
              <!-- <div v-if="cloudData.length ==0 && !cloudLoading" class="noneData">
               <img src="@/assets/images/nodata.png" alt="">
               <div>暂无数据</div>
             </div> -->
            </div>
          </div>
          <!-- 抖音热搜 -->
          <div class="focus-case focus-ct">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">抖音热搜</span>
                <span class="light-text" v-else>抖音热搜</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'" @click="handleMore(6)">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg">
              <div class="deal-title">
                <span>排名</span>
                <span style="width:66%">文章名称</span>
                <span>指数</span>
              </div>
              <div class="deal-wrap" v-loading="douyinLoading" element-loading-background="rgba(18, 42, 96,0)">
                <div v-for="(item,index) in hotData" :key="index" class="deal-main">
                  <div class="deal-title-pre">
                    <img v-if="index+1 == 1" src="@/assets/images/champion.png" alt="" class="fire">
                    <img v-if="index+1 == 2" src="@/assets/images/runner.png" alt="" class="fire">
                    <img v-if="index+1 == 3" src="@/assets/images/second-runner.png" alt="" class="fire">
                    <span v-if="index+1>3">{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                  </div>
                  <el-tooltip :content="item.title" placement="top" effect="light" :raw-content="true">
                    <span class="deal-text-left" @click="goOrigin(item)">{{item.title}}</span>
                  </el-tooltip>
                  <el-tooltip :content="String(item.indexNum)" placement="top" effect="light" :raw-content="true">
                        <span :style=" { textAlign: 'center', padding:'0 0.05rem' }">
                          {{ item.indexNum }}
                        </span>
                  </el-tooltip>
                </div>
                <div v-if="hotData.length ==0 && !douyinLoading" class="noneData">
                  <img :src="noDataImg" alt="">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 右 -->
        <div class="home-rg">
          <!-- 辖区总览 -->
          <div class="focus-case alert-message">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">辖区总览</span>
                <span class="light-text" v-else>辖区总览</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'"
                    @click="handleDetail('area')">更多>></span>
            </div>
            <div class="area-list" :style="trendBg">
              <div class="area-main-title">
                <div class="deal-title main-title-line">
                  <span>区域</span>
                  <span>舆情总量</span>
                </div>
                <div class="area-line"></div>
                <div class="deal-title">
                  <span>区域</span>
                  <span>舆情总量</span>
                </div>
              </div>
              <div class="area-mainer">
                <div class="area-main-wrap area-main-line">
                  <div :class="switchTheme?'deal-wrap area-deal-border':'deal-wrap area-deal-border-light'"
                       v-loading="areaLoading" element-loading-background="rgba(18, 42, 96,0)">
                    <div v-for="(item,index) in areaOneList" :key="index" class="deal-main">
                      <span class="deal-text-left" @click="goToExpendDetail('辖区总览', item.areaName, item.news)">{{item.areaName}}</span>
                      <el-tooltip :content="String(item.total)" placement="top" effect="light" :raw-content="true">
                        <span class="area-total" @click="goToExpendDetail('辖区总览', item.areaName, item.news)">{{item.total}}</span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
                <div class="area-main-wrap">
                  <div class="deal-wrap" v-loading="areaLoading" element-loading-background="rgba(18, 42, 96,0)">
                    <div v-for="(item,index) in areaTwoList" :key="index" class="deal-main">
                      <span @click="goToExpendDetail('辖区总览', item.areaName, item.news)">{{item.areaName}}</span>
                      <el-tooltip :content="String(item.total)" placement="top" effect="light" :raw-content="true">
                        <span class="area-total" @click="goToExpendDetail('辖区总览', item.areaName, item.news)">{{item.total}}</span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 重点账号 -->
          <div class="focus-case">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">重点账号</span>
                <span class="light-text" v-else>重点账号</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'"
                    @click="handleDetail('pointAccount')">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg">
              <div class="point-main">
                <div class="point-wrap" v-loading="keyLoading" element-loading-background="rgba(18, 42, 96,0)">
                  <div :class="switchTheme?'point-box point-box-dark':'point-box point-box-light'"
                       v-for="(item,index) in keyList" :key="index">
                    <img v-if="item.avatar" :src="baseUrl+'/common/url/image?url='+item.avatar" alt="">
                    <img v-else src="@/assets/images/badPicture.png" alt="">
                    <div class="point-content"
                         @click="goToExpendDetail('重点账号',item.name,allcountData,item.type,item.videoHost,0)">
                      <!-- <div class="point-name">{{item.name}}</div> -->
                      <el-tooltip :content="item.name" placement="top" effect="light" :raw-content="true">
                        <div class="point-name">{{item.name}}</div>
                      </el-tooltip>
                      <div class="point-source">
                        来源：{{ item.source }}
                      </div>
                    </div>
                  </div>
                  <div v-if="keyList.length ==0 && !keyLoading" class="noneData">
                    <img :src="noDataImg" alt="">
                    <div>暂无数据</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 重点账号动态 -->
          <div class="right-three">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">重点账号动态</span>
                <span class="light-text" v-else>重点账号动态</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'"
                    @click="handleDetail('point')">更多>></span>
            </div>
            <div class="trend-list deal-list" :style="trendBg">
              <div class="trend-title deal-title ">
                <span>序号</span>
                <span style="width:50%">内容</span>
                <span>律师</span>
                <span>来源</span>
              </div>
              <div v-loading="keyLoading" @click="viewKey($event)" element-loading-background="rgba(18, 42, 96,0)"
                   style="height:100%;">
                <vue-seamless-scroll
                  :step="0.1"
                  hover="true"
                  :data="keyDymaticList"
                  :class-option="defaultOption(keyDymaticList)"
                  class="seamless-warp">
                  <div class="trend-wrap">
                    <div v-for="(item,index) in keyDymaticList" :key="index" class="trend-main">
                      <div class="deal-title-pre">
                        <span>{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                      </div>
                      <el-tooltip placement="top" effect="light">
                        <div slot="content" class="text_warp">
                          {{ item.content }}
                        </div>
                        <div :data="JSON.stringify(item)" class="text_els keyItem">
                          {{ item.content }}
                        </div>
                      </el-tooltip>
                      <el-tooltip :content="item.name" placement="top" effect="light" :raw-content="true">
                        <span style="cursor:pointer;" class="dymaticItem"
                              :dymatic="JSON.stringify(item)">{{item.name}}</span>
                      </el-tooltip>
                      <span>{{item.source}}</span>
                    </div>
                    <div v-if="keyDymaticList.length ==0 && !keyLoading" class="noneData">
                      <img :src="noDataImg" alt="">
                      <div>暂无数据</div>
                    </div>
                  </div>
                </vue-seamless-scroll>
              </div>
            </div>
          </div>
          <!-- 百度热搜 -->
          <div class="focus-case focus-ct">
            <div class="section-title" :style="subTitleBg">
              <div class="focus-center">
                <span class="light-spot" v-if="!switchTheme"></span>
                <span class="text" v-if="switchTheme">百度热搜</span>
                <span class="light-text" v-else>百度热搜</span>
                <img v-if="switchTheme" class="go-image" :src="subImage" alt="">
              </div>
              <span :class="switchTheme?'home-title-more':'home-title-more-light'" @click="handleMore(8)">更多>></span>
            </div>
            <div class="deal-list" :style="trendBg">
              <div class="deal-title">
                <span>排名</span>
                <span style="width:66%">文章名称</span>
                <span>指数</span>
              </div>
              <div class="deal-wrap" v-loading="baiduLoading" element-loading-background="rgba(18, 42, 96,0)">
                <div v-for="(item,index) in opinionRealTimeData" :key="index" class="deal-main">
                  <div class="deal-title-pre">
                    <img v-if="index+1 == 1" src="@/assets/images/champion.png" alt="" class="fire">
                    <img v-if="index+1 == 2" src="@/assets/images/runner.png" alt="" class="fire">
                    <img v-if="index+1 == 3" src="@/assets/images/second-runner.png" alt="" class="fire">
                    <span v-if="index+1>3">{{ index < 9 ? 0 : null}}{{ index + 1 }}</span>
                  </div>
                  <el-tooltip :content="item.title" placement="top" effect="light" :raw-content="true">
                    <span class="deal-text-left" @click="goOrigin(item)">{{item.title}}</span>
                  </el-tooltip>
                  <el-tooltip :content="String(item.indexNum)" placement="top" effect="light" :raw-content="true">
                        <span :style=" { textAlign: 'center', padding:'0 0.05rem' }">
                          {{ item.indexNum }}
                        </span>
                  </el-tooltip>
                </div>
                <div v-if="opinionRealTimeData.length ==0 && !baiduLoading" class="noneData">
                  <img :src="noDataImg" alt="">
                  <div>暂无数据</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import dayjs from "dayjs";
import {beforeTime} from '@/utils/time.js'
import EmtionMove from './home/<USER>/EmtionMove.vue';
import Flipper from './home/<USER>/Flipper.vue'
import weekPublic from './home/<USER>/weekPublic.vue';
import ActiveAccount from './home/<USER>/ActiveAccount.vue';
import CloudChart from './home/<USER>/CloudChart.vue';
import LightEmtion from './home/<USER>/LightEmtion.vue'
import variables from '@/assets/styles/variables.scss'
import screenfull from 'screenfull'
import {getInfo} from '@/api/login'
import {
  homeWarn,
  keyAccounts,
  hotZJ,
  getEmontionStatis,
  getWordsAnalyse,
  getTotal,
  getCurve,
  getAreaOverview,
  getHabit,
  setHabit,
  getHotPlan,
  DeleteHotWord
} from '@/api/home/<USER>'
import {hotList, hotRankList} from "@/api/search/index";

export default {
  name: "index",
  components: {EmtionMove, Flipper, weekPublic, ActiveAccount, CloudChart, LightEmtion},
  data() {
    const now = dayjs(); // 获取当前时间
    const weekdays = ['日', '一', '二', '三', '四', '五', '六']; // 映射星期几的数字到名称
    const weekdayIndex = now.day(); // 获取星期几的数字（0-6）
    const weekdayName = weekdays[weekdayIndex]; // 获取星期几的名称
    return {
      baseUrl: process.env.VUE_APP_BASE_API,
      warnList: [],
      warnLoading: false,
      keyList: [],
      keyDymaticList: [],
      keyLoading: false,
      zjList: [],
      zjLoading: false,
      areaOneList: [],
      areaTwoList: [],
      weiboLoading: false,
      newsData: [],
      douyinLoading: false,
      hotData: [],
      baiduLoading: false,
      opinionRealTimeData: [],
      currentTheme: 'dark',
      homeRef: null,
      currentTime: `${now.format("YYYY/MM/DD HH:mm:ss")}   星期${weekdayName}`,
      timer: null, // 计时器 ID，用于清除计时器
      switchTheme: true,
      isFull: true,
      dateArr: [
        {
          label: '今天',
          value: '1'
        },
        {
          label: '24小时',
          value: '2'
        },
        {
          label: '7天',
          value: '3'
        },
        {
          label: '30天',
          value: '4'
        }
      ],
      queryParams: {
        startTime: beforeTime.oneDay.split(',')[0],
        endTime: beforeTime.oneDay.split(',')[1],
        timeType: '2'
      },
      startTime: '',
      endTime: '',
      startOption: {
        disabledDate: this.startDisable
      },
      endOption: {
        disabledDate: this.endDisable
      },
      allcountData: 0,
      mainLoading: false,
      hotLoading: false,
      emtionLoading: false,
      lineLoading: false,
      accountLoading: false,
      cloudLoading: false,
      provinceLoading: false,
      areaLoading: false,
      trendLoading: false,
      pieData: [],
      lineData: {},
      accountData: {},
      cloudData: [],
      trendData: [],
      caseList: [],
      areaList: [],
      countryList: [],
      provinceList: [],
      userId: null,
      keyTypes: '',
      videos: ''
    };
  },
  computed: {
    titleName() {
      return this.$store.state.user.titleName
    },
    defaultOption() {
      return (data) => {
        return {
          step: 0.3, // 数值越大速度滚动越快
          limitMoveNum: 8, // 开始无缝滚动的数据量 this.dataList.length
          hoverStop: true, // 是否开启鼠标悬停stop
          direction: 1, // 0向下 1向上 2向左 3向右
          openWatch: true, // 开启数据实时监控刷新dom
          singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
          singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
          waitTime: 0 // 单步运动停止的时间(默认值1000ms)
        }
      }
    },
    backgroundStyle() {
      return this.switchTheme ?
        {backgroundImage: `url(${require("@/assets/images/background.jpg")})`}
        :
        // {background:'#F4F7FB'}
        {backgroundImage: `url(${require("@/assets/images/home-light-bg.png")})`}
    },
    // 头部背景图片
    headBgImage() {
      return this.switchTheme ?
        require('@/assets/images/head-title.png')
        :
        require('@/assets/images/light-head-title.png')
    },
    // 小标题背景图片
    subTitleBg() {
      return this.switchTheme ?
        {backgroundImage: `url(${require('@/assets/images/sub-bg.png')})`}
        :
        {background: 'none'}
    },
    // 小标题图片
    subImage() {
      return this.switchTheme ? require('@/assets/images/ongoing.png') : ''
    },
    // 小标题内容背景
    subContentBg() {
      return this.switchTheme ?
        {backgroundImage: `url(${require('@/assets/images/one.png')})`} :
        {backgroundImage: `url(${require('@/assets/images/one-light.png')})`}
    },
    // 情感分布内容bg
    emtionBg() {
      return this.switchTheme ?
        {backgroundImage: `url(${require('@/assets/images/emtion-bg.png')})`} :
        {backgroundImage: `url(${require('@/assets/images/emtion-bg-light.png')})`}
    },
    // 活跃账号内容bg
    activeBg() {
      return this.switchTheme ?
        {backgroundImage: `url(${require('@/assets/images/one.png')})`} :
        {backgroundImage: `url(${require('@/assets/images/one-light.png')})`}
    },
    // 热词内容bg
    cloudBg() {
      return this.switchTheme ?
        {backgroundImage: `url(${require('@/assets/images/cloud-bg.png')})`} :
        {backgroundImage: `url(${require('@/assets/images/cloud-bg-light.png')})`}
    },
    // 辖区内容bg
    areaBg() {
      return this.switchTheme ?
        {backgroundImage: `url(${require('@/assets/images/area-bg.png')})`} :
        {backgroundImage: `url(${require('@/assets/images/area-bg-light.png')})`}
    },
    // 律师内容bg
    trendBg() {
      return this.switchTheme ?
        {backgroundImage: `url(${require('@/assets/images/trend-bg.png')})`} :
        {backgroundImage: `url(${require('@/assets/images/trend-bg-light.png')})`}
    },
    // 缺省图
    noDataImg() {
      return this.switchTheme ? require('@/assets/images/nodata.png') : require('@/assets/images/none.png')
    }
  },
  mounted() {
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    // 在组件挂载后设置计时器
    this.timer = setInterval(() => {
      const now = dayjs();
      const weekdayIndex = now.day();
      const weekdayName = weekdays[weekdayIndex];
      this.currentTime = `${dayjs().format("YYYY/MM/DD HH:mm:ss")}   星期${weekdayName}`;
    }, 1000);
    this.getCountData()
    this.getDataInterval = setInterval(() => {
      this.getCountData()
    }, 60000)
    // 设置主题色
    window.document.documentElement.setAttribute('data-theme', this.currentTheme)
  },
  created() {
    this.getHabitTheme()
    this.getHot() // 政法热点
    this.EchartsList()
    getInfo().then(res => {
      this.userId = res.user.userId
    })
    this.platWeiboList() // 微博热搜
    this.platDouyinList() // 抖音热搜
    this.platBaiduList() // 百度热搜
  },
  beforeDestroy() {
    // 在组件销毁前清除计时器和移除事件监听器
    clearInterval(this.timer);
  },
  methods: {
    // 修复滚动组件点击事件不生效bug
    viewRecord(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /warnItem/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data")); // 单项数据详情，点击那行数据的所有数据
      // 跳转详情页面
      this.goOrigin(data)
    },
    viewKey(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /keyItem/.test(r.className));
      if (target.length) {
        target = target[0];
        const data = JSON.parse(target.getAttribute("data")); // 单项数据详情，点击那行数据的所有数据
        // 跳转详情页面
        this.goOrigin(data)
      }
      let dymatic = path.filter((r) => /dymaticItem/.test(r.className));
      if (dymatic.length) {
        dymatic = dymatic[0]
        const dymaticData = JSON.parse(dymatic.getAttribute("dymatic"));
        this.goToExpendDetail('重点账号动态', dymaticData.name, this.allcountData, dymaticData.type, dymaticData.videoHost, 0)
      }
    },
    // 预警信息
    async queryWarn() {
      try {
        this.warnLoading = true
        let res = await homeWarn({
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
          timeType: this.queryParams.timeType
        })
        this.warnList = res.data
      } finally {
        this.warnLoading = false
      }
    },
    // 预警信息跳转
    handleAlert() {

    },
    // 重点账号/动态
    async queryKeyAccount() {
      try {
        this.keyLoading = true
        let res = await keyAccounts({
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
          timeType: this.queryParams.timeType
        })
        this.keyList = res.data.lawyer

        this.videos = res.data.lawyer.map(item => item.videoHost).join(',')

        this.keyTypes = res.data.lawyer.map(item => item.type).join(',')
        console.log('this.videos :>> ', this.videos);
        console.log('this.keyTypes :>> ', this.keyTypes);
        this.keyDymaticList = res.data.lawyerMoment
      } finally {
        this.keyLoading = false
      }
    },
    // 浙江政法热点
    async queryZjList() {
      try {
        this.zjLoading = true
        let res = await hotZJ({
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
          timeType: this.queryParams.timeType
        })
        this.zjList = res.data
      } finally {
        this.zjLoading = false
      }
    },
    // 更多热榜数据
    handleMore(item) {
      const fullPath = this.$router.resolve({
        path: '/fullSearch/rankDetail',
        query: {type: item, timeType: this.queryParams.timeType}
      })
      window.open(fullPath.href, '_blank')
    },
    // 平台- 微博热榜数据
    async platWeiboList() {
      try {
        this.weiboLoading = true
        let res = await hotRankList({type: 1, count: 10})
        this.newsData = res.data.realTime
      } finally {
        this.weiboLoading = false
      }
    },
    // 平台- 抖音热榜
    async platDouyinList() {
      try {
        this.douyinLoading = true
        let res = await hotRankList({type: 2, count: 10})
        this.hotData = res.data.hot
      } finally {
        this.douyinLoading = false
      }
    },
    // 平台- 百度热榜
    async platBaiduList() {
      try {
        this.baiduLoading = true
        let res = await hotRankList({type: 3, count: 10})
        this.opinionRealTimeData = res.data.realTime
      } finally {
        this.baiduLoading = false
      }
    },
    // 标题跳转到详情
    handleDetail(val) {
      if (val == 'law') {
        const planRoute = this.$router.resolve({
          path: '/fullSearch/searchRank',
        })
        window.open(planRoute.href, '_blank')
      }
      if (val == 'alert') {
        const planRoute = this.$router.resolve({
          path: '/publicOpinionWarning',
          query: {
            startTime: this.queryParams.startTime,
            endTime: this.queryParams.endTime,
          }
        })
        window.open(planRoute.href, '_blank')
      }
      if (val == 'account') {
        this.goToExpendDetail('活跃账号', '全部', undefined)
        window.localStorage.setItem('accountData', JSON.stringify(this.accountData.yyData))
      }
      if (val == 'words') {
        this.goToExpendDetail('热词云', '全部', undefined)
        window.localStorage.setItem('wordsData', JSON.stringify(this.cloudData.map((item) => item.name)))
      }
      if (val == 'area') {
        this.goToExpendDetail('辖区总览', '全部', undefined)
        window.localStorage.setItem('areaData', JSON.stringify(this.areaList.map((item) => item.areaName)))
      }
      if (val == 'point') {
        window.localStorage.setItem('pointData', JSON.stringify(this.keyList))
        this.goToExpendDetail('重点账号动态', '全部', this.allcountData, undefined, undefined, 0)
      }
      if (val == 'pointAccount') {
        window.localStorage.setItem('pointData', JSON.stringify(this.keyList))
        this.goToExpendDetail('重点账号', '全部', this.allcountData, this.keyTypes, this.videos, 0)
      }
    },
    // 过滤信息
    filterLists(item) {
      let filterParams = {word: item.name}
      // this.$confirm('此操作将过滤热词, 是否继续?', '提示', {
      //           confirmButtonText: '确定',
      //           cancelButtonText: '取消',
      //           type: 'warning'
      //           }).then(() => {
      DeleteHotWord(filterParams).then(res => {
        if (res.code == 200) {
          this.$message.success('操作成功')
          this.getCloudData()
        }
      })
      // })
    },
    startDisable(time) {
      return time.getTime() > new Date(this.endTime).getTime()
    },
    // 结束日期规则
    endDisable(time) {
      return time.getTime() < new Date(this.startTime).getTime()
    },
    // 获取主题
    async getHabitTheme() {
      const res = await getHabit()
      this.currentTheme = res.data || 'dark'
      this.switchTheme = res.data !== 'light';
      this.$nextTick(() => {
        window.document.documentElement.setAttribute('data-theme', this.currentTheme)
      })
    },
    // 切换主题
    async changeTheme(value) {
      this.currentTheme = value ? 'dark' : 'light'
      window.document.documentElement.setAttribute('data-theme', this.currentTheme)
      const res = await setHabit({themeColor: this.currentTheme})
      // this.EchartsList()
      // this.getPieData()
      this.getLineData()
      // this.getAccountData()
      this.getCountData()
    },
    // 退出全屏
    outFullScreen() {
      screenfull.exit();
      this.isFull = true
    },
    // 全屏
    toggleFullScreen() {
      const elem = document.getElementById('fullscreenContent')
      if (!screenfull.isEnabled) {
        return false;
      }
      if (elem) {
        screenfull.request(elem);
      }
      screenfull.on('change', () => {
        if (screenfull.isFullscreen) {
          // 进入全屏模式，隐藏不必要的元素
          document.body.classList.add('fullscreen-mode');
          this.isFull = false
        } else {
          // 退出全屏模式，显示不必要的元素
          document.body.classList.remove('fullscreen-mode');
          this.isFull = true
        }
      });
    },
    // 获取数据总量
    async getCountData() {
      const res = await getTotal(this.queryParams)
      this.allcountData = res.data
      this.$refs.flipperRef?.toOrderNum(parseInt(this.allcountData))
    },
    // 查看原文
    goOrigin(item) {
      window.open(item.url, '_blank')
    },
    // 详情
    goToExpendDetail(title, name, value, type, videoHost, useArea, historyFlag = 0) {
      console.log('title, :>> ', title, name);
      if (title == '关注' && value == 0) {
        return false
      }
      if (title == '关注' && name == '重点关注事件') {
        return this.goKeyFocusMonitor(historyFlag)
      }
      let useCommon = title == '关注' || title == '重点账号动态' || title == '重点账号' ? 0 : 1
      const planRoute = this.$router.resolve({
        path: '/home/<USER>',
        query: {
          startTime: this.queryParams.startTime,
          endTime: this.queryParams.endTime,
          title: title,
          name: name,
          value: value,
          useCommon: useCommon,
          type: type,
          videoHost: videoHost,
          useArea,
        }
      })
      window.open(planRoute.href, '_blank')
    },
    // 跳转到重点事件
    goKeyFocusMonitor(historyFlag) {
      const planRoute = this.$router.resolve({
        path: '/keyFocusMonitor',
        query: {
          toHistoryFlag: historyFlag
        }
      })
      window.open(planRoute.href, '_blank')
    },
    // 全国政法热点
    async getHot() {
      try {
        this.hotLoading = true
        const res = await hotList({type: 4, count: 10})
        this.hotLoading = false
        this.countryList = res.data
      } catch (error) {
        this.hotLoading = false
      }
    },
    // 热点事件
    async getProvince() {
      try {
        this.provinceLoading = true
        const res = await getHotPlan(this.queryParams)
        this.provinceLoading = false
        this.provinceList = res.data
      } catch (error) {
        this.provinceLoading = false
      }
    },
    // 辖区总览
    async getAreaData() {
      this.areaLoading = true
      const res = await getAreaOverview(this.queryParams)
      this.areaList = res.data
      let half = Math.floor(this.areaList.length / 2)
      if (this.areaList.length % 2 != 0) {
        half = half + 1
      }
      this.areaOneList = this.areaList.slice(0, half);
      this.areaTwoList = this.areaList.slice(half)
      this.areaLoading = false
    },
    // 情感分布
    async getPieData() {
      this.emtionLoading = true
      this.chartData = []
      const res = await getEmontionStatis(this.queryParams)
      this.emtionLoading = false
      this.pieData = res.data || []
    },
    // 折线图
    async getLineData() {
      this.lineLoading = true
      this.lineData = {}
      const res = await getCurve(this.queryParams)
      this.lineLoading = false
      this.lineData = res.data
    },
    // 热词云
    async getCloudData() {
      this.cloudData = []
      this.cloudLoading = true
      const res = await getWordsAnalyse(this.queryParams)
      this.cloudLoading = false
      this.cloudData = res.data || []
      console.log(this.cloudData, 'this.cloudData');


    },
    // all
    EchartsList() {
      this.queryWarn() //预警信息
      this.queryKeyAccount() //重点账号、动态
      this.getCountData() //数据总量
      this.queryZjList()
      // this.getPieData()
      this.getCloudData()
      this.getLineData()
      this.getAreaData() //辖区总览
      this.getProvince()
    },
    // 点击日期
    changeDate(val) {
      if (val !== '4') {
        this.startTime = ''
        this.endTime = ''
      }
    },
    // 查询操作
    dataSearch() {
      if (this.queryParams.timeType == '1') {
        this.queryParams.startTime = beforeTime.momentDay.split(',')[0]
        this.queryParams.endTime = beforeTime.momentDay.split(',')[1]
        this.EchartsList()
      } else if (this.queryParams.timeType == '2') {
        this.queryParams.startTime = beforeTime.oneDay.split(',')[0]
        this.queryParams.endTime = beforeTime.oneDay.split(',')[1]
        this.EchartsList()
      } else if (this.queryParams.timeType == '3') {
        this.queryParams.startTime = beforeTime.sevenDay.split(',')[0]
        this.queryParams.endTime = beforeTime.sevenDay.split(',')[1]
        this.EchartsList()
      } else {
        this.queryParams.startTime = beforeTime.oneMonth.split(',')[0]
        this.queryParams.endTime = beforeTime.oneMonth.split(',')[1]
        this.EchartsList()
        // if (this.endTime && this.startTime) {
        //   this.queryParams.startTime = this.startTime
        //   this.queryParams.endTime = this.endTime
        //   this.EchartsList()
        // } else {
        //   this.$message.error('请选择正确的时间范围')
        // }
      }
    },
  }
};
</script>
<style>
.home-content em {
  color: red;
}
</style>
<style scoped lang="scss">
@import './index.scss';
</style>
