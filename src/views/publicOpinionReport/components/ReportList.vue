<template>
  <div class="list-wrap">
    <el-form :inline="true" :model="queryForm" class="demo-form-inline" size="mini">
      <el-form-item label="">
        <el-select v-model="queryForm.isFile">
          <el-option label="全部报告" value="0"></el-option>
          <el-option label="上传的报告" value="2"></el-option>
          <el-option label="生成的报告" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-select v-model="queryForm.reportType" placeholder="请选择报告分类" clearable>
          <el-option v-for="item in typeLists" :key="item.value" :label="item.name" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-date-picker v-model="queryForm.startTime" :picker-options="startOption" type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss" placeholder="生成时间" class="timeRange"/>
        -
        <el-date-picker v-model="queryForm.endTime" :picker-options="endOption" type="datetime"
                        value-format="yyyy-MM-dd HH:mm:ss" placeholder="生成时间" class="timeRange"/>
      </el-form-item>
      <el-form-item>
        <el-input v-model.trim="queryForm.title" clearable placeholder="请输入报告标题"
                  class="input-with-select"></el-input>
      </el-form-item>
      <el-button size="mini" type="primary" @click="submitSearch">检索</el-button>
      <el-button size="mini" @click="upReport">上传</el-button>
      <el-dropdown>
        <el-button style="margin:0px 10px" size="mini" type="primary">一键下载</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item @click.native="mulDownLoad('pdf')">PDF</el-dropdown-item>
          <el-dropdown-item @click.native="mulDownLoad('docx')">Word</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button size="mini" :disabled="multiple" @click="deleteReport">一键删除</el-button>
      </el-form-item>
    </el-form>
    <div class="list-table">
      <el-table border v-loading="loading" :data="reportList" :header-cell-style="{background:'#F8FAFF'}"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center"/>
        <el-table-column label="序号" type="index" align="center" :index="indexMethod"></el-table-column>
        <el-table-column label="报告标题" prop="title" align="center"></el-table-column>
        <el-table-column label="报告模板" prop="tempName" align="center">
          <template slot-scope="scope">
            <div>{{ scope.row.tempName || '无'}}</div>
          </template>
        </el-table-column>
        <el-table-column label="报告分类" prop="tempName" align="center">
          <template slot-scope="scope">
            <div> {{ reportTypeName(scope.row) }}</div>
          </template>
        </el-table-column>
        <el-table-column label="生成时间" prop="createTime" align="center"></el-table-column>
        <!-- <el-table-column label="状态" prop="createTime" align="center">
          <template-slot="scope">
          </template>
        </el-table-column> -->
        <el-table-column label="生成人" prop="createBy" align="center"></el-table-column>
        <el-table-column label="操作" class-name="small-padding fixed-width" align="center">
          <template slot-scope="scope">
            <el-button type="text" v-if="scope.row.isFile == 1">
              <el-tooltip content="查看" placement="top" effect="light">
                <img class="img-eye" src="@/assets/report/report-eye.png" alt="" @click="viewReport(scope.row)">
              </el-tooltip>
            </el-button>
            <el-button type="text">
              <el-dropdown>
                <el-tooltip content="下载" placement="top" effect="light">
                  <img class="img-down" src="@/assets/report/report-down.png" alt="">
                </el-tooltip>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="downLoad('pdf',scope.row)">PDF</el-dropdown-item>
                  <el-dropdown-item @click.native="downLoad('docx',scope.row)">Word</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </el-button>
            <!-- <el-button type="text">
                 <el-dropdown>
                     <el-tooltip content="报告下发方式" placement="top" effect="light">
                         <img class="img-send" src="@/assets/report/report-send.png" alt="">
                     </el-tooltip>
                     <el-dropdown-menu  slot="dropdown">
                         <el-dropdown-item @click.native="sendReport('email',scope.row)">
                           <i class="el-icon-message"></i>邮件</el-dropdown-item>
                         <el-dropdown-item @click.native="sendReport('message',scope.row)">
                           <i class="el-icon-mobile-phone"></i>短信</el-dropdown-item>
                     </el-dropdown-menu>
                 </el-dropdown>
             </el-button> -->
            <el-button type="text" v-if="scope.row.isFile == 1">
              <el-tooltip content="修改" placement="top" effect="light">
                <img class="img-get" src="@/assets/report/report-edit.png" alt="" @click="editReport(scope.row)">
              </el-tooltip>
            </el-button>
            <el-button type="text">
              <el-tooltip content="删除" placement="top" effect="light">
                <img class="img-get" src="@/assets/report/report-delete.png" alt="" @click="deleteReport(scope.row)">
              </el-tooltip>
            </el-button>
            <!-- <el-button type="text">
              <el-tooltip content="提取素材" placement="top" effect="light">
                   <img class="img-get" src="@/assets/report/report-get.png" alt="">
               </el-tooltip>
             </el-button> -->
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total>0" :total="total" :page.sync="queryForm.pageNum" :limit.sync="queryForm.pageSize"
                  @pagination="pagination"/>
    </div>
    <!-- 上传报告 -->
    <el-dialog :visible.sync="dialogReport" title="上传报告" width="50%" append-to-body>
      <el-form ref="reportForm" :model="reportForm" :rules="rules" label-width="100px">
        <el-form-item label="报告标题：" prop="title">
          <el-input v-model.trim="reportForm.title" placeholder="请输入报告标题"/>
        </el-form-item>
        <el-form-item label="报告分类：">
          <el-select v-model="reportForm.reportType" placeholder="请选择报告分类" clearable>
            <el-option v-for="item in typeLists" :key="item.value" :label="item.name" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="上传附件：" prop="fileId">
          <el-upload
            :action="uploadFileUrl"
            :before-upload="handleBeforeUpload"
            :file-list="fileList"
            :limit="1"
            :on-success="handleFileSuccess"
            :on-exceed="handleExceed"
            :show-file-list="true"
            :headers="headers"
            class="upload-file-uploader"
            ref="upload"
          >
            <!-- 上传按钮 -->
            <el-button size="mini" type="primary">选取文件</el-button>
            <!-- 上传提示 -->
            <div class="el-upload__tip" slot="tip">
              请上传
              <template> 大小不超过 <b style="color: #f56c6c">2MB</b></template>
              <template> 格式为 <b style="color: #f56c6c">pdf、docx</b></template>
              的文件
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="submitReport">确定</el-button>
                <el-button @click="dialogReport = false">取消</el-button>
            </span>
      </template>
    </el-dialog>
    <!-- 查看报告 -->
    <el-dialog class="viewClass" :visible.sync="dialogVisible" :show-close="true" center top="15vh">
      <div class="contain" v-loading="reportLoading">
        <el-button type="text" @click="goReport">链接详情</el-button>
        <div class="head">
          <div class="head-title">{{ detailList.title }}</div>
          <div class="star-insert">第（ {{detailList.issue}} ）期</div>
          <div>{{detailList.head}} <span style="display:inline-block;width:10px;"></span> {{ detailList.createTime }}
          </div>
          <div class="line"></div>
        </div>
        <div class="content">
          <div class="div-box" v-for="item in newList">
            <h1 v-if="chartData[item.params]">{{ item.name }}</h1>
            <div class="text" v-if="item.id === 1 || item.id === 2 || item.id === 3">{{ chartData[item.params]}}</div>
            <div class="image">
              <pieChart
                ref="pieChartOneRef"
                v-if="item.params == 'mediaStatistics' && chartData[item.params] "
                style="width:100%;height:300px;"
                :show-loading="sourceLoading"
                :data="chartData[item.params] && chartData[item.params]"
                :radius="['30%', '50%']"/>
              <pieChart
                ref="pieChartTwoRef"
                v-if="item.params == 'emotionAnalysis' && chartData[item.params] "
                style="width:100%;height:300px;"
                :show-loading="sourceLoading"
                :data="chartData[item.params]"/>
              <barChart
                ref="barChartRef"
                v-if="item.params == 'mediaDetails' && chartData[item.params] "
                style="width:100%;height:300px;"
                :data="chartData[item.params]"
                :showLoading="sourceLoading"/>
              <cloudChart
                ref="cloudRef"
                v-if="item.params == 'charCloud' && chartData[item.params]"
                style="width:100%;height:300px;"
                :showLoading="sourceLoading"
                :data="chartData[item.params]"
              />
              <div v-if="item.params == 'mainInfo' && chartData[item.params]">
                <el-table
                  ref="tableRef"
                  border
                  v-loading="sourceLoading"
                  :data="chartData[item.params]"
                  :header-cell-style="{background:'#F8FAFF'}">
                  <el-table-column label="序号" type="index" align="center"></el-table-column>
                  <el-table-column label="标题" prop="title" align="center">
                    <template slot-scope="scope">
                      <div v-html="scope.row.title"></div>
                    </template>
                  </el-table-column>
                  <el-table-column label="日期与来源" prop="sourceAndTime" align="center"></el-table-column>
                  <el-table-column label="属性" prop="emotion" align="center"/>
                </el-table>
              </div>
              <div v-if="item.params == 'infoIntro' && infoIntro ">
                <el-descriptions class="margin-top" :column="2" border ref="descriptionsRef">
                  <el-descriptions-item label="性质" label-class-name="my-label">{{ infoIntro.emotion}}
                  </el-descriptions-item>
                  <el-descriptions-item label="文章来源" label-class-name="my-label">{{ infoIntro.source}}
                  </el-descriptions-item>
                  <el-descriptions-item label="时间" label-class-name="my-label">{{ infoIntro.time}}
                  </el-descriptions-item>
                  <el-descriptions-item label="作者" label-class-name="my-label">{{ infoIntro.author}}
                  </el-descriptions-item>
                  <el-descriptions-item label="内容" label-class-name="my-label" :span="2">{{ infoIntro.text}}
                  </el-descriptions-item>
                  <el-descriptions-item label="原文" label-class-name="my-label" :span="2">
                    <a :href="infoIntro.url" target="_blank" style="color:#247CFF;">{{ infoIntro.url}}</a>
                  </el-descriptions-item>
                </el-descriptions>

              </div>
              <lineChart
                v-if="item.params == 'mediaTrendChart' && chartData[item.params]"
                style="width:100%;height:300px;"
                :data="chartData[item.params]"
                :showLoading="sourceLoading"/>
            </div>
          </div>

        </div>
      </div>
    </el-dialog>
    <!-- 隐藏 -->
    <div class="content" style="display:none;" v-for="items in allImgLists">
      <div class="div-box" v-for="item in items">
        <h1 v-if="chartData[item.params]">{{ item.name }}</h1>
        <div class="text" v-if="item.id === 1 || item.id === 2 || item.id === 3">{{ chartData[item.params]}}</div>
        <div class="image">
          <pieChart
            ref="pieChartOneRef"
            @chartRef="chartToImg"
            v-if="item.params == 'mediaStatistics' && chartData[item.params] "
            style="width:600px;height:300px;"
            :show-loading="sourceLoading"
            :data="chartData[item.params]"
            :isToImg="'pie1'"
            :radius="['30%', '50%']"/>
          <pieChart
            ref="pieChartTwoRef"
            @chartRef="chartToImg"
            v-if="item.params == 'emotionAnalysis' && chartData[item.params] "
            style="width:600px;height:300px;"
            :show-loading="sourceLoading"
            :isToImg="'pie2'"
            :data="chartData[item.params]"/>
          <barChart
            @chartRef="chartToImg"
            ref="barChartRef"
            v-if="item.params == 'mediaDetails' && chartData[item.params] "
            style="width:600px;height:300px;"
            :isToImg="'bar'"
            :data="chartData[item.params]"
            :showLoading="sourceLoading"/>
          <cloudChart
            @chartRef="chartToImg"
            ref="cloudChartRef"
            v-if="item.params == 'charCloud' && chartData[item.params]"
            style="width:600px;height:300px;"
            :showLoading="sourceLoading"
            :isToImg="'cloud'"
            :data="chartData[item.params]"
          />
          <div v-if="item.params == 'mainInfo' && chartData[item.params]">
            <el-table
              ref="tableRef"
              border
              v-loading="sourceLoading"
              :data="chartData[item.params]"
              :header-cell-style="{background:'#F8FAFF'}">
              <el-table-column label="序号" type="index" align="center"></el-table-column>
              <el-table-column label="标题" prop="title" align="center">
                <template slot-scope="scope">
                  <div v-html="scope.row.title"></div>
                </template>
              </el-table-column>
              <el-table-column label="日期与来源" prop="sourceAndTime" align="center"></el-table-column>
              <el-table-column label="属性" prop="emotion" align="center"/>
            </el-table>
          </div>
          <div v-if="item.params == 'infoIntro' && infoIntro ">
            <el-descriptions class="margin-top" :column="2" border ref="descriptionsRef">
              <el-descriptions-item label="性质" label-class-name="my-label">{{ infoIntro.emotion}}
              </el-descriptions-item>
              <el-descriptions-item label="文章来源" label-class-name="my-label">{{ infoIntro.source}}
              </el-descriptions-item>
              <el-descriptions-item label="时间" label-class-name="my-label">{{ infoIntro.time}}</el-descriptions-item>
              <el-descriptions-item label="作者" label-class-name="my-label">{{ infoIntro.author}}
              </el-descriptions-item>
              <el-descriptions-item label="内容" label-class-name="my-label" :span="2">{{ infoIntro.text}}
              </el-descriptions-item>
              <el-descriptions-item label="原文" label-class-name="my-label" :span="2">
                <a :href="infoIntro.url" target="_blank" style="color:#247CFF;">{{ infoIntro.url}}</a>
              </el-descriptions-item>
            </el-descriptions>

          </div>
          <lineChart
            @chartRef="chartToImg"
            v-if="item.params == 'mediaTrendChart' && chartData[item.params]"
            style="width:600px;height:300px;"
            :data="chartData[item.params]"
            :isToImg="'line'"
            :showLoading="sourceLoading"/>
        </div>
      </div>

    </div>
    <el-card v-show="progressFlag" class="processbox">
      <span class="progressSpan">导出进度：</span>
      <el-progress :text-inside="true" :stroke-width="17" status="warning" :percentage="progressPercent">
      </el-progress>
    </el-card>
  </div>
</template>

<script>
import domtoimage from "dom-to-image";
import {getReportPageList, deleteReport, uploadReport, downReport, detailReport} from '@/api/report/list'
import {getToken} from "@/utils/auth";
import pieChart from '@/views/fullSearch/components/pieChart.vue'
import barChart from '@/views/fullSearch/components/barChart.vue'
import cloudChart from '@/views/fullSearch/components/cloudChart.vue'
import lineChart from '@/views/fullSearch/components/lineChart.vue'
import {downPostBlobFile} from '@/utils/index'

export default {
  components: {pieChart, barChart, cloudChart, lineChart},
  data() {
    return {
      headers: {Authorization: "Bearer " + getToken()},
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/report/uploadReport", // 上传的图片服务器地址
      reportForm: {
        title: '',
        reportType: '',
        fileId: ''
      },
      typeLists: [{name: '日报', value: 1}, {name: '周报', value: 2}, {name: '月报', value: 3},
        {name: '季度报', value: 4}, {name: '年报', value: 5}, {name: '专报', value: 6}],
      fileList: [],
      dialogReport: false,
      loading: false,
      reportList: [],
      total: 0,
      ids: [],
      items: [],
      multiple: true,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        reportType: '',
        isFile: '0',
        title: '',
        startTime: '',
        endTime: '',
      },
      // 表单校验
      rules: {
        title: [
          {required: true, message: "请输入报告标题", trigger: "blur"},
          {min: 1, max: 30, message: '长度不能超过30个字符', trigger: 'change'}
        ],
        // fileId: [
        //   { required: true, message: "请上传报告", trigger: "change" }
        // ]
      },
      startOption: {
        disabledDate: this.startDisable
      },
      endOption: {
        disabledDate: this.endDisable
      },
      dialogVisible: false,
      detailList: {},
      chartData: {},
      dimension: [
        {id: 1, name: '报告导读', params: 'reportIntro'},
        {id: 2, name: '处置建议', params: 'suggest'},
        {id: 3, name: '监测概述', params: 'overview'},
        {id: 4, name: '媒体来源统计', params: 'mediaStatistics'},
        {id: 5, name: '信息情感分析', params: 'emotionAnalysis'},
        {id: 6, name: '来源明细', params: 'mediaDetails'},
        {id: 7, name: '信息字符云', params: 'charCloud'},
        {id: 8, name: '主要舆情', params: 'mainInfo'},
        {id: 9, name: '舆情导读', params: 'infoIntro'},
        {id: 10, name: '媒体信息走势图', params: 'mediaTrendChart'},
      ],
      newList: [],
      sourceLoading: false,
      legendData: [],
      infoIntro: {},
      imgEchart: {
        pieImg: '',
        pieImg2: '',
        lineImg: '',
        barImg: '',
        cloudImg: ''
      },
      allImgLists: [],
      progressFlag: false,
      progressPercent: 0,
      resParams: {},
      reportLoading: false,
      detailId: ''

    }
  },
  created() {
    this.getList()
  },
  methods: {
    reportTypeName(row) {
      const type = this.typeLists.find(type => type.value == row.reportType);
      return type ? type.name : '无';
    },
    goReport() {
      const fullPath = this.$router.resolve({
        path: '/outReport',
        query: {reportId: this.detailId}
      })
      window.open(fullPath.href, '_blank')
    },
    indexMethod(index) {
      return this.queryForm.pageNum * this.queryForm.pageSize - this.queryForm.pageSize + (index + 1)
    },
    startDisable(time) {
      return time.getTime() > new Date(this.queryForm.endTime).getTime()
    },
    // 结束日期规则
    endDisable(time) {
      return time.getTime() < new Date(this.queryForm.startTime).getTime()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.reportId)
      this.items = selection
      this.multiple = !selection.length
    },
    getList() {
      this.loading = true
      getReportPageList(this.queryForm).then(res => {
        this.reportList = res.rows
        this.total = res.total
        this.loading = false
      })
    },
    submitSearch() {
      this.getList()
    },
    // 分页查询
    pagination(page) {
      this.queryForm.pageNum = page.page
      this.queryForm.pageSize = page.limit
      this.getList()
    },
    // 获取详情
    async getDetails(item, value) {
      this.sourceLoading = true
      try {
        const res = await detailReport(item.reportId)
        this.sourceLoading = false

        this.detailList = res.data
        this.detailList.createTime = res.data.createTime.slice(0, 10);

        this.chartData = res.data.data
        this.chartData.mediaStatistics = res.data.data?.mediaStatistics ? {data: res.data.data.mediaStatistics} : ''
        this.chartData.emotionAnalysis = res.data.data?.emotionAnalysis ? {data: res.data.data.emotionAnalysis} : ''

        const yyData = res.data.data?.mediaDetails?.xList
        const xxData = res.data.data?.mediaDetails?.yList
        this.chartData.mediaDetails = this.chartData.mediaDetails ? {xxData, yyData} : ''
        this.chartData.charCloud = res.data.data?.charCloud ? res.data.data?.charCloud : ''

        this.infoIntro = res.data.data?.infoIntro ? res.data.data?.infoIntro[0] : ''
        const inputComponents = res.data.data.inputComponents ? res.data.data.inputComponents : []
        const dimensionMap = this.dimension.reduce((acc, item) => {
          acc[item.params] = item;
          return acc;
        }, {});

        this.newList = inputComponents.map(param => dimensionMap[param]);
        this.allImgLists = [...this.allImgLists, inputComponents.map(param => dimensionMap[param])]
        if (value) {
          setTimeout(() => {
            const resParams = {
              reportId: item.reportId,
              fileType: value,
              data: {
                inputComponents: this.detailList.data.inputComponents,
                mediaStatistics: this.imgEchart.pieImg,
                emotionAnalysis: this.imgEchart.pieImg2,
                mediaDetails: this.imgEchart.barImg,
                charCloud: this.imgEchart.cloudImg,
                mediaTrendChart: this.imgEchart.lineImg,
              }
            }
            downPostBlobFile('/report/downloadReport', resParams, `${item.title}.${value}`, () => {
              this.progressFlag = false
              this.$message.success('下载成功')
              if (this.chartData.charCloud.length > 0) {
                this.$refs?.cloudRef?.[0]?.chart.dispose();
              }
            })
          }, 2000)
        }

      } catch (error) {
        this.sourceLoading = false
        console.error('Failed to fetch report details:', error);
        // 可以在这里添加错误处理逻辑，如显示错误消息
      }
    },
    // 查看
    async viewReport(row) {
      this.dialogVisible = true
      this.detailId = row.reportId
      await this.getDetails(row, '')
      this.$nextTick(() => {
        if (this.chartData.charCloud.length > 0) {
          this.$refs?.cloudRef?.[0]?.initChart();
        }
      })
    },
    // 批量下载
    mulDownLoad(value) {
      this.allImgLists = []
      if (!this.items.length) {
        this.$message.error('请先选择要下载的报告')
      }
      this.items.forEach(async (item, index) => {
        if (item.isFile == 2) {
          const req = {reportId: item.reportId, fileType: value};
          downPostBlobFile('/report/downloadReport', req, `${item.title}.${value}`, () => {
            this.$message.success('下载成功')
          })
        } else {
          await this.getDetails(item, value)
        }
      })
    },
    async downLoad(value, row) {
      this.allImgLists = []
      // 生成报告
      if (row.isFile == 2) {
        const req = {reportId: row.reportId, fileType: value}
        downPostBlobFile('/report/downloadReport', req, `${row.title}.${value}`, () => {
          this.$message.success('下载成功')
        })
      } else {
        await this.getDetails(row, value)
      }
    },
    chartToImg(val, base) {
      const imgMappings = {
        'pie1': 'pieImg',
        'pie2': 'pieImg2',
        'bar': 'barImg',
        'line': 'lineImg',
        'cloud': 'cloudImg'
      };
      const propName = imgMappings[val]
      this.imgEchart[propName] = base;
    },
    // 发送报告
    sendReport(value, row) {
      console.log('value :>> ', value);
    },
    // 上传报告
    upReport() {
      this.dialogReport = true
      this.reportForm.title = ''
      this.reportForm.fileId = ''
      this.reportForm.reportType = ''
      this.$nextTick(() => {
        this.$refs.upload.clearFiles();
      })
    },
    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      const isLt2M = file.size / 1024 / 1024
      if (isLt2M > 2) {
        this.$message.error('上传图片大小不能超过2MB')
        return false; // 阻止文件上传
      }

      const imgType = file.name.endsWith('.pdf') || file.name.endsWith('.docx')
      if (!imgType) {
        this.$message.error(`文件格式不正确, 请上传pdf、docx格式文件!`);
        return false; // 阻止文件上传
      }
      return isLt2M && imgType
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`只允许上传单个文件`);
    },
    // 文件上传成功处理
    handleFileSuccess(response) {
      this.reportForm.fileId = response.data
    },
    // 提交上传文件
    submitReport() {
      this.$refs["reportForm"].validate(valid => {
        if (valid) {
          if (this.reportForm.fileId) {
            uploadReport(this.reportForm).then(res => {
              if (res.code == 200) {
                this.$message.success('上传成功')
                this.$refs.upload.clearFiles();
                this.dialogReport = false
                this.getList()
              }
            })
          } else {
            this.$message.error('请先上传附件')
          }
        }
      });
    },
    // 修改
    editReport(row) {
      this.$router.push({path: '/updateReport', query: {reportId: row.reportId}})
    },
    /** 删除 */
    deleteReport(row) {
      let wordIds = row.reportId || this.ids;
      this.$confirm('是否确认删除所选中的数据?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return deleteReport(wordIds);
      }).then(() => {
        this.getList();
        this.msgSuccess("操作成功");
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.list-wrap {
  background: #fff;
  min-height: calc(100vh - 120px);
  padding: 30px 40px 0px 40px;
  // overflow-y: auto;
  .list-table {
    margin-top: 20px;

    .img-eye {
      width: 20px;
      height: 10px;
    }

    .img-down {
      width: 16px;
      height: 14px;
    }

    .img-send {
      width: 14px;
      height: 14px;
    }

    .img-get {
      width: 13px;
      height: 13px;
    }
  }

  .contain {
    overflow-y: auto;

    .head {
      text-align: center;

      .head-title {
        font-weight: bold;
        font-size: 24px;
        color: #FF0D0D;
      }

      .star-insert {
        margin: 10px;
        font-size: 14px;
        line-height: 20px;
      }

      .line {
        height: 1px;
        border: 2px solid #FF0D0D;
        margin-top: 26px;
        margin-bottom: 35px;
      }
    }

    .content {
      .div-box {
        .text {
          font-size: 16px;
          line-height: 24px;
        }

        .image {
          // width: 100%;
          // // height: 100%;
          // height: 200px;
        }
      }
    }
  }
}

::v-deep .el-dialog__footer {
  text-align: center;
}

::v-deep .el-dialog__header {
  background: #247CFF;
  padding-bottom: 20px;

  .el-dialog__title {
    color: #fff;
  }

  .el-dialog__close {
    color: #fff;
  }
}

.viewClass {
  ::v-deep .el-dialog__header {
    background: #fff;
    padding-bottom: 20px;
    // .el-dialog__title {
    //     color: #fff;
    //   }
    .el-dialog__close {
      color: #333;
    }
  }

  ::v-deep .el-dialog {
    width: 60%;
    height: 90%;
    overflow: hidden;

    .el-dialog__body {
      height: 90%;
      overflow-y: auto;
    }

    .el-dialog__footer {
      padding: 10px 0px;
      position: absolute;
      bottom: 0px;
      display: flex;
      justify-content: center;
      width: 100%;
      background: rgba(74, 77, 81, .8);
    }
  }

}

.uploadFile {
  width: 100%;
  text-align: center;
  background-color: #fff;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 380px;
  height: 160px;
  text-align: center;
  cursor: pointer;
  position: relative;
  // overflow: hidden;
  .el-icon-upload {
    font-size: 67px;
    color: #C0C4CC;
    margin: 20px 0 16px;
    line-height: 50px;
  }
}

::v-deep .my-label {
  width: 80px;
}

.processbox {
  width: 50%;
  height: 140px;
  background: #fff;
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 20px;
  box-sizing: border-box;
  border-radius: 5px;

  .progressSpan {
    width: 100%;
    overflow: hidden;
    margin-bottom: 10px;
    display: block;
  }
}
</style>
