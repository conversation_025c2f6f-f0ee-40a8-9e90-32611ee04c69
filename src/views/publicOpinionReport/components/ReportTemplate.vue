<template>
  <div class="temp-wrap">
    <div class="top-temp">
      <div class="label">
        <img src="@/assets/images/temp-title.png" alt="">
        <span class="text">系统模板</span>
        <span>系统提供的报告模板</span>
      </div>
      <div class="template-wrappers">
        <div class="template-wrapper" v-for="(item,index) in systemTemplate" :key="index">
          <div class="template-bg" @click="getOview(item.tempId)">
            <div class="content">
              <div class="title">{{item.name}}</div>
              <div class="publish-num">({{currentYear}}) 第一期</div>
              <div class="date"> 报告 {{item.createTime}}</div>
            </div>
            <img v-if="item.isDefault" class="default-img" src="@/assets/images/default-template.png" alt="">
          </div>
          <div class="template-bottom">
            <el-checkbox v-model="item.isDefault == 1 ? true : false " :disabled="item.isDefault==1"
                         @change="defaultTemp(item.tempId)">
              {{item.isDefault == 1 ? '默认模板' : '设为默认'}}
            </el-checkbox>
          </div>
        </div>
      </div>
      <!--  -->

      <div class="label head">
        <img src="@/assets/images/temp-title.png" alt="">
        <span class="text">自定义模板</span>
        <span>自定义的报告模板</span>
      </div>
      <div class="template-wrappers">
        <div class="template-wrapper" v-for="(item,index) in userTemplate" :key="item.tempId">
          <div class="template-bg" @click="getOview(item.tempId)">
            <div class="content">
              <div class="title">{{item.name}}</div>
              <div class="publish-num">({{currentYear}}) 第一期</div>
              <div class="date">报告 {{item.createTime}}</div>
            </div>
            <img v-if="item.isDefault" class="default-img" src="@/assets/images/default-template.png" alt="">
          </div>
          <div class="template-bottom">
            <el-checkbox v-model="item.isDefault == 1 ? true : false " :disabled="item.isDefault==1"
                         @change="defaultTemp(item.tempId)">
              {{item.isDefault == 1 ? '默认模板' : '设为默认'}}
            </el-checkbox>
            <el-button type="text" style="margin-left:4px;color:#666;" @click="updateTemp(item.tempId)">修改</el-button>
            <el-button type="text" style="margin-left:4px;color:#666;" @click="deleteTemp(item.tempId)">删除</el-button>
          </div>
        </div>
        <div class="template-wrapper new">
          <img class="add-img" src="@/assets/images/big-add.png" alt="" @click="addTemplate">
          <div>新建自定义模板</div>
        </div>

      </div>

    </div>
    <!-- 详情 -->
    <el-dialog :visible.sync="dialogVisible" :show-close="false" center top="15vh">
      <div class="contain">
        <div class="head">
          <div class="head-title">{{ detailList.name }}</div>
          <div class="star-insert">第（ 1 ）期</div>
          <div>网络舆情中心 {{ detailList.createTime }}</div>
          <div class="line"></div>
        </div>
        <div class="content">
          <div class="div-box" v-for="item in newList">
            <h1>{{ item.name}}</h1>
            <div class="text" v-if="item.id === 1 || item.id === 2 || item.id === 3">{{item.content}}</div>
            <img v-else :src="item.img" alt="" class="image">
          </div>

        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="createReport">创建报告</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import dayjs from "dayjs";
import {listTemplate, deleteTemplate, defaultTemplate, detailTemplate} from '@/api/report/template'

export default {
  data() {
    return {
      systemTemplate: [],
      userTemplate: [],
      currentYear: `${dayjs().format("YYYY")}`,
      dialogVisible: false,
      detailList: {},
      contentList: [
        {
          id: 1,
          name: '报告导读',
          params: 'reportIntro',
          content: '本报告就加入素材的文章进行分析，共有49篇相关内容，1篇相关评论。其中微博19篇，占比38%，客户端9篇，占比18%，微信9篇，占比18%，论坛4篇，占比8%，网站4篇，占比8%，新闻4篇，占比8%，外媒1篇，占比2%，而微博的比重最大，共有19篇，达到信息总量的38%。目前主要的报道集中在微博、客户端、微信、论坛、网站等几大站点。详细报告请继续浏览。'
        },
        {
          id: 2,
          name: '处置建议',
          params: 'suggest',
          content: '对于舆情信息中具有潜在危害的事件及情况应给予关注并积极处理，防止不良影响产生及扩散。此外，密切关注此前敏感预警事件的发展情况，及时制定有效应对措施。鉴于监测结果中负面舆情时有发生， 应吸取相关经验教训，做好预防和处理工作。'
        },
        {
          id: 3,
          name: '监测概述',
          params: 'overview',
          content: '监测主题相关信息内容48条，1篇相关评论。其中敏感5条，敏感占比10.2%，非敏感40条，非敏感占比81.6%，中性4条，中性占比8.1%。'
        },
        {id: 4, name: '媒体来源统计', params: 'mediaStatistics', img: require('@/assets/report/media-chart.png')},
        {id: 5, name: '信息情感分析', params: 'emotionAnalysis', img: require('@/assets/report/emtion-chart.png')},
        {id: 6, name: '媒体来源明细', params: 'mediaDetails', img: require('@/assets/report/active-chart.png')},
        {id: 7, name: '信息字符云', params: 'charCloud', img: require('@/assets/report/cloud-chart.png')},
        {id: 8, name: '主要舆情', params: 'mainInfo', img: require('@/assets/report/primary-table.png')},
        {id: 9, name: '舆情导读', params: 'infoIntro', img: require('@/assets/report/read-table.png')},
        {id: 10, name: '媒体信息走势图', params: 'mediaTrendChart', img: require('@/assets/report/line-chart.png')},
      ],
      newList: [],
      emitTempId: undefined
    }
  },
  created() {
    this.getReportTemplate()
  },
  methods: {
    async getReportTemplate() {
      const res = await listTemplate()
      this.systemTemplate = res.data.sysTemps
      this.systemTemplate = this.systemTemplate.map(item => {
        const createtimeStr = item.createTime;
        const [year, month, day] = createtimeStr.split(' ')[0].split('-');
        const formattedDate = `${('0' + month).slice(-2)}月${('0' + day).slice(-2)}日`;
        return {
          ...item,
          createTime: formattedDate,
        }
      })

      this.userTemplate = res.data.userTemps
      this.userTemplate = this.userTemplate.map(item => {
        const createtimeStr = item.createTime;
        const [year, month, day] = createtimeStr.split(' ')[0].split('-');
        const formattedDate = `${('0' + month).slice(-2)}月${('0' + day).slice(-2)}日`;
        return {
          ...item,
          createTime: formattedDate,
        }
      })

    },
    addTemplate() {
      this.$router.push('/customTemplate')
    },
    async defaultTemp(tempId) {
      const res = await defaultTemplate(tempId)
      this.getReportTemplate()

    },
    deleteTemp(tempId) {
      this.$confirm('确认删除当前模板吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTemplate(tempId).then(res => {
          if (res.code == 200) {
            this.$message.success('操作成功')
            this.getReportTemplate()
          }
        })
      })
    },
    updateTemp(tempId) {
      this.$router.push({
        path: '/customTemplate',
        query: {id: tempId}
      })
    },
    createReport() {
      this.dialogVisible = false
      this.$emit('changeIndex', 0, this.emitTempId)
    },
    getOview(tempId) {
      this.emitTempId = tempId
      this.dialogVisible = true
      detailTemplate(tempId).then(res => {
        this.detailList = res.data
        this.detailList.createTime = res.data.createTime.slice(0, 10);
        const inputComponents = res.data.inputComponents
        const dimensionMap = this.contentList.reduce((acc, item) => {
          acc[item.params] = item;
          return acc;
        }, {});

        this.newList = inputComponents.map(param => dimensionMap[param]);

      })
    }
  }
}
</script>
<style lang="scss" scoped>
.temp-wrap {
  background: #fff;
  min-height: calc(100vh - 120px);

  .top-temp {
    padding-left: 18px;
    padding-top: 40px;

    .head {
      margin-bottom: 30px;
      margin-top: 40px;
    }

    .label {
      position: relative;
      margin-bottom: 28px;

      .text {
        position: absolute;
        left: 80px;
        top: 50%;
        transform: translate(-50%, -50%);
        color: #fff;
        font-size: 18px;
        line-height: 0;
      }

      img {
        width: 160px;
        height: 36px;
        margin-right: 20px;
      }

      span {
        color: #999999;
        font-size: 14px;
      }
    }

    .template-wrappers {
      display: flex;
      flex-wrap: wrap;
      padding: 0 24px;

      .template-wrapper {
        width: 166px;
        margin: 0 11px 15px;
        border: 1px solid #EFEFEF;

        .template-bg {
          position: relative;
          height: 216px;
          font-size: 12px;
          text-align: center;
          background: url('./../../../assets/images/red-template.png') no-repeat;
          background-size: 100% 100%;
          cursor: pointer;

          .default-img {
            width: 54px;
            height: 50px;
            position: absolute;
            right: 18px;
            bottom: 26px;
          }

          .content {
            padding-top: 48px;

            .title {
              margin-bottom: 8px;
              padding: 0 15px;
              overflow: hidden;
              color: #DC4A48;
              font-size: 18px;
              font-weight: bold;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .publish-num {
              margin-bottom: 15px;
              color: #333;
              font-size: 14px;
              line-height: 20px;
            }

            .date {
              display: inline-block;
              width: 95px;
              padding: 0 5px;
              color: #DC4A48;
              line-height: 25px;
              border: 1px solid #DC4A48;
              border-radius: 2px;
            }
          }
        }

        .template-bottom {
          padding: 0 6px;
          color: #333;
          font-size: 12px;
          line-height: 27px;
          text-align: right;
          border-top: 1px solid #e5e5e5;

          ::v-deep .el-checkbox__inner {
            border-radius: 50%;
          }
        }

        .add-img {
          width: 90px;
          height: 90px;
          margin-bottom: 20px;
          cursor: pointer;
        }
      }

      .template-wrapper.new {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 243px;
        border: none;
      }
    }
  }
}

.contain {
  overflow-y: auto;

  .head {
    text-align: center;

    .head-title {
      font-weight: bold;
      font-size: 24px;
      color: #FF0D0D;
    }

    .star-insert {
      margin: 10px;
      font-size: 14px;
      line-height: 20px;
    }

    .line {
      height: 1px;
      border: 2px solid #FF0D0D;
      margin-top: 26px;
      margin-bottom: 35px;
    }
  }

  .content {
    .div-box {
      .text {
        font-size: 16px;
        line-height: 24px;
      }

      .image {
        width: 100%;
        height: 100%;
      }
    }
  }
}

::v-deep .el-dialog {
  width: 60%;
  height: 90%;
  overflow: hidden;

  .el-dialog__body {
    height: 90%;
    overflow-y: auto;
  }

  .el-dialog__footer {
    padding: 10px 0px;
    position: absolute;
    bottom: 0px;
    display: flex;
    justify-content: center;
    width: 100%;
    background: rgba(74, 77, 81, .8);
  }
}
</style>
