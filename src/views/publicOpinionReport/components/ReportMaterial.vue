<template>
  <div class="wrap">
    <div class="wrap-lf">
      <el-input class="topSearch" v-model="keyWords" clearable placeholder="" style="width: 100%"
                @clear="getTreeData">
        <template #append>
          <el-button @click="getTreeData">搜索</el-button>
        </template>
      </el-input>
      <div v-loading="searchLoading" class="dataTree">

        <el-tree ref="cusTreeRef" :data="treeDataauto" node-key="id"
                 default-expand-all
                 check-on-click-node
                 :highlight-current="true"
                 :props="defaultProps"
                 @check="handleCheckChangeCus">

          <template #default="{ node, data }">
                    <span
                      :class="`custom-tree-node ${node.level == 2 && data.id == checkedNode.id ? 'activeNode' : ''}`"
                      style="width: 100%">
                        <el-tooltip effect="light" placement="top" :open-delay="1000" :content="node.label">
                            <span class="treeitemTitle" v-if="node.level == 1">{{ node.label }}</span>
                            <span class="treeitemTitle" v-if="node.level == 2">{{ node.label }}({{data.count || 0}}/10000)</span>
                        </el-tooltip>
                        <el-dropdown style="float: right"
                                     v-if="!['默认素材组', '默认素材库'].includes(data.folderName)">
                            <span class="el-dropdown-link">
                                <img class="gearIcon" src="@/assets/images/gear.svg" alt=""
                                     @click.stop="clickGear(node, data)"/>
                            </span>

                            <el-dropdown-menu slot="dropdown">
                                <!-- <el-dropdown-item
                                    @click.native="changeSort(data.id, '4', data.fatherid || '', node.level == 1 ? 'type' : 'task')">
                                    置底
                                </el-dropdown-item>
                                   <el-dropdown-item
                                    @click.native="changeSort(data.id, '2', data.fatherid || '', node.level == 1 ? 'type' : 'task')">
                                    下移
                                </el-dropdown-item> -->
                                <el-dropdown-item
                                  @click.native="editTaskClass(data)">重命名</el-dropdown-item>
                                <el-dropdown-item
                                  @click.native="deleteTask(data.id, node.level == 1 ? 'type' : 'task')">
                                    删除
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </span>
          </template>
        </el-tree>

      </div>
      <div class="addTaskButton" @click="addLibrary"><img src="@/assets/images/add.png" alt=""> 点我创建素材库</div>

      <!-- 创建素材库 -->
      <el-dialog
        title="素材库"
        :visible.sync="dialogVisible"
        width="40%"
        append-to-body>
        <el-form ref="materialForm" :model="materialForm">
          <el-form-item label="" v-if="isShow">
            <el-select v-model="materialForm.parentId" placeholder="请选择" style="width:90%">
              <el-option :label="item.folderName" :value="item.id" v-for="item in treeDataauto"
                         :key="item.id"></el-option>
            </el-select>
            <el-tooltip content="点击创建新分组" placement="top" effect="light">
              <img src="@/assets/images/addBtn.svg" alt="" class="addBtn" @click="createGroup">
            </el-tooltip>
          </el-form-item>
          <el-form-item label="" v-if="isShow">
            <el-input v-model.trim="materialForm.folderName" placeholder="请输入素材库名称"></el-input>
          </el-form-item>
          <el-form-item label="" v-if="!isShow">
            <el-input v-model.trim="materialForm.folderName" placeholder="请输入新建素材组名称"></el-input>
          </el-form-item>

        </el-form>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="saveFile('materialForm')">确 定</el-button>
            <el-button @click="dialogVisible = false" v-if="isShow">取 消</el-button>
            <el-button @click="backFile" v-if="!isShow">返 回</el-button>
          </span>
      </el-dialog>

    </div>
    <div class="wrap-rg" v-if="showTable">
      <div class="nav-search">
        <el-form :inline="true" :model="queryForm" class="demo-form-inline">
          <el-form-item label="">
            <el-select v-model="queryForm.type">
              <el-option label="全部来源" value=""></el-option>
              <el-option v-for="dict in typeList" :label="dict.dictLabel" :value="dict.dictValue"
                         :key="dict.dictValue"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="">
            <el-date-picker v-model="queryForm.startTime" :picker-options="startOption" type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss" placeholder="发布时间" class="timeRange"/>
            -
            <el-date-picker v-model="queryForm.endTime" :picker-options="endOption" type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss" placeholder="发布时间" class="timeRange"/>
          </el-form-item>
          <el-form-item>
            <el-input v-model="queryForm.search" clearable placeholder="在结果中搜索，支持单个词组"
                      class="input-with-select">
              <template #prepend>
                <el-select v-model="queryForm.searchType" placeholder="Select" style="width: 90px">
                  <el-option label="按全文" :value="0"/>
                  <el-option label="按标题" :value="1"/>
                  <el-option label="按正文" :value="2"/>
                  <el-option label="按作者" :value="3"/>
                </el-select>
              </template>
            </el-input>
          </el-form-item>
          <el-button :loading="searchLoading" type="primary" @click="submitSearch">搜索</el-button>
          <el-button type="primary" @click="createReport">创建报告</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-checkbox :disabled="total==0" v-model="pageChecked" @change="allSelect">本页全选</el-checkbox>
        <!-- <el-checkbox :disabled="total==0" v-model="checked" @change="allSelect">素材全选</el-checkbox> -->
        <el-button :disabled="multipleSelection.selectedRows.length == 0" style="margin-left:25px" type="text" primary
                   icon="el-icon-delete" @click="deleteMaterial(null)">删除
        </el-button>
      </div>
      <!-- table -->
      <div class="dataTable-report">
        <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" border
                  :header-cell-class-name="cellClass"
                  style="width: 100%" :header-cell-style="{background:'#fcfcfd'}" :class="titleFixed?'result-fiexd':''"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center"></el-table-column>
          <el-table-column prop="title" label="" align="left" header-align="left">

            <template slot="header">
              <div class="header-title">
                <span>{{checkedNode.folderName}}（共包含素材 <span style="color:#247CFF;">{{ total }}</span> 条）</span>
                <div class="jump-page">
                  <i class="el-icon-arrow-left" @click="goLeft"></i>
                  <el-input-number size="mini" @change="submitSearch()" v-model="queryForm.pageNum" :max="totalPage"
                                   :min="1" placeholder="请输入内容"></el-input-number>
                  <span class="jump-line">/</span>
                  <span>{{totalPage}}</span>
                  <i class="el-icon-arrow-right" @click="goRight"></i>
                </div>
                <span class="title-rg" @click="addMaterial" style="visibility:hidden">如何添加素材?</span>
              </div>
            </template>
            <template #default="scope">
              <div :class="scope.row.isRead==1?'tableItemTitle cover-column':'tableItemTitle'">
                <div class="tableTitle" @click="goDetail(scope.row)">
                  <el-tooltip placement="top" effect="light" raw-content>
                    <div slot="content">
                      <div v-html="scope.row.title"></div>
                    </div>
                    <div class="tableTitleSpan">
                      <span>{{(queryForm.pageNum - 1) * queryForm.pageSize + scope.$index + 1}}. </span>
                      <span v-html="scope.row.title"></span>
                    </div>
                  </el-tooltip>
                  <el-select v-model="scope.row.emotionFlag"
                             :class="scope.row.emotionFlag==2?'emotionSelect table-nosense':scope.row.emotionFlag==1?'emotionSelect table-sense':'emotionSelect table-neutral'"
                             size="mini"
                             placeholder="请选择" @change="(val)=>{changeSensitive(val,scope.row)}">
                    <el-option :key="2" label="非敏感" :value="2"></el-option>
                    <el-option :key="1" label="敏感" :value="1"></el-option>
                    <el-option :key="0" label="中性" :value="0"></el-option>
                  </el-select>
                </div>
                <div class="tableMain" v-html="scope.row.text" @click="goDetail(scope.row)"></div>
                <div class="tableFoot">
                  <div class="footButtonGroup">
                    <div>
                      <div class="text-three">
                        <img src="@/assets/report/send-icon.png" alt="">
                        发布时间：{{ scope.row.publishTime }}<span style="margin:0 20px;">相似文章数: {{ scope.row.similarCount}}</span>
                        来源：{{scope.row.typeName}}
                      </div>
                    </div>
                    <div style="white-space: nowrap;">
                      <div class="footButonItem" @click="goOrigin(scope.row.url)">
                        <img src="@/assets/images/goOrigin.png" alt="" class="footIcon"/>
                        <span>原文</span>
                      </div>
                      <div class="footButonItem" @click="deleteMaterial(scope.row.id)">
                        <img src="@/assets/images/material-delete.png" alt="" class="footIcon"/>
                        <span>删除</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <img class="read-img" v-if="scope.row.isRead==1" src="@/assets/images/read.png" alt="">
              <img class="follow-img" v-if="scope.row.follow==1" src="@/assets/images/follow.png" alt="">
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total>0" :total="total" :page.sync="queryForm.pageNum" :limit.sync="queryForm.pageSize"
                    @pagination="pagination"/>
      </div>
      <!-- 素材弹窗 -->
      <!-- <el-dialog
        title="如何添加素材"
        :visible.sync="materialDialog"
        append-to-body>
        <el-carousel indicator-position="outside">
            <el-carousel-item v-for="item in 4" :key="item">
              <h3>{{ item }}</h3>
            </el-carousel-item>
        </el-carousel>
      </el-dialog> -->

    </div>
    <!-- 重命名弹窗 -->
    <el-dialog :visible.sync="dialogFile" title="修改名称" width="30%" append-to-body>
      <el-form ref="classForm" :model="materialForm" @submit.prevent :rules="rules">
        <el-form-item label="" prop="folderName">
          <el-input v-model.trim="materialForm.folderName"/>
        </el-form-item>
      </el-form>
      <template #footer>
            <span class="dialog-footer">
                <el-button type="primary" @click="submitFile">确定</el-button>
                <el-button @click="dialogFile = false">取消</el-button>
            </span>
      </template>
    </el-dialog>
    <!--  -->
    <div class="wrap-rg no-material" v-if="total == 0 && !showTable">
      <div class="noData">
        <img src="@/assets/report/more-material.png" alt="">
        <div class="tip">该素材库无素材，您可以选择以下方式添加素材</div>
        <div class="method"><span class="bot">1</span>在信息列表添加素材： <span class="color-main"
                                                                                @click="getMaterial('舆情')">舆情监测 </span>|
          <span class="color-main" @click="getMaterial('全文')">全文搜索</span></div>
        <!-- <div class="method"><span class="bot">2</span>提取生成简报的素材：  <span class="color-main" @click="getMaterial('报告')">报告列表</span></div> -->
      </div>
    </div>
  </div>
</template>

<script>
import {similarCount} from "@/api/search/index";
import {searchData, updateEmotion} from "@/api/search/index";
import {getFolderList, getPageList, saveFolder, deleteFolder, deleteMaterialList} from '@/api/report/material.js'

export default {
  data() {
    return {
      keyWords: '',
      dialogFile: false,
      searchLoading: false,
      treeDataauto: [],
      defaultProps: {
        children: 'children',
        label: 'folderName',
        disabled: function (data, node) {
          if (data.children && data.children.length > 0) {
            return true
          } else {
            return false
          }
        }
      },
      checkedNode: {},//树选中的子节点(单选)
      dialogVisible: false,
      materialDialog: false,
      materialForm: {
        parentId: '',
        folderName: '',
      },
      rules: {
        folderName: [
          {required: true, message: '请输入名称', trigger: 'blur'},
          {max: 20, message: '长度不能超过20个字符', trigger: 'blur'}
        ]
      },
      isShow: true,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        type: '',
        search: '',
        folderId: '',
        searchType: 0,
        startTime: undefined,
        endTime: undefined,
      },
      startOption: {
        disabledDate: this.startDisable
      },
      endOption: {
        disabledDate: this.endDisable
      },
      total: 0,
      totalPage: 0,
      searchLoading: false,
      tableLoading: false,
      tableData: [],
      titleFixed: false,
      multipleSelection: {selectedRows: []},
      pageChecked: false,
      checked: false,
      typeList: [],
      showTable: false,
      currentNodeId: null,
    }
  },
  //  watch: {
  //       checkedNode(newVal,oldVal) {
  //           if (newVal) {//当前tab显示时，树节点改变才刷新
  //               this.$nextTick(() => {
  //                   setTimeout(() => {
  //                       this.init()
  //                   }, 200);
  //               });
  //           }
  //       },
  // },
  created() {
    this.getDicts('sys_media_type').then(res => {
      this.typeList = res.data
    })
    this.getTreeData()

  },
  computed: {
    // 计算属性来判断是否应该显示下拉菜单链接
    shouldShowDropdownLink(folderName) {
      return !['默认素材组', '默认素材库'].includes(folderName)
    }
  },
  mounted() {

  },
  methods: {
    // // // 初始化
    // async init(){
    //     this.queryForm.folderId = this.checkedNode.id
    //     this.submitSearch()
    // },
    //  async enrichArrayWithDetails(arrayA) {
    //       // 使用map创建一个Promise数组
    //       const promises = arrayA.map(async item => {
    //           const detail = await similarCount({ md5: item.md5,startTime: item.publishTime,endTime:item.publishTime });
    //           return { ...item, similarCount: detail.data };
    //       });
    //       const enrichedArray = await Promise.all(promises);
    //       return enrichedArray;
    //   },
    cellClass(row) {
      if (row.columnIndex === 0) {
        return 'disabledCheck'
      }
    },
    getMaterial(value) {
      if (value == '舆情') {
        this.$router.push('/publicOpinionMonitor')
      } else if (value == '全文') {
        this.$router.push('/fullSearch/searchRank')
      } else {
        this.$emit('changeIndex', 3)
      }
    },
    startDisable(time) {
      return time.getTime() > new Date(this.queryForm.endTime).getTime()
    },
    // 结束日期规则
    endDisable(time) {
      return time.getTime() < new Date(this.queryForm.startTime).getTime()
    },
    saveFile() {
      if (this.materialForm.folderName) {
        // this.$refs.materialForm.validate(valid => {
        //   if (valid) {
        if (this.materialForm.folderName.length > 20) {
          this.$message.error('长度不能超过20个字符')
        } else {
          saveFolder(this.materialForm).then(res => {
            if (res.code == 200) {
              this.$message.success('创建成功')
              this.getTreeData()
              this.dialogVisible = false
            }
          })
        }


        //   }
        // })
      } else {
        this.$message.error('请输入名称')
      }

    },
    createReport() {
      this.$emit('changeIndex', 0)
    },
    backFile() {
      this.isShow = true
      this.materialForm.parentId = this.treeDataauto[0]?.id
    },
    addLibrary() {
      this.dialogVisible = true
      this.isShow = true
      this.materialForm.folderName = ''
    },
    createGroup() {
      this.isShow = !this.isShow
      this.materialForm.parentId = ''
    },
    // 添加素材
    addMaterial() {
      this.materialDialog = true
    },
    goRight() {
      if (this.totalPage > this.queryForm.pageNum && this.totalPage > 0) {
        this.queryForm.pageNum++
        this.submitSearch()
      }
    },
    goLeft() {
      if (this.totalPage >= this.queryForm.pageNum && this.queryForm.pageNum > 1) {
        this.queryForm.pageNum--
        this.submitSearch()
      }
    },
    // 全选
    allSelect(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },
    // 切换敏感类型
    async changeSensitive(val, row) {
      let res = await updateEmotion({md5: row.md5, emotionFlag: val})
      this.$set(row, 'emotionFlag', val);
      if (res.code == 200) {
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'emotionFlag', row.originFlag);
        this.$message.error(res.msg)
      }
    },
    // 跳转详情页
    async goDetail(row) {
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: {id: row.contentId, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5}
      })
      window.open(fullPath.href, '_blank')
    },
    // 跳转详情页
    goOrigin(url) {
      window.open(url, '_blank')
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.multipleSelection.selectedRows = val
    },
    // 分页查询
    pagination(page) {
      this.queryForm.pageNum = page.page
      this.queryForm.pageSize = page.limit
      this.submitSearch()
    },
    // 查询列表
    async submitSearch() {
      try {
        this.tableLoading = true
        this.searchLoading = true
        let params = JSON.parse(JSON.stringify(this.queryForm))
        const res = await getPageList(params)
        this.tableData = res.rows.map(item => {
          item.originFlag = item.emotionFlag;
          // 初始化 `urlAccessStatus` 属性
          this.$set(item, 'urlAccessStatus', null);
          return item;
        });
        this.tableData = res.rows
        this.tableData.map((item) => {
          item.originFlag = item.emotionFlag
        })
        this.total = Number(res.total)

        this.totalPage = Math.ceil(this.total / this.queryForm.pageSize)
        this.searchLoading = false
        this.tableLoading = false
        // this.tableData = await this.enrichArrayWithDetails(this.tableData)
        console.log('this. :>> ', this.tableData);
      } catch (error) {
        console.log(error)
        this.searchLoading = false
        this.tableLoading = false
      }
    },
    // 获取素材树
    async getTreeData() {
      this.searchLoading = true

      let resauto = await getFolderList({name: this.keyWords})
      if (resauto.code == 200) {
        this.treeDataauto = resauto.data

        this.materialForm.parentId = this.treeDataauto[0]?.id
        this.checkedNode.folderName = this.treeDataauto[0]?.children[0]?.folderName
        const count = this.treeDataauto[0]?.children[0]?.count || 0
        this.showTable = count > 0 ? true : false
        this.$nextTick(() => {
          if (this.treeDataauto && this.treeDataauto[0] && this.treeDataauto[0].children && this.treeDataauto[0].children.length > 0) {
            const selectId = this.treeDataauto[0].children[0].id;
            this.$refs.cusTreeRef.setCurrentKey(selectId);
            this.queryForm.folderId = selectId
            this.submitSearch()

          }
        });
      }
      this.searchLoading = false
    },
    clickGear(node, data) {
      console.log(node, data)
    },
    //获取所有子节点
    getAllChilds(data) {
      const childs = []
      if (data && data.length > 0) {
        data.forEach(parent => {
          parent.children?.forEach(child => {
            childs.push(child)
          })
        })
      }
      return childs
    },
    // 重命名
    editTaskClass(item) {
      console.log('item 12:>> ', item);
      this.dialogFile = true
      this.materialForm.folderName = item.folderName
      this.materialForm.id = item.id
    },
    submitFile() {
      const params = {
        folderName: this.materialForm.folderName,
        id: this.materialForm.id
      }
      this.$refs.classForm.validate(valid => {
        if (valid) {
          saveFolder(params).then(res => {
            if (res.code == 200) {
              this.$message.success('修改成功')
              delete this.materialForm.id
              this.dialogFile = false
              this.getTreeData()
            }
          })
        }
      })
    },
    changeSort(id, sortType, fatherid, who) {
      this.searchLoading = true
      // if (who == 'type') {
      //     changeTypeSort({ typeId: id, sortType }).then((res) => {
      //         this.getTreeData()
      //         this.msgSuccess("修改成功");
      //     }).catch((err) => {
      //         this.searchLoading = false
      //     })
      // } else {
      //     changeTaskSort({ planId: id, typeId: fatherid, sortType }).then((res) => {
      //         this.getTreeData()
      //         this.msgSuccess("修改成功");
      //     }).catch((err) => {
      //         this.searchLoading = false
      //     })
      // }
    },
    // 删除
    deleteTask(id, who) {
      this.$confirm(`确认删除此素材库吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.searchLoading = true
        deleteFolder(id).then((res) => {
          this.$message.success('删除成功')
          setTimeout(() => {
            this.getTreeData()
          }, 500)
        }).catch((err) => {
          this.searchLoading = false
        })

      })
    },
    // 删除素材
    deleteMaterial(id) {
      const ids = id || this.multipleSelection.selectedRows.map(item => item.id)
      this.$confirm(`确认删除该条素材吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.searchLoading = true
        deleteMaterialList(ids).then((res) => {
          this.$message.success('删除成功')
          setTimeout(() => {
            this.getTreeData()
          }, 500)
        })
      })
    },
    //方案选中树节点
    handleCheckChangeCus(data, checked, indeterminate) {
      console.log('data 1111:>> ', data);
      this.queryForm.folderId = data.id
      if (data.children === null) {
        this.checkedNode = data
        this.showTable = data.count > 0 ? true : false
        this.submitSearch()
        // this.$emit('checkNode', data)
      } else {
        console.log('111 :>> ', 111);
      }
    },
  }
}
</script>
<style scoped lang="scss">
@import "./ReportMaterial.scss";
</style>
<style lang="scss" scoped>

.wrap {
  // height: 100%;
  height: calc(100vh - 120px);
  overflow-y: auto;
  display: flex;

  .wrap-lf {
    width: 280px;
    height: 100%;
    background: #FFFFFF;
    margin-right: 10px;
    padding: 30px 20px 0px 20px;

    .topSearch {
      ::v-deep .el-input-group__append {
        color: #fff;
        background: #247CFF;
        border: none;
      }

      margin-bottom: 38px;
    }
  }

  .wrap-rg {
    flex: 1;
    height: 100%;
    background: #FFFFFF;
    padding: 30px 40px 0px 40px;
    overflow-y: auto;

  }

  .no-material {
    display: flex;
    justify-content: center;
    align-items: center;

    .noData {
      width: 380px;
      font-size: 16px;
      color: #333;
      text-align: center;

      img {
        width: 48px;
        height: 52px;
        margin-bottom: 10px;
      }

      .color-main {
        color: #247CFF;
        cursor: pointer;
      }

      .tip {
        border-bottom: 1px solid #e5e5e5;
        margin-top: 10px;
        padding-bottom: 15px;
        text-align: left;
      }

      .method {
        margin-top: 5px;
        padding: 10px 0px;
        text-align: left;

        .bot {
          background: #247CFF;
          color: #FFFFFF;
          font-size: 14px;
          display: inline-block;
          width: 20px;
          height: 20px;
          line-height: 20px;
          border-radius: 50%;
          text-align: center;
          margin-right: 10px;
        }
      }
    }
  }
}

.dataTree {
  // height: 640px;
  height: calc(100% - 127px);
  overflow-y: auto;
  position: relative;

  .activeNode {
    color: #409eff;
  }

  .custom-tree-node {
    width: 100%;
    position: relative;

    .gearIcon {
      display: none;
    }

    .iconGroup {
      position: absolute;
      right: 212px;
      bottom: -2px;

      img {
        margin-right: 2px;
      }
    }

    .markIcon {
      height: 15px;
      display: inline-block;
      // vertical-align: middle;
      margin-right: 2px;
    }
  }

  .custom-tree-node:hover {
    .gearIcon {
      display: inline;
      width: 13px;
      position: relative;
      top: 2px;
    }
  }

  p {
    height: 40px;
    line-height: 40px;
    background: #EBEFF4;
    font-weight: bolder;
    padding-left: 20px;
  }

  ::v-deep .el-tree {
    .el-tree-node:hover {
      // background-color:#F5F7FA;
    }

    .el-dropdown {
      font-size: 12px;
    }

    .el-tree-node__content {
      // margin-left: 20px;
      height: 35px;
    }

    .is-expanded {
    }

    // is-leaf el-tree-node__expand-icon el-icon-caret-right
    .el-tree-node__expand-icon.is-leaf::before {
      content: url("~@/assets/images/closedFolder.png") !important;
    }

    .el-tree-node__children {
      .el-tree-node__expand-icon.is-leaf::before {
        content: '' !important;
      }
    }

    .el-icon-caret-right::before {
      content: url("~@/assets/images/closedFolder.png");
    }

    .el-tree-node__expand-icon.expanded::before {
      content: url("~@/assets/images/openedFolder.png");
    }

    .el-tree-node__expand-icon.expanded {
      -webkit-transform: none;
      transform: none
    }

    .el-tree-node__expand-icon.is-leaf::before {
      content: '';
    }
  }
}

.addTaskButton {
  text-align: center;
  padding: 6px 0;
  margin: 0 auto;
  cursor: pointer;

  width: 148px;
  background: #247CFF;
  border-radius: 2px;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;

  img {
    width: 12px;
    height: 12px;
  }

  // &:hover {
  //     border: 1px solid #409eff;
  //     color: #409eff;
  // }
}

.addBtn {
  width: 28px;
  height: 28px;
  color: #247CFF;
  margin-left: 6px;
  margin-bottom: -8px;
  cursor: pointer;
}

.timeRange {
  width: 170px;

  ::v-deep .el-input__inner {
    padding-right: 6px;
  }
}

.input-with-select {
  width: 400px;
}

::v-deep .el-dialog__footer {
  text-align: center;
}

::v-deep .el-dialog__header {
  background: #247CFF;
  padding-bottom: 20px;

  .el-dialog__title {
    color: #fff;
  }

  .el-dialog__close {
    color: #fff;
  }
}

</style>
