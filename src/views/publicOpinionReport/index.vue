<template>
  <div :class="`dataContainer`">
    <div class="dataSidebar">
      <!-- <div class="tree-button" @click="treeFlag=!treeFlag">
          <img src="@/assets/images/retract.svg" alt="">
      </div> -->
      <ul class="toolbar-menus">
        <li
          :class="{'active': currentIndex == index}"
          v-for="(item,index) in menus"
          :key="index"
          @click="currentIndex = index">
          <img :src="currentIndex == index ? item.activeImg : item.img" alt="">
          {{ item.name }}
        </li>
      </ul>
    </div>
    <div class="report-content">
      <component
        v-for="(tab, index) in menus"
        :key="index"
        @changeIndex="changeIndex"
        :tempId="tempId"
        :is="currentIndex === index ? tab.component : null"/>
    </div>
  </div>
</template>

<script>
import CreateReport from './components/CreateReport.vue'
import ReportList from './components/ReportList.vue'
import ReportMaterial from './components/ReportMaterial.vue'
import ReportTemplate from './components/ReportTemplate.vue'

export default {
  name: 'publicOpinionReport',
  components: {CreateReport, ReportList, ReportMaterial, ReportTemplate},
  data() {
    return {
      treeFlag: true,
      menus: [
        {
          name: '创建报告',
          img: require('@/assets/images/report-add.png'),
          activeImg: require('@/assets/images/report-add-active.png'),
          component: CreateReport
        },
        {
          name: '报告素材',
          img: require('@/assets/images/material.png'),
          activeImg: require('@/assets/images/material-active.png'),
          component: ReportMaterial
        },
        {
          name: '报告模板',
          img: require('@/assets/images/template.png'),
          activeImg: require('@/assets/images/template-active.png'),
          component: ReportTemplate
        },
        {
          name: '报告列表',
          img: require('@/assets/images/list.png'),
          activeImg: require('@/assets/images/list-active.png'),
          component: ReportList
        }
      ],
      currentIndex: 0,
      tempId: undefined

    }
  },
  mounted() {
    console.log('object :>> ', this.$route);
    if (this.$route.params.msg) {
      this.currentIndex = this.$route.params.msg
    }
    if (this.$route.query.tempId) {
      this.currentIndex = 0
      this.tempId = this.$route.query.tempId
    }
  },
  created() {

  },
  methods: {
    changeIndex(index, tempId) {
      this.tempId = tempId
      this.currentIndex = index
    }
  }
}
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
