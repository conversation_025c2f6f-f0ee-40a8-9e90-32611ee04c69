<template>
  <div class="publicOpinionWarning">
    <div class="search-wrap">
      <div class="title"><span>筛选条件</span></div>
      <el-form :inline="true" :model="queryForm" size="small" class="demo-form-inline">

        <el-form-item label="搜索时间:" label-width="100px">
          <el-date-picker v-model="queryForm.updateStart" type="datetime" placeholder="开始时间" clearable
            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsUpdateStart"
            @change="updateStartChange">
          </el-date-picker>
          -
          <el-date-picker v-model="queryForm.updateEnd" type="datetime" placeholder="结束时间" clearable
            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsUpdateEnd"
            @change="updateEndChange">
          </el-date-picker>
        </el-form-item>



        <el-form-item label="原文时间:" label-width="100px">
          <el-date-picker v-model="queryForm.publishStart" type="datetime" placeholder="开始时间" clearable
            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsStart"
            @change="startChange">
          </el-date-picker>
          -
          <el-date-picker v-model="queryForm.publishEnd" type="datetime" placeholder="结束时间" clearable
            format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsEnd"
            @change="endChange">
          </el-date-picker>
        </el-form-item>

        <el-form-item label="信息属性" prop="emotionFlag" label-width="100px">
          <el-select v-model="queryForm.emotionFlag" placeholder="请选择信息属性" clearable size="small">
            <el-option v-for="(item,index) in emotionData" :key="index" :label="item.name" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="来源类型" prop="type" label-width="100px">
          <el-select v-model="queryForm.type" placeholder="请选择类型" clearable size="small" multiple collapse-tags>
            <el-option v-for="dict in mediaList" :key="dict.dictValue" :label="dict.dictLabel"
              :value="dict.dictValue" />
          </el-select>
        </el-form-item>

        <el-form-item label="信息内容:" label-width="100px">
          <el-input v-model="queryForm.kw" size="small" clearable placeholder="请输入信息内容" />
        </el-form-item>
      </el-form>
      <div class="search-btn">
        <el-button type="primary" @click="submitSearch('query')">查询</el-button>
      </div>
    </div>
    <div class="wrap-content">
      <div class="data-table" ref="data_table">
        <div :class="titleFixed?'dataTot is_data_fixed':'dataTot'">
          <div class="mt-4 dataSel">
            <span v-show="total>0">
              <el-select size="small" v-model="exportNum" placeholder="选择导出条数" style="width: 125px;margin-right: 10px;"
                clearable @change="exportNumChange">
                <el-option label="选择当前页" value="0" />
                <el-option label="前500条" value="500" />
                <el-option label="前1000条" value="1000" />
                <el-option label="前5000条" value="5000" />
              </el-select>
              <el-dropdown placement="bottom" ref="Dropdown" trigger="click">
                <div><el-button type="text" style="margin-right: 10px;">批量导入至素材</el-button></div>
                <el-dropdown-menu slot="dropdown" class="subClass">
                  <el-dropdown-item v-for="item in treeDataauto" :key="item.id" :command="item.id"
                    @mouseenter="() => {$refs.Dropdown.show()}">
                    <template v-if="!item.children">{{ item.folderName }}</template>
                    <template v-else>
                      <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                        <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                        <span class="el-dropdown-link" style="color: #606266;">
                          {{ item.folderName }}<i v-if="item.children.length != 0"
                            class="el-icon-arrow-right el-icon--right" />
                        </span>
                        <el-dropdown-menu slot="dropdown" class="menuClass">
                          <el-dropdown-item v-for="subItem in item.children" :key="subItem.id" :command="subItem.id"
                            @click.native="importToMaterials(subItem)">
                            {{ subItem.folderName }}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </template>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button :loading="downloadLoading" type="text" @click="exportExcel()">导出Excel</el-button>
            </span>
            <span></span>
          </div>
        </div>
        <div class="dataTable">
          <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" border style="width: 100%"
            :header-cell-style="{background:'#fcfcfd'}" :class="titleFixed?'result-fiexd':''"
            @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center"></el-table-column>
            <el-table-column prop="title" label="标题" align="left" header-align="center">
              <template #default="scope">
                <!-- <div :class="scope.row.readFlag==1?'tableItemTitle cover-column':'tableItemTitle'"> -->
                <div :class="'tableItemTitle'">
                  <div class="tableTitle" @click="goDetail(scope.row)">
                    <img class="tableItemImg" :src="transImage(scope.row.type,scope.row.host||'')" alt="无图片" />
                    <el-tooltip placement="top" effect="light" raw-content>
                      <div slot="content">
                        <div v-html="scope.row.title"></div>
                      </div>
                      <div class="tableTitleSpan">
                        <span>{{(queryForm.pageNum - 1) * queryForm.pageSize + scope.$index +
                          1}}. </span>
                        <span v-html="scope.row.title"></span>
                      </div>
                    </el-tooltip>
                    <!-- <el-select v-model="scope.row.emotionFlag"
                        :class="scope.row.emotionFlag==2?'emotionSelect table-nosense':scope.row.emotionFlag==1?'emotionSelect table-sense':'emotionSelect table-neutral'"
                        size="mini" placeholder="请选择"
                        @change="(val)=>{changeSensitive(val,scope.row)}">
                        <el-option :key="2" label="非敏感" :value="2"></el-option>
                        <el-option :key="1" label="敏感" :value="1"></el-option>
                        <el-option :key="0" label="中性" :value="0"></el-option>
                    </el-select> -->
                    <p class="article-type"
                      :style="scope.row.emotionFlag==2?'background-color: #a8dfef;color: #006e8f;':scope.row.emotionFlag==1?'background-color: #fcc3c2;color: #b52626;':'background-color: #ffdc70;color: #8e5d00;'">
                      {{scope.row.emotionFlag==2?"非敏感":scope.row.emotionFlag==1?"敏感":"中性"}}</p>
                    <p class="article-type" style="background-color: #339593" v-show="scope.row.isOriginal">原创</p>
                    <p class="article-type" v-show="!scope.row.isOriginal">转载</p>
                    <!-- <p class="article-type" style="background-color: #F7B8B3;color: #FA2C1C;"
                        v-show="scope.row.warned==1">流转中</p> -->
                    <p class="article-type" style="background-color: #B2E8F3;color: #00B4D8;"
                      v-show="scope.row.deal==1">已处置</p>
                    <p class="article-type" style="background-color: #D8D8D8;color: #999999;"
                      v-if="scope.row.urlAccessStatus==0">已删除</p>
                    <p class="article-type" style="background-color: #D5F8D1;color: #3EC82F;" v-else>可访问</p>
                    <!-- <el-select v-model="scope.row.riskGrade"
                             :class="['dangerSelect',scope.row.riskGrade==2?'table-nosense':scope.row.riskGrade==1?'table-sense':'table-neutral']"
                             size="mini"
                             placeholder="暂无" @change="(val)=>changeRiskGrade(val,scope.row)">
                      <el-option :key="2" label="低风险" :value="2"></el-option>
                      <el-option :key="0" label="中风险" :value="0"></el-option>
                      <el-option :key="1" label="高风险" :value="1"></el-option>
                    </el-select> -->
                  </div>
                  <div class="tableMain" v-html="scope.row.text" @click="goDetail(scope.row)"></div>
                  <div class="tableFoot">
                    <div class="footInfo">
                      <img src="@/assets/images/message.png" alt="" class="footIcon" />
                      <span>{{scope.row.commentNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <img src="@/assets/images/book.png" alt="" class="footIcon" />
                      <span>{{scope.row.readNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <img src="@/assets/images/good.png" alt="" class="footIcon" />
                      <span>{{scope.row.likeNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <img src="@/assets/images/share.png" alt="" class="footIcon" />
                      <span>{{scope.row.reprintNum || 0}}</span>
                    </div>
                    <div class="footButtonGroup">
                      <div>
                        <div class="footButonItem" v-show="scope.row.hitWords">
                          <el-tooltip effect="light" content="涉及词" placement="top">
                            <img src="@/assets/images/keyword.png" alt="" class="footIcon" />
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.hitWords" placement="top">
                            <span class="keyword">{{ scope.row.hitWords || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-show="scope.row.hitCourtNames">
                          <el-tooltip effect="light" content="涉及法院" placement="top">
                            <img src="@/assets/images/court.png" alt="" class="footIcon" />
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.hitCourtNames" placement="top">
                            <span class="keyword" style="color: #247CFF;">{{
                              scope.row.hitCourtNames || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-show="scope.row.contentAreaCodeName">
                          <el-tooltip effect="light" content="精准地域" placement="top">
                            <img src="@/assets/images/areaDetail.png" alt="" class="footIcon" />
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.contentAreaCodeName" placement="top">
                            <span class="keyword" style="color: #356391;">{{
                              scope.row.contentAreaCodeName || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-if="scope.row.contentMeta&&scope.row.contentMeta.length">
                          <el-tooltip effect="light" content="信息分类" placement="top">
                            <img src="@/assets/images/contentMeta.png" alt="" class="footIcon" />
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.contentMeta.join(' ')" placement="top">
                            <span class="keyword" style="color: #666;">{{ scope.row.contentMeta.join(' ') }}</span>
                          </el-tooltip>
                        </div>
                      </div>

                      <div style="white-space: nowrap;">
                        <div class="footButonItem" @click="copyAritical(scope.row)">
                          <img src="@/assets/images/copy.png" alt="" class="footIcon" />
                          <span>复制</span>
                        </div>
                        <!-- <div class="footButonItem" @click="openSendMsg(scope.row)">
                            <img src="@/assets/images/send.png" alt="" class="footIcon" />
                            <span>报送</span>
                        </div> -->
                        <div class="footButonItem" @click="goOrigin(scope.row.url)">
                          <img src="@/assets/images/goOrigin.png" alt="" class="footIcon" />
                          <span>查看原文</span>
                        </div>
                        <div class="footButonItem" @click="copyText(scope.row.url,true)">
                          <img src="@/assets/images/copyLink.png" alt="" class="footIcon" />
                          <span>拷贝地址</span>
                        </div>
                        <el-dropdown>
                          <div class="footButonItem">
                            <img src="@/assets/images/markIcon.png" alt="" class="footIcon" />
                            <span>标记</span>
                          </div>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                              @click.native="markDisposition(scope.row)">{{scope.row.deal==1?'取消处置':'处置'}}
                            </el-dropdown-item>
                            <el-dropdown-item
                              @click.native="markKeyFocus(scope.row)">{{scope.row.follow==1?'取消重点关注':'重点关注'}}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- <img class="read-img" v-if="scope.row.readFlag==1" src="@/assets/images/read.png" alt=""> -->
                <img class="follow-img" v-if="scope.row.follow==1" src="@/assets/images/follow.png" alt="">
              </template>
            </el-table-column>

            <el-table-column prop="updateTime" align="center" width="150px">
              <template slot="header">
                搜索时间
              </template>
              <template slot-scope="scope">
                <div>{{ scope.row.updateTime.substring(0,10) }}</div>
                <div>{{ scope.row.updateTime.substring(11,19) }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="publishTime" align="center" width="150px">
              <template slot="header">
                原文时间
              </template>
              <template slot-scope="scope">
                <div>{{ scope.row.publishTime.substring(0,10) }}</div>
                <div>{{ scope.row.publishTime.substring(11,19) }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="option" label="操作" align="center" width="150px">
              <template #default="scope">
                <el-button type="text" @click="deleteLog(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-show="total>0"
            style="width:100%;min-height: 20px;display:flex;align-items:center;justify-content: space-between;">
            <div style="margin-top:10px;">
              <el-select v-model="exportNum" size="small" placeholder="选择导出条数" style="width: 125px;margin-right: 10px"
                clearable @change="exportNumChange">
                <el-option label="选择当前页" value="0" />
                <el-option label="前500条" value="500" />
                <el-option label="前1000条" value="1000" />
                <el-option label="前5000条" value="5000" />
              </el-select>
              <el-dropdown placement="bottom" ref="Dropdown" trigger="click">
                <div>
                  <el-button type="text" style="margin-right: 10px;">批量导入至素材</el-button>
                </div>
                <el-dropdown-menu slot="dropdown" class="subClass">
                  <el-dropdown-item v-for="item in treeDataauto" :key="item.id" :command="item.id"
                    @mouseenter="() => {$refs.Dropdown.show()}">
                    <template v-if="!item.children">{{ item.folderName }}</template>
                    <template v-else>
                      <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                        <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                        <span class="el-dropdown-link" style="color: #606266;">
                          {{ item.folderName }}<i v-if="item.children.length != 0"
                            class="el-icon-arrow-right el-icon--right" />
                        </span>
                        <el-dropdown-menu slot="dropdown" class="menuClass">
                          <el-dropdown-item v-for="subItem in item.children" :key="subItem.id" :command="subItem.id"
                            @click.native="importToMaterials(subItem)">
                            {{ subItem.folderName }}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </template>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button :loading="downloadLoading" type="text" @click="exportExcel()">导出Excel</el-button>
            </div>
            <pagination :total="total" :page.sync="queryForm.pageNum" :limit.sync="queryForm.pageSize"
              @pagination="pagination" />

          </div>
        </div>
      </div>
    </div>
    <!-- 短信报送 -->
    <SendMsg :visible.sync="sendMsgDialog" :sendMsgRow="sendMsgRow" @visibleChange="visibleChange"></SendMsg>
  </div>
</template>

<script>
import moment from 'moment'
import { copyText, replaceHtml, copyAritical } from "@/utils/index"
import { resetTag, transImage, changeSensitive, changeRiskGrade } from '@/utils/index';
import SendMsg from '../fullSearch/components/sendMsg.vue';
import { updateEmotion, getUrlAccessStatusApi, updateDeal, updateFollow, esBeanMarkListApi, esBeanMarkDeleteApi,esBeanMarkExportApi,esBeanMarkAddMaterialApi } from "@/api/search/index";
import { getFolderList, addFolder } from '@/api/report/material.js'

export default {
  components: { SendMsg },
  data() {
    return {
      searchNum: 0,
      titleFixed: false,
      total: 0,
      downloadLoading: false,
      exportNum: null,
      sendMsgDialog: false,
      sendMsgRow: {},//短信报送对象的信息
      tableLoading: false,
      tableData: [],
      multipleSelection: { selectedRows: [] },
      allTotal: 0,
      totalPage: 0,
      wordsLoading: false,
      transImage,
      changeSensitive,
      changeRiskGrade,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        updateStart: moment(new Date()).subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
        updateEnd: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        publishStart: '',
        publishEnd: '',
        emotionFlag: '',
        type: [],
        kw: '',
      },
      mediaList: [],
      articleData: [
        { name: '是', value: true },
        { name: '否', value: false },
      ],
      emotionData: [
        { name: '中性', value: 0 },
        { name: '敏感', value: 1 },
        { name: '非敏感', value: 2 }
      ],

      pickerOptionsUpdateStart: {
        disabledDate: (time) => {
          let endDateVal = this.queryForm.updateEnd;
          if (endDateVal) {
            const endDate = new Date(endDateVal);
            return time.getTime() > endDate.getTime() && time.toDateString() !== endDate.toDateString();
          }
        },
      },
      pickerOptionsUpdateEnd: {
        disabledDate: (time) => {
          let beginDateVal = this.queryForm.updateStart;
          if (beginDateVal) {
            const beginDate = new Date(beginDateVal);
            return time.getTime() < beginDate.getTime() && time.toDateString() !== beginDate.toDateString();
          }
        },
      },
      pickerOptionsStart: {
        disabledDate: (time) => {
          let endDateVal = this.queryForm.publishEnd;
          if (endDateVal) {
            const endDate = new Date(endDateVal);
            return time.getTime() > endDate.getTime() && time.toDateString() !== endDate.toDateString();
          }
        },
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          let beginDateVal = this.queryForm.publishStart;
          if (beginDateVal) {
            const beginDate = new Date(beginDateVal);
            return time.getTime() < beginDate.getTime() && time.toDateString() !== beginDate.toDateString();
          }
        },
      },


      treeDataauto: [],
    }
  },
  async created() {
    this.getDict()
    this.getTreeData()

    // if (this.$route.query.publishStart) {
    //   this.queryForm.publishStart = this.$route.query.publishStart
    //   this.queryForm.publishEnd = this.$route.query.publishEnd
    // } else {
    //   this.queryForm.publishStart = moment(new Date()).subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss')
    //   this.queryForm.publishEnd = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    // }
    await this.submitSearch()
  },

  mounted() {
    this.titleFixed = false;
    this.$nextTick(() => {
      document.addEventListener('scroll', this.handleScroll, true);
    })
    document.addEventListener("mouseup", (e) => {
      let treeDom = this.$refs.addInput
      if (treeDom) {
        if (!treeDom.contains(e.target)) {
          this.slideFlag = false;
        }
      }
    });
  },
  beforeDestroy() {
    document.removeEventListener('mouseup', this.showSlide);
    document.removeEventListener('scroll', this.handleScroll, true);
  },
  methods: {

    getDict() {
      // 来源类型
      this.getDicts('sys_media_type').then(res => {
        this.mediaList = res.data
      })
    },

    //滚动监听，头部固定
    handleScroll(val) {
      // if (this.tableData.length > 0) {
      //     this.$nextTick(() => {
      //         const tableFixed = this.$refs.data_table
      //         let offsetTop = tableFixed.getBoundingClientRect().top;
      //         this.titleFixed = offsetTop < 100;
      //         if (this.titleFixed) {
      //             setTimeout(() => {
      //                 const dom = document.querySelector('.is_data_fixed')
      //                 const tableDom = document.querySelector('.el-table__header-wrapper')
      //                 if (dom) {
      //                     if (tableFixed.offsetWidth - dom.offsetWidth != 20 || val) {
      //                         dom.style.width = `${tableFixed.offsetWidth - 40}px`
      //                         tableDom.style.width = `${tableFixed.offsetWidth - 40}px`
      //                     }
      //                 }
      //             }, 200);

      //         }
      //     })
      // } else {
      //     this.titleFixed = false;
      // }

      if (this.tableData.length > 0) {
        const tabFixed = this.$refs.data_table
        let offsetTop = tabFixed.getBoundingClientRect().top;

        this.titleFixed = offsetTop < document.querySelector('.fixed-header').offsetHeight;

        const marginDom = document.querySelector('.dataTable')

        if (this.titleFixed) {
          // this.$nextTick(() => {
          const dom = document.querySelector('.is_data_fixed')
          const tableDom = document.querySelector('.el-table__header-wrapper')
          const dataTotDom = document.querySelector('.dataTot')
          setTimeout(() => {
            if (dom) {
              if (tabFixed.offsetWidth - dom.offsetWidth != 20 || val) {
                dom.style.width = `${tabFixed.offsetWidth - 40}px`
                tableDom.style.width = `${tabFixed.offsetWidth - 40}px`
              }
            }
          }, 10);
          if (marginDom && dataTotDom) {
            marginDom.style.marginTop = `${tableDom.offsetHeight + dataTotDom.offsetHeight}px`
          }
          // })
        } else {
          marginDom.style.marginTop = 0
        }
      } else {
        this.titleFixed = false
      }
    },
    // 分页查询
    pagination(page) {
      this.queryForm.pageNum = page.page
      this.queryForm.pageSize = page.limit
      this.submitSearch()
      document.querySelector('.app-main').scrollTop = 430
    },
    // 列表导出
    exportExcel(id) {
      if (!this.queryForm.updateStart || !this.queryForm.updateEnd) {
        this.$message.error('请输入完整的时间范围')
        return
      }

      let params = JSON.parse(JSON.stringify(this.queryForm))
      params.type = params.type.join(',')


      const req = { ...params }
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.pageSize = parseInt(this.exportNum)
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.esBeanMarkId)
        req.ids = ids
      } else {
        this.$message.warning('请选择导出条数或导出项')
        return
      }
      this.downloadLoading = true
      this.$confirm('是否确认导出所有类型数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return esBeanMarkExportApi(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message.success('导出成功')
        this.download(response.msg);
      }).catch(() => {
        this.downloadLoading = false
      })
    },
    //导出条数变动后的table选中项改变
    exportNumChange(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },


    // 获取素材树
    async getTreeData() {
      let resauto = await getFolderList()
      this.treeDataauto = resauto.data
    },
    // 批量导入至素材
    importToMaterials(item) {
      if (!this.queryForm.updateStart || !this.queryForm.updateEnd) {
        this.$message.error('请输入完整的时间范围')
        return
      }
      let params = JSON.parse(JSON.stringify(this.queryForm))
      params.type = params.type.join(',')

      const req = { ...params }
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.pageSize = parseInt(this.exportNum)
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.esBeanMarkId)
        req.ids = ids
      } else {
        this.$message.warning('请选择导入条数或导入项')
        return
      }

      //设置素材库收藏夹信息
      req.folderId = item.id

      this.downloadLoading = true
      this.$confirm('是否确认导入至素材?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return esBeanMarkAddMaterialApi(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message.success('导入成功')
      }).catch(() => {
        this.downloadLoading = false
      })
    },
    //处置||移除
    async markDisposition(row) {
      let val = row.deal ? 0 : 1;
      let res = await updateDeal({ md5: row.md5, deal: val, indexId: row?.id, createTime: row?.publishTime })
      if (res.code == 200) {
        this.$set(row, 'deal', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'deal', row.deal);
        this.$message.error(res.msg)
      }
    },
    //重点关注||移除
    async markKeyFocus(row) {
      let val = row.follow ? 0 : 1;
      let res = await updateFollow({ md5: row.md5, follow: val, indexId: row?.id, createTime: row?.publishTime })
      if (res.code == 200) {
        this.$set(row, 'follow', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'follow', row.follow);
        this.$message.error(res.msg)
      }
    },
    // 复制文章
    copyAritical(row, tips) {
      copyAritical(row)
    },
    // 复制
    copyText(content, tips) {
      copyText(content, tips)
    },
    // 跳转详情页
    goOrigin(url) {
      window.open(url, '_blank')
    },
    //开启发送短信弹窗
    openSendMsg(row) {
      this.sendMsgDialog = true
      this.sendMsgRow = row
      this.sendMsgRow.keyWord1 = ''
      this.sendMsgRow.planId = ''
    },
    //同步sendMsgDialog值
    visibleChange(value) {
      this.sendMsgDialog = value
    },
    // 跳转详情页
    async goDetail(row) {
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: { id: row.id, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5 }
      })
      window.open(fullPath.href, '_blank')
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.multipleSelection.selectedRows = val
    },
    // 查询列表
    async submitSearch(type) {
      this.searchNum++
      this.slideFlag = false
      if (type == 'search') {
        this.queryForm.pageNum = 1
      }

      if (!this.queryForm.updateStart || !this.queryForm.updateEnd) {
        this.$message.error('请输入完整的时间范围')
        return
      }

      this.exportNum = ''//翻页时将导出页数选择器重置
      try {
        this.tableLoading = true

        let params = JSON.parse(JSON.stringify(this.queryForm))
        params.type = params.type.join(',')

        console.log('params :>> ', params);
        const res = await esBeanMarkListApi(params)
        console.log('res :>> ', res);
        this.tableData = res.data.records.map(item => {
          item.originFlag = item.emotionFlag;
          item.originalRiskGrade = item.riskGrade
          // 初始化 `urlAccessStatus` 属性
          this.$set(item, 'urlAccessStatus', null);
          return item;
        });
        this.total = Number(res.data.total)
        this.totalPage = Math.ceil(this.total / this.queryForm.pageSize)
        this.tableLoading = false
        await this.checkUrlAlive(this.tableData, this.searchNum)
      } catch (error) {
        console.log(error)
        this.tableLoading = false
      }
    },
    //校验原文是否删除
    async checkUrlAlive(data, number) {
      const urls = data.map(item => {
        return item.url
      })
      const newArray = data
      try {
        let res = await getUrlAccessStatusApi(urls)
        res.data.map((item, index) => {
          newArray[index].urlAccessStatus = item
        })
      } catch (err) {
        this.$message.error(err)
      }
      if (this.searchNum == number) {
        this.tableData = newArray;
      }
    },
    goLeft() {
      if (this.totalPage >= this.queryForm.pageNum && this.queryForm.pageNum > 1) {
        this.queryForm.pageNum--
        this.submitSearch()
      }
    },
    goRight() {
      if (this.totalPage > this.queryForm.pageNum && this.totalPage > 0) {
        this.queryForm.pageNum++
        this.submitSearch()
      }
    },

    updateStartChange(val) {
      let endDateVal = this.queryForm.updateEnd;
      if (endDateVal && new Date(this.queryForm.updateStart).getTime() > new Date(endDateVal).getTime()) {
        this.queryForm.updateEnd = val;
      }
    },
    updateEndChange(val) {
      let beginDateVal = this.queryForm.updateStart;
      if (beginDateVal && new Date(this.queryForm.updateEnd).getTime() < new Date(beginDateVal).getTime()) {
        this.queryForm.updateStart = val;
      }
    },

    startChange(val) {
      let endDateVal = this.queryForm.publishEnd;
      if (endDateVal && new Date(this.queryForm.publishStart).getTime() > new Date(endDateVal).getTime()) {
        this.queryForm.publishEnd = val;
      }
    },
    endChange(val) {
      let beginDateVal = this.queryForm.publishStart;
      if (beginDateVal && new Date(this.queryForm.publishEnd).getTime() < new Date(beginDateVal).getTime()) {
        this.queryForm.publishStart = val;
      }
    },
    deleteLog(row) {
      this.$confirm('是否确认删除?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        esBeanMarkDeleteApi({ esBeanMarkId: row.esBeanMarkId }).then(res => {
          if (res.code == 200) {
            this.$message.success('删除成功')
            this.submitSearch()
          }
        })
      })
    },
  }
}
</script>

<style scoped lang="scss">
.publicOpinionWarning {
  background: #F4F7FB;
  padding: 20px 40px;
  min-height: 100vh;

  .search-wrap {
    // height: 210px;
    background: #FFFFFF;
    // margin-top: 30px;
    margin-bottom: 20px;
    padding-bottom: 20px;

    .title {
      height: 60px;
      line-height: 60px;
      border-bottom: 1px solid #DCDEE0;

      span {
        border-left: 6px solid #247CFF;
        padding-left: 28px;
        font-weight: bold;
        font-size: 18px;
        color: #333333;
      }
    }

    .demo-form-inline {
      padding-top: 30px;
      padding-left: 36px;

      ::v-deep .el-form-item__label {
        font-size: 14px;
        color: #666666;
        font-weight: normal;
      }
    }

    .search-btn {
      text-align: center;
    }
  }

  .wrap-content {
    background: #F4F7FB;
  }

  .data-table {
    background: #fff;
    padding: 20px;
    border-radius: 8px;

    .dataTot {
      width: 100%;
      text-align: center;
      margin: 0;
      font-size: 14px;
      color: #333;

      .jump-page {
        margin-right: 50px;
        font-size: 14px;
        line-height: 22px;

        span {
          color: #000;

          &.jump-line {
            margin: 0 16px;
          }
        }

        i {
          color: #000;

          &.el-icon-arrow-right {
            margin-left: 18px;
          }

          &.el-icon-arrow-left {
            margin-right: 18px;
          }
        }
      }

      .dataSel {
        display: flex;
        justify-content: space-between;
        background-color: #fff;
        padding: 13px 20px 0 20px;

        .input-with-select {
          width: 400px;
        }
      }

      .el-icon-refresh {
        color: #247CFF;
        cursor: pointer;
      }
    }
  }


  .dataTable {
    width: 100%;
    //overflow: hidden;
    padding: 20px;
    box-sizing: border-box;
    background: #fff;

    .tableItemTitle {
      .tableTitle {
        margin-top: 25px;
        display: flex;
        align-items: center;

        ::v-deep em {
          color: #f46263;
        }

        .tableItemImg {
          width: 29px;
        }

        .tableItemImg,
        .tableTitleSpan {
          font-size: 16px;
          color: #333333;
          line-height: 22px;
          vertical-align: middle;
          margin-right: 14px;
        }

        .tableTitleSpan {
          display: inline-block;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
          max-width: calc(100% - 190px);
          font-size: 16px;
          line-height: 22px;
          color: #333;
          cursor: pointer;
          font-weight: 600;
        }

        .article-type {
          margin: 0 0 0 10px;
          display: inline-block;
          padding: 2px 6px;
          background: #247CFF;
          font-size: 12px;
          color: #FFFFFF;
          line-height: 17px;
          border-radius: 2px;
          width: -moz-fit-content;
          width: fit-content;
          white-space: nowrap;
        }
      }

      .tableMain {
        margin: 9px 0 7px 0;
        position: relative;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        -webkit-box-orient: vertical;
        max-height: calc(2em * 2);
        /* 控制显示的高度 */
        overflow: hidden;
        font-size: 14px;
        line-height: 20px;
        color: #666;
        cursor: pointer;

        ::v-deep em {
          color: #f46263;
        }
      }

      .tableFoot {
        font-size: 12px;
        color: #666;

        .footInfo {
          // display: inline-block;
          display: none;
          margin-right: 40px;
          font-size: 14px;
          color: #999;

          img {
            width: 16px;
            margin-right: 6px;
          }
        }

        .footButtonGroup {
          margin: 13px 0 27px 0;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .keyword {
            font-size: 12px;
            color: #F46263;
            line-height: 17px;
            max-width: 12em;
            text-overflow: ellipsis;
            white-space: nowrap;
            overflow: hidden;
          }

          .footButonItem {
            font-size: 12px;
            display: inline-block;
            margin-right: 20px;
            cursor: pointer;
            white-space: nowrap;

            &:last-child {
              // margin-right: 0;
            }
          }
        }

        img {
          width: 14px;
          margin-right: 3px;
        }

        img,
        span {
          display: inline-block;
          vertical-align: middle;
        }
      }
    }

    .cover-column {
      opacity: 0.5;
    }

    .follow-img {
      position: absolute;
      top: 0;
      right: 0;
      width: 50px;
    }

    .read-img {
      position: absolute;
      top: 30%;
      right: 20%;
      width: 80px;
    }
  }

  .exportImg {
    width: 16px;
    vertical-align: middle;
    margin-left: 20px;
  }


  .is_data_fixed {
    padding: 0 0px 20px 0px;
    width: inherit;
    position: fixed;
    top: 80px;
    background: #fff;
    z-index: 299;
  }

  ::v-deep {
    .el-backtop {
      background-color: rgba(255, 255, 255, 0.6);
    }
  }

  .emotionSelect {
    ::v-deep .el-input.el-input--mini.el-input--suffix {
      width: 72px;
    }
  }

  .result-fiexd {
    ::v-deep.el-table__header-wrapper {
      top: 145px;
    }
  }

  .subClass {
    .el-popper[x-placement^=right] .popper__arrow {
      border: none;
    }

    .el-popper[x-placement^=right] .popper__arrow::after {
      border: none;
    }
  }
}
</style>
