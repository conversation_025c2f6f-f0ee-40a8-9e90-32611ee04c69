<template>
  <div ref="cloudChart" style="width: 100%; height: 100%"></div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-wordcloud';

export default {
  props: {
    data: {
      type: Array,
      default: () => []
    },
    showLoading: {
      type: Boolean,
      default: true,
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    },
    chartStyle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {}
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    window.removeEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  mounted() {
    this.initChart();
    if (this.chart) {
      this.chart.on('contextmenu', (params) => {
        params.event.stop()
        this.$emit('filterCloud', params)
      })
      this.chart.on('click', (params) => {
        this.$emit('goToExpendDetail', '关键词云', params.name, params.value)
      })
    }
    window.addEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.cloudChart)
      this.chart.showLoading();

      const colorList = ['#44C4E4', '#147EA0', '#C3D884']
      // 人像的base64编码
      let image1 = 'data:image/png;base64,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'
      var maskResource = new Image();
      maskResource.src = image1;

      const option = {
        //   backgroundColor: '#012248',
        tooltip: {
          show: true
        },
        toolbox: {
          right: "30",
          show: true,
          feature: {
            dataView: {  //设置数据视图
              show: this.isDataView,
              title: '数据视图',
              readOnly: false,
              lang: ['数据视图', '关闭', '刷新'],
            },
            restore: {  //设置数据重置
              show: this.isDataView,
              title: '还原',
            },
            saveAsImage: {
              name: "关键词云",
              iconStyle: {
                normal: {
                  color: '#FFFFFF'
                }
              }
            }
          }
        },
        series: [{
          name: '',
          type: 'wordCloud',
          maskImage: maskResource,
          gridSize: 7,
          // grid: {
          //   left: "3%",
          //   right: "4%",
          //   bottom: "3%",
          //   containLabel: true,
          // },
          sizeRange: [15, 40],
          rotationRange: [0, 0],//旋转角度
          // rotationStep: 45,
          shape: 'sphere',
          width: '100%',
          height: '100%',
          drawOutOfBound: false,
          textStyle: {
            color: function () {
              // 随机颜色
              // return 'rgb(' + [
              //     Math.round(Math.random() * 200 + 55),
              //     Math.round(Math.random() * 200 + 55),
              //     Math.round(Math.random() * 200 + 55)
              // ].join(',') + ')';

              let index = Math.floor(Math.random() * colorList.length)
              return colorList[index]
            },
          },
          data: this.data
        }]
      }
      if (!this.showLoading) {
        console.log('this.showLoading', this.showLoading)
        this.chart.hideLoading();
      }
      this.chart.setOption(option, true)
      if (this.chart) {
        this.chart.on('rendered', () => {
          if (this.isToImg) {
            const chartRef = this.isDown ?
              this.chart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
              :
              this.chart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
            this.$emit('chartRef', this.isToImg, chartRef)
          }
        });
        // 监听工具栏中数据视图的修改事件。
        this.chart.on('dataViewChanged', (params) => {
          // params中包含了与数据视图修改相关的信息
          console.log('修改后的数据视图相关信息:', params);
          const newData = params.newOption.series[0].data
          this.$emit('modifiedData', this.chartStyle, newData)
        });
        // 监听工具栏中restore 重置 option 事件
        this.chart.on('restore', (params) => {
          this.$emit('modifiedData', this.chartStyle, '')
        });

      }
      this.$nextTick(() => {
        this.chart.resize();
      });
    },

  }
}


</script>
<style lang="scss" scoped>
.ciyun {
  text-align: center;
  width: 100%;
  height: 100%;
}

#myCanvas {
  width: 100%;
  height: 100%;
}

#tags {
  width: 100%;
  height: 100%;
}
</style>
<style scoped lang="scss">
::v-deep {
  textarea {
    line-height: 1.8em !important;
  }
}
</style>
