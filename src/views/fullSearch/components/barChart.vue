<template>
  <div ref="barChart" style="width: 100%; height: 100%;position: relative;"></div>
</template>
<script>
import * as echarts from "echarts";

export default {
  name: "barChart",
  props: {
    data: {
      type: Object,
      default: () => {
      },
    },
    showLoading: {
      type: Boolean,
      default: true,
    },
    allCount: {
      type: Number,
      default: 0,
    },
    toolName: {
      type: String,
      default: "饼图",
    },
    color: {
      type: Array,
      default: () => ["#F4A259", "#F07167", "#00B4D8"],
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    },
    chartStyle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    window.removeEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  mounted() {
    this.initChart();
    if (this.chart) {
      this.chart.on('click', (params) => {
        this.$emit('goToExpendDetail', '媒体活跃', params.name, params.value)
      })
    }
    window.addEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.barChart);
      this.chart.showLoading();
      let data = this.data;

      const option = {
        color: ["#40C3E3"],
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLabel: {interval: 0, rotate: 45},
          data: this.data.yyData,
        },
        yAxis: {
          type: "value",
        },
        toolbox: {
          right: "30",
          show: true,
          feature: {
            dataView: {  //设置数据视图
              show: this.isDataView,
              title: '数据视图',
              readOnly: false,
              lang: ['数据视图', '关闭', '刷新'],
            },
            restore: {  //设置数据重置
              show: this.isDataView,
              title: '还原',
            },
            saveAsImage: {show: true, name: "媒体活跃度"},
          },
        },
        series: {
          name: '',
          type: "bar",
          data: this.data.xxData,
          barWidth: "10%",
          datasetIndex: 1,
          label: {
            show: true,
            position: "top",
          },
        },
      };

      if (!this.showLoading) {
        this.chart.hideLoading();
      }
      this.chart.setOption(option, true);
      if (this.chart) {
        this.chart.on('rendered', () => {
          if (this.isToImg) {
            const chartRef = this.isDown ?
              this.chart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
              :
              this.chart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
            this.$emit('chartRef', this.isToImg, chartRef)
          }
        });

        // 监听工具栏中数据视图的修改事件。
        this.chart.on('dataViewChanged', (params) => {
          // params中包含了与数据视图修改相关的信息
          console.log('修改后的数据视图相关信息:', params);
          const newData = {xList: params.newOption.xAxis[0].data, yList: params.newOption.series[0].data}
          this.$emit('modifiedData', this.chartStyle, newData)
        });
        // 监听工具栏中restore 重置 option 事件
        this.chart.on('restore', (params) => {
          this.$emit('modifiedData', this.chartStyle, '')
        });

      }
      this.$nextTick(() => {
        this.chart.resize();
      });
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep {
  textarea {
    line-height: 1.8em !important;
  }
}
</style>
