<template>
  <div ref="spread" class="chart" />
</template>

<script>
import * as echarts from "echarts";

export default {
  props: {
    chartData: {
      type: Object,
      default: () => { },
    },
    categories: {
      type: Array,
      default: () => [
        { name: "降温" },
        { name: "升温" },
        { name: "爆发" },
        { name: "扩散" },
        { name: "最早" },
      ]
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    },
    chartStyle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    chartData() {
      this.initChart();
    },
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', () => {
      if (this.chart) this.chart.resize();
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.spread);
      let nodes = this.chartData.nodes;
      console.log('this.chartData',this.chartData)
      console.log('nodes',nodes)
      // nodes.map((item) => {
      //   item.symbolSize = item.symbolSize < 10 ? 5 : item.symbolSize / 1.5
      // })
      let links = this.chartData.links;
      let categories = this.categories;
      let option = {
        // color: [
        //   "#ffffbf",
        //   "#fee090",
        //   "#fdae61",
        //   "#f46d43",
        //   "#d73027",
        //   "#a50026"
        // ],
        color: ["#67fdd8", "#3dc5ff", "#4388ff", "#7e4dff", "#e041fd"],
        // 图表标题
        // backgroundColor: "#fff",
        //数据提示框配置
        tooltip: {
          extraCssText: 'max-width:500px;white-space:pre-wrap'
        },
        toolbox: {
          right: '30',
          show: true,
          feature: {
            dataView: {  //设置数据视图
              show: this.isDataView,
              title: '数据视图',
              readOnly: false,
              lang: ['数据视图', '关闭', '刷新'],
            },
            restore: {  //设置数据重置
              show: this.isDataView,
              title: '还原',
            },
            saveAsImage: {show: true, name: this.toolName}
          }
        },
        // visualMap: {
        //   type: "piecewise",
        //   categories: categories.map(function(a) {
        //       return a.name;
        //     }),
        //   inRange: {
        //     color: [
        //       "#313695",
        //       "#4575b4",
        //       "#74add1",
        //       "#abd9e9",
        //       "#e0f3f8"
        //     ]
        //   }
        // },
        //图例配置
        legend: [
          {
            top: 30,
            x: "right", //图例位置
            orient: "vertical",
            data: categories.map(function (a) {
              return a.name;
            }), //关系图中需要与series中的categories的name保持一致
          },
        ],
        //sereis的数据: 用于设置图表数据之用
        series: [
          {
            type: "graph",
            //layout:"none",
            layout: "force", //layout为force，layout可以选择none、circular和force
            //'none' 不采用任何布局，使用节点中提供的 x， y 作为节点的位置。
            //'circular' 采用环形布局。
            //'force' 采用力引导布局。
            // symbolSize: 15,xw
            // edgeSymbol: ["circle", "arrow"], ///边两端的标记类型，可以是一个数组分别指定两端，也可以是单个统一指定。默认不显示标记[ 'none', 'none' ]，常见的可以设置为箭头
            //edgeSymbolSize: [4, 8], //边两端的标记大小，可以是一个数组分别指定两端，也可以是单个统一指定。
            // edgeLabel: {//线条的边缘标签
            //             normal: {
            //                 show: true,
            //                 formatter: function (x) {
            //                     return x.data.name;
            //                 }
            //             }
            //         },
            // edgeLabel: {
            //     normal: {
            //         textStyle: {
            //             fontSize: 20
            //         }
            //     }
            // },
            animationDuration: 1500,
            animationEasingUpdate: "quinticInOut",
            //animation: false, //是否开启动画
            roam: true, //是否开启滚轮缩放和拖拽漫游，默认为false（关闭）

            label: {
              //图形上的文本标签
              normal: {
                show: true, //是否显示标签。
                position: "right", ///标签相对于节点标签的位置
                textStyle: {
                  //标签的字体样式
                  color: "#666", //字体颜色
                  fontStyle: "normal", //文字字体的风格 'normal'标准 'italic'斜体 'oblique' 倾斜
                  fontWeight: "bolder", //'normal'标准'bold'粗的'bolder'更粗的'lighter'更细的或100 | 200 | 300 | 400...
                  fontFamily: "sans-serif", //文字的字体系列
                  fontSize: 14, //字体大小
                },
                // 回调函数，你期望节点标签上显示什么
                formatter: function (params) {
                  let name = params.data.siteHost;
                  let nameString = "";
                  if (name.length > 20) {
                    nameString = name.substring(0, 20) + '...'
                  } else {
                    nameString = name
                  }
                  return nameString;
                }
                // }
              },
            },
            focusNodeAdjacency: true,
            draggable: true, //指示节点是否可以拖动
            // focusNodeAdjacency: true, //当鼠标移动到节点上，突出显示节点以及节点的边和邻接节点
            data: nodes, //节点数据
            edges: links, //边数据
            categories: categories,
            force: {
              //力引导图基本配置
              //initLayout: ,//力引导的初始化布局，默认使用xy轴的标点
              repulsion: 140, //节点之间的斥力因子。支持数组表达斥力范围，值越大斥力越大。
              // gravity : 0.03,//节点受到的向中心的引力因子。该值越大节点越往中心点靠拢。
              edgeLength: 150, //边的两个节点之间的距离，这个距离也会受 repulsion。[10, 50] 。值越小则长度越长
              layoutAnimation: false
              //因为力引导布局会在多次迭代后才会稳定，这个参数决定是否显示布局的迭代动画，在浏览器端节点数据较多（>100）的时候不建议关闭，布局过程会造成浏览器假死。
            },

            itemStyle: {
              show: true,
              borderColor: "#fff",
              borderWidth: 1,
              shadowBlur: 10,
              shadowColor: "rgba(0, 0, 0, 0.3)",
            },
            lineStyle: {
              // color: "source",
              curveness: 0.3,
              color: "#000",
            },
            emphasis: {
              lineStyle: {
                width: 5,
              },
              //高亮状态
            },
          },
        ],
      };
      // let option = {
      //   tooltip: {},
      //   legend: [
      //     {
      //       data: categories,
      //       left: 20,
      //       top: 20,
      //       show:false
      //     },
      //   ],
      //   grid: {
      //     top: "25%",
      //     left: "5%",
      //     right: "5%",
      //     bottom: "8%",
      //     containLabel: true,
      //   },
      //   series: [
      //     {
      //       name: "传播路径图",
      //       type: "graph",
      //       layout: "none",
      //       data: nodes,
      //       links: links,
      //       categories:categories,
      //       roam: true,
      //       label: {
      //         show: true,
      //         position: "right",
      //         formatter: "{b}",
      //       },
      //       labelLayout: {
      //         hideOverlap: true,
      //       },
      //       scaleLimit: {
      //         min: 0.4,
      //         max: 2,
      //       },
      //       lineStyle: {
      //         color: "source",
      //         curveness: 0.3,
      //       },
      //     },
      //   ],
      // };
      this.chart.setOption(option);

      if (this.chart) {
        this.chart.on('rendered', () => {
          if (this.isToImg) {
            const chartRef = this.isDown ?
              this.chart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
              :
              this.chart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
            this.$emit('chartRef', this.isToImg, chartRef)
          }
        });
        // 监听工具栏中数据视图的修改事件。
        this.chart.on('dataViewChanged', (params) => {
          // params中包含了与数据视图修改相关的信息
          console.log('修改后的数据视图相关信息:', params);
          const newData = {seriesList: params.newOption.series, xs: params.newOption.xAxis[0].data}
          this.$emit('modifiedData', this.chartStyle, newData)
        });
        // 监听工具栏中restore 重置 option 事件
        this.chart.on('restore', (params) => {
          this.$emit('modifiedData', this.chartStyle, '')
        });

      }
    },
  },
};
</script>
