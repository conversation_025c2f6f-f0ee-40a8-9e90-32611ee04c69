<template>
  <div ref="barChart" style="width: 100%; height: 100%;position: relative;"></div>
</template>
<script>
import * as echarts from "echarts";

export default {
  name: "barChart",
  props: {
    data: {
      type: Object,
      default: () => {
      },
    },
    showLoading: {
      type: Boolean,
      default: true,
    },
    allCount: {
      type: Number,
      default: 0,
    },
    toolName: {
      type: String,
      default: "观点图",
    },
    color: {
      type: Array,
      default: () => ["#F4A259", "#F07167", "#00B4D8"],
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    window.removeEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  mounted() {
    this.initChart();
    // if(this.chart){
    //   this.chart.on('click',(params) => {
    //      this.$emit('goToExpendDetail','媒体级别', params.name, params.value)
    //     })
    // }
    window.addEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.barChart);
      this.chart.showLoading();
      let data = this.data;

      let colorList = ["#e2748e", '#ffb97d', '#bad35e', '#7fabcb', '#4fa6e2']
      const option = {
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          type: 'category',
          inverse: true,
          axisLine: { // x轴轴线配置
            show: false // 隐藏x轴轴线
          },
          // 其他x轴配置（如刻度线、坐标轴标签等，根据需要配置）
          axisTick: {
            show: false // 示例：显示刻度线（根据需要设置）
          },
          axisLabel: {
            show: false, // 示例：显示坐标轴标签（根据需要设置）
          },
          data: data.yyData
        },
        toolbox: {
          right: "30",
          show: true,
          feature: {
            dataView: {  //设置数据视图
              show: this.isDataView,
              title: '数据视图',
              readOnly: false,
              lang: ['数据视图', '关闭', '刷新'],
            },
            restore: {  //设置数据重置
              show: this.isDataView,
              title: '还原',
            },
            saveAsImage: {
              name: "媒体观点",
              iconStyle: {
                normal: {
                  color: '#FFFFFF'
                }
              }
            }
          }
        },
        series: [
          {
            name: '',
            type: 'bar',
            label: {
              show: true,
              position: 'center',
              padding: [20, 0, 20, 5],
              fontSize: 20,
              formatter: '{b} {c}%',
              color: '#333', // 字体颜色
              fontFamily: 'sourcehan', // 字体族
              fontWeight: 'bold', // 字体粗细
            },

            barWidth: 55,
            barGap: '2%',
            lineHeight: 26,
            itemStyle: {
              barBorderRadius: 0,
              color: (params) => {
                return colorList[params.dataIndex]
              },
            },
            data: data.xxData
          }
        ]
      };

      if (!this.showLoading) {
        this.chart.hideLoading();
      }
      this.chart.setOption(option, true);
      if (this.chart) {
        this.chart.on('rendered', () => {
          if (this.isToImg) {
            const chartRef = this.isDown ?
              this.chart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
              :
              this.chart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
            this.$emit('chartRef', this.isToImg, chartRef)
          }
        });
      }
      this.$nextTick(() => {
        this.chart.resize();
      });
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep {
  textarea {
    line-height: 1.8em !important;
  }
}
</style>
