<template>
  <div ref="piechart" style="width: 100%; height: 100%"></div>
</template>
<script>
import * as echarts from "echarts";

export default {
  name: "piechart",
  props: {
    data: {
      type: Object,
      default: () => {
      },
    },
    showLoading: {
      type: Boolean,
      default: true,
    },
    allCount: {
      type: Number,
      default: 0,
    },
    toolName: {
      type: String,
      default: "饼图",
    },
    radius: {
      type: Array,
      default: () => ["25%", "50%"],
    },
    color: {
      type: Array,
      default: () => ["#F4A259", "#F07167", "#00B4D8"],
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    },
    chartStyle: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      chart: null,
    };
  },
  watch: {
    data() {
      this.$nextTick(() => {
        this.initChart();
      });
    },
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    window.removeEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  mounted() {
    this.initChart();
    if (this.chart) {
      this.chart.on('click', (params) => {
        const newName = params.name ? params.name : '全部'
        this.$emit('goToExpendDetail', this.toolName, newName, params.value)
      })
    }
    window.addEventListener("resize", () => {
      if (this.chart) this.chart.resize();
    });
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.piechart);
      this.chart.showLoading();
      const color = this.color;
      const radius = this.radius;

      const tooltip = {
        trigger: "item",
        formatter: "{b} {c}",
      };
      const option = {
        color,
        tooltip,
        title: [],
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          right: "30",
          show: true,
          feature: {
            dataView: {  //设置数据视图
              show: this.isDataView,
              title: '数据视图',
              readOnly: false,
              lang: ['数据视图', '关闭', '刷新'],
            },
            restore: {  //设置数据重置
              show: this.isDataView,
              title: '还原',
            },
            saveAsImage: {show: true, name: this.toolName},
          },
        },
        legend: {
          orient: "vertical",
          icon: "circle",
          type: "scroll",
          align: "left",
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 20,
          borderRadius: 4,
          textStyle: {
            fontSize: 12,
          },
          left: "6%",
          top: 20,
          //   x: 'right',
          y: "center",
          // data: ['访问人数', '提交订单', '支付成功', '到店消费'],
          formatter: (params) => {
            if (this.data.data) {
              // const item = this.data.data.filter((item) => item.name === params)[0]
              // return `${params} | ${item.value}`
              return `${params}`;
            }
          },
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: radius,
            center: ["40%", "50%"],
            avoidLabelOverlap: false, //做同心圆用到
            clockwise: true, //顺时针排列
            // startAngle: 100, //起始角度 影响不大
            //   roseType: 'radius', //area|radius
            label: {
              show: true,
              color: "inherit",
              formatter: "{b}\n{d}%",
            },
            labelLine: {
              show: true, //显示引导线
              // length: 1, //连接饼图第1段线条的长度 length length2 不写自适应
              // length2: 5, //链接饼图第2段线条长度
              smooth: false, //是否光滑连接线
            },
            itemStyle: {
              //每个扇形的设置
              borderColor: "rgba(0,0,0,.1)", //扇形边框颜色
              borderWidth: 0, //扇形边框大小 要先给borderColor颜色 设置borderWidth才会有效果
            },
            data: this.data.data,
            emphasis: {
              scale: true,
              scaleSize: 10,
              //启用鼠标放上去放大效果，这个挺好的
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)",
              },
            },
          },
        ],
      };
      if (!this.showLoading) {
        this.chart.hideLoading();
      }
      this.chart.setOption(option, true);

      // setTimeout(() => {
      if (this.chart) {
        this.chart.on('rendered', () => {
          if (this.isToImg) {
            const chartRef = this.isDown ?
              this.chart.getDataURL({type: "png", pixelRatio: 2}) //拿到base64 地址，就好下载了。
              :
              this.chart.getDataURL({type: "png", pixelRatio: 2, excludeComponents: ['toolbox']}) //拿到base64 地址，就好下载了。
            this.$emit('chartRef', this.isToImg, chartRef)
          }
        });

        // 监听工具栏中数据视图的修改事件。
        this.chart.on('dataViewChanged', (params) => {
          // params中包含了与数据视图修改相关的信息
          console.log('修改后的数据视图相关信息:', params);
          const newData = params.newOption.series[0].data
          this.$emit('modifiedData', this.chartStyle, newData)
        });
        // 监听工具栏中restore 重置 option 事件
        this.chart.on('restore', (params) => {
          this.$emit('modifiedData', this.chartStyle, '')
        });
      }
      // }, 2000);


      this.$nextTick(() => {
        this.chart.resize();
      });
    },
  },
};
</script>
<style scoped lang="scss">
::v-deep {
  textarea {
    line-height: 1.8em !important;
  }
}
</style>
