<template>
  <div class="entity-identification">
    <!-- 主要内容区域 -->
    <el-card class="container" shadow="never">
      <div slot="header" class="card-header">实体识别</div>
      <div class="panel-body">
        <div class="text-content" v-html="originalText"></div>
        <br />
        <div class="keywords-container">
          <span class="text-main">关键词提取：</span>
          <span>
            <span v-for="item in keywordsList"> {{ item }}</span>
          </span>
        </div>
      </div>
    </el-card>

    <el-card class="container" shadow="never">
      <div slot="header" class="card-header">分类统计</div>
      <div class="panel-body">
        <el-row :gutter="20">
          <el-col :span="8">
            <div ref="map1" class="chart-container"></div>
          </el-col>
          <el-col :span="8">
            <div ref="map2" class="chart-container"></div>
          </el-col>
          <el-col :span="8">
            <div ref="map3" class="chart-container"></div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <el-card class="container" shadow="never">
      <div slot="header" class="card-header">关联关系挖掘</div>
      <div class="panel-body">
        <div ref="map4" class="relation-chart"></div>
        <div class="sentence-container">
          <span class="text-main">摘要提取：</span>
          <span>{{ sentenceText }}</span>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { entityIdentificationApi } from '@/api/search'

export default {
  name: 'EntityIdentification',

  data() {
    return {
      originalText: '',
      keywordsList: '',
      sentenceText: '',
      id: this.$route.query.id || '',
      time: this.$route.query.time || '',
      loading: false,
      charts: {
        map1: null,
        map2: null,
        map3: null,
        map4: null
      }
    }
  },

  mounted() {
    this.$nextTick(() => {
      this.initCharts()
      this.getEntityOption()
    })
  },

  beforeDestroy() {
    this.disposeCharts()
    window.removeEventListener('resize', this.handleResize)
  },

  methods: {
    // 初始化所有图表
    initCharts() {
      const chartRefs = ['map1', 'map2', 'map3', 'map4']
      const chartOptions = {
        map4: { renderer: 'canvas' } // 关联关系图使用 canvas 渲染器以获得更好的性能
      }

      // 初始化每个图表
      chartRefs.forEach(refName => {
        if (this.$refs[refName]) {
          const options = chartOptions[refName] || {}
          this.charts[refName] = echarts.init(this.$refs[refName], null, options)
        } else {
          console.warn(`图表容器 ${refName} 不存在`)
        }
      })

      // 添加窗口大小变化监听，以便调整图表大小
      window.addEventListener('resize', this.handleResize)
    },

    // 销毁所有图表实例
    disposeCharts() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.dispose()
        }
      })

      // 清空图表引用
      Object.keys(this.charts).forEach(key => {
        this.charts[key] = null
      })
    },

    // 处理窗口大小变化
    handleResize() {
      Object.values(this.charts).forEach(chart => {
        if (chart) {
          chart.resize()
        }
      })
    },

    // 获取实体识别数据
    getEntityOption() {
      this.loading = true

      const params = {
        id: this.id,
        time: this.time
      }

      entityIdentificationApi(params)
        .then(response => {
          const data = response.data

          // 直接设置文本内容，不使用HTML字符串
          this.originalText = data.text
          this.keywordsList = data.keywordList
          this.sentenceText = data.sentence

          // 设置各个图表
          this.renderCharts(data)

          this.loading = false
        })
        .catch(error => {
          console.error('获取数据失败:', error)
          this.loading = false
          this.$message.error('获取数据失败')
        })
    },

    // 渲染所有图表
    renderCharts(data) {
      // 渲染三个分类统计图表
      this.renderChart('map1', this.getMap(data.nameJson, '人名', '#ff7f50'))
      this.renderChart('map2', this.getMap(data.placeJson, '地名', '#87cefa'))
      this.renderChart('map3', this.getMap(data.organizationJson, '机构名', '#da70d6'))

      // 渲染关联关系图
      this.renderChart('map4', this.getLinkOption(data.relevance))
    },

    // 渲染单个图表
    renderChart(chartId, option) {
      const chart = this.charts[chartId]
      if (!chart) {
        console.warn(`图表 ${chartId} 未初始化`)
        return
      }

      chart.showLoading({ text: '数据正在加载...' })

      // 使用 nextTick 确保在 DOM 更新后设置图表选项
      this.$nextTick(() => {
        chart.setOption(option, true) // 第二个参数为 true 表示不合并之前的选项
        chart.hideLoading()
      })
    },

    getMap(data, title, bcolor) {
      let keys = []
      let values = []

      for (let key in data) {
        keys.push(key)
        values.push(data[key])
      }

      if (keys.length === 0) {
        keys = ['暂无数据']
        values = [0]
      }

      return {
        title: {
          textStyle: {
            color: '#98a2b0'
          },
          text: title
        },
        textStyle: {
          color: '#98a2b0'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          show: true,
          feature: {
            saveAsImage: { show: true },
            restore: { show: true },
            dataView: { show: true, readOnly: false },
            magicType: { show: true, type: ['line', 'bar'] }
          }
        },
        xAxis: {
          type: 'value',
          boundaryGap: [0, 0.01],
          splitLine: {
            lineStyle: {
              color: ['#eee']
            }
          }
        },
        yAxis: {
          type: 'category',
          data: keys
        },
        series: [{
          name: '数量',
          type: 'bar',
          barWidth: '15px',
          data: values,
          itemStyle: {
            normal: {
              color: bcolor
            }
          }
        }]
      }
    },

    getLinkOption(relevance) {
      const obj = relevance

      return {
        tooltip: {
          trigger: 'item',
          formatter: '{a} : {b}'
        },
        toolbox: {
          show: true,
          feature: {
            restore: { show: true },
            saveAsImage: { show: true }
          }
        },
        legend: [{
          textStyle: {
            color: '#98a2b0'
          },
          data: ['实体类', '实体']
        }],
        animation: false,
        color: ['#FF7F50', '#87CEFA'],
        series: [{
          name: "关联关系",
          type: 'graph',
          layout: 'force',
          data: obj.nodes,
          links: obj.links,
          categories: [
            { name: '文章' },
            { name: '实体类' },
            { name: '实体' }
          ],
          roam: true,
          symbolSize: 25,
          itemStyle: {
            normal: {
              label: {
                show: true,
                textStyle: {
                  color: '#333'
                }
              }
            }
          },
          force: {
            repulsion: 100
          }
        }]
      }
    }
  }
}
</script>

<style scoped>
.entity-identification {
  padding: 20px 80px;
}

.container {
  margin-bottom: 20px;
}

.card-header {
  font-size: 16px;
  font-weight: bold;
}

.panel-body {
  padding: 15px;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.relation-chart {
  height: 430px;
  width: 100%;
}

.text-main {
  color: #409EFF;
  font-weight: bold;
}

.sentence-container {
  margin-top: 15px;
}
</style>
