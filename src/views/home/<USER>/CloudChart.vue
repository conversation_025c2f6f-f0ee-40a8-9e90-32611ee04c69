<template>
  <div class="ciyun" v-if="cloudData.length > 0">
    <canvas id="myCanvas"></canvas>
    <div id="tags">
      <a
        id="word"
        href="javascript:void(0);"
        v-for="item in cloudData"
        :data-weight="item.value"
        :key="item.name"
        @click="goToExpendDetail(item)"
      >{{ item.name }}</a>
    </div>
    <div :class="dialogClass">
      <el-dialog
        :visible.sync="dialogVisible"
        width="25%"
        title="请选择操作"
        :show-close="true"
      >
        <div>
          <el-button class="word-btn" size="small" type="primary" @click="commonFn(2)" style="margin-right: 16px;">
            查看详情
          </el-button>
          <el-button class="word-btn" size="small" type="danger" @click="commonFn(1)">过滤热词</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import 'echarts-wordcloud';

export default {
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    switchTheme: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      cloudData: [],
      dialogVisible: false,
      wordData: {}
      // cloudData:
      //   [{
      //          name : "美食",
      //          value: 20
      //       },
      //       {
      //          name: "网红店",
      //           value: 20
      //       },
      //   ]
    }
  },
  computed: {
    dialogClass() {
      return this.switchTheme ? 'lightDialog' : 'showDialog'
    }
  },
  watch: {
    chartData(newal) {
      this.cloudData = this.chartData
      this.$nextTick(() => {
        this.ciyun()
      })
    }
  },
  mounted() {
    // this.ciyun();
  },
  methods: {
    ciyun() {
      try {
        var i;
        var et = document.getElementById('tags').childNodes;
        var colors = ['#FF0000', '#ff9f1c', '#8338ec', '#3A86FF', '#2EC4B6', '#EF476F', '#00CDFF', '#B8DE3C', '#06d6a0', '#00a6fb', '#ff4000', '#ff5400', '#FFCF00', '#CC25A2', '#07BEB8', '#21DF00', '#FF70EB']; // 预设的颜色数组
        for (i in et) {
          if (et[i].nodeName == 'A') {
            // 设置随机颜色   textColour 设为null
            var randomColorIndex = Math.floor(Math.random() * colors.length); // 获取随机索引
            et[i].style.color = colors[randomColorIndex]; // 应用颜色
            // et[i].style.color = 'rgb(' + parseInt(Math.random() * 220) + ',' + parseInt(Math.random() * 220) + ',' + parseInt(Math.random() * 245) + ')';
            // 设置字体大小（例如，随机字体大小在 12px 到 24px 之间）
            et[i].style.fontSize = (parseInt(Math.random() * 13) + 12) + 'px';
            et[i].style.fontWeight = 'bold';
          }
        }
        window.TagCanvas.Delete('myCanvas')
        window.TagCanvas.Start('myCanvas', 'tags', {
          // // 默认颜色
          textColour: null,
          // 默认移入边框
          // outlineColour: '#fff',
          zoom: 0.9, // 缩放比
          reverse: true,
          weight: true,
          minBrightness: 1,
          weightFrom: 'data-weight',
          weightMode: 'size',
          weightSize: 1,
          weightSizeMin: 12,
          weightSizeMax: 22,
          depth: 2.9,
          // false牵引（鼠标指向的方向就是转动的地方）
          // true 拖拽
          dragControl: true,
          // dragControl: true,
          // interval:30,
          // 速度
          maxSpeed: 0.01,
          minSpeed: 0.01,
          // 速度
          initial: [-0.2, 0]
        });
      } catch (e) {
      }
    },
    goToExpendDetail(item) {
      this.dialogVisible = true
      this.wordData = item
    },
    commonFn(val) {
      if (val == 1) {
        this.$emit('filterCloud', this.wordData)
      } else {
        this.$emit('goToExpendDetail', '热词云', this.wordData.name, this.wordData.value)
      }
      this.dialogVisible = false

    }
  }
}


</script>
<style lang="scss" scoped>
.ciyun {
  text-align: center;
  width: 100%;
  height: 100%;
}

#myCanvas {
  width: 100%;
  height: 100%;
}

#tags {
  width: 100%;
  height: 100%;
}

.word-btn {
  font-size: 0.16rem;
  color: #FFFFFF;
}

.showDialog {
  ::v-deep .el-dialog {
    margin-top: 40vh !important;
  }

  ::v-deep .el-dialog__header {
    background: #F4F7FB;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px;
    padding-left: 30px;

    .el-dialog__title {
      font-size: 0.16rem;
      color: #333333;
    }
  }

  ::v-deep .el-dialog__headerbtn {
    top: 10px;

    .el-dialog__close {
      color: #999999;
    }
  }
}

.lightDialog {
  ::v-deep .el-dialog {
    margin-top: 40vh !important;
    background: #023764;
    border: 2px solid #00B3FF;
  }

  ::v-deep .el-dialog__header {
    background: #008ED5;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0px;
    padding-left: 30px;

    .el-dialog__title {
      font-size: 0.16rem;
      color: #fff;
    }
  }

  ::v-deep .el-dialog__headerbtn {
    top: 10px;

    .el-dialog__close {
      color: #FFF;
    }
  }
}
</style>
