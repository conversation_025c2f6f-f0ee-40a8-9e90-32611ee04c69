<template>
  <div>
    <!-- 审核 -->
    <el-dialog title="审核" width="550px" :visible.sync="dialogAuditVisible" :before-close="cancelDialog"
               :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form :model="form" ref="dynamicValidateForm" label-width="130px" :rules="rules">
        <el-form-item label="审核意见：" prop="processResult">
          <el-select style="width:100%" v-model="form.processResult" placeholder="请选择" @change="changeResult">
            <el-option v-for="item in resultOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分发人员：" prop="dispenserId" v-if="form.processResult==5">
          <treeselect v-model="form.dispenserId" :multiple="true" value-consists-of="LEAF_PRIORITY"
                      noOptionsText="没有数据" noResultsText="暂无结果"
                      :options="processorList" placeholder="请选择分发人员"/>
        </el-form-item>
        <el-form-item label="上报人员：" prop="managerUser" v-if="form.processResult==4">
          <treeselect disableBranchNodes v-model="form.managerUser" noOptionsText="没有数据" noResultsText="暂无结果"
                      :options="loadList" placeholder="请选择上报人员"/>
        </el-form-item>
        <el-form-item label="处置截止时间：" prop="deadlineNum" v-if="form.processResult==5">
          <el-select style="width:100%" v-model="form.deadlineNum" placeholder="请选择处置截止时间">
            <el-option v-for="item in deadOptions" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="评论：" prop="comment">
          <el-input type="textarea" v-model="form.comment" placeholder="请输入200字符以内的评论"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm('dynamicValidateForm')">确 定</el-button>
        <el-button @click="cancelDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {getManager, getProcessor, loadApprover, approvalSubmit} from '@/api/infoSubmit/index'
import {decryptByAES, encryptByAES} from '@/utils/jsencrypt'
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: 'AuditDialog',
  components: {Treeselect},
  props: {
    dialogAuditVisible: {
      type: Boolean,
      default: false,
    },
    infoId: {
      type: String,
      default: '',
    }
  },
  data() {
    return {
      rules: {
        comment: [
          {min: 0, max: 200, message: '长度在200个字符以内', trigger: 'blur'}
        ],
        managerUser: [
          {required: true, message: '请选择上报人员', trigger: 'blur'}
        ],
        deadlineNum: [
          {required: true, message: '请选择处置截止时间', trigger: 'blur'}
        ],
        processResult: [
          {required: true, message: '请选择审核意见', trigger: 'blur'}
        ],
        dispenserId: [
          {required: true, message: '请选择分发人员', trigger: 'blur'}
        ]
      },
      formLabelWidth: '120',
      form: {
        managerUser: null,
        processResult: 4,
        dispenserId: [],
        deadlineNum: '',
        comment: ''
      },
      resultOptions: [
        {label: '上报', value: 4},
        {label: '分发', value: 5},
        {label: '修改材料', value: 2},
        {label: '无需处置', value: 1},
      ],
      deadOptions: [
        {label: '暂无', value: '暂无'},
        {label: '12H', value: '12H'},
        {label: '24H', value: '24H'},
        {label: '48H', value: '48H'},
      ],
      processorList: [],
      loading: false,
      loadList: []
    }
  },
  computed: {
    roles() {
      return this.$store.getters.roles
    },
    user() {
      return this.$store.getters.user
    }
  },
  created() {
    if (!this.roles.includes('fenxishi')) {
      this.queryProcess()
      this.queryLoad()
    }
  },
  methods: {
    // 意见切换
    changeResult(val) {
      this.$nextTick(() => {
        this.form = {
          processResult: val,
          deadlineNum: undefined,
          comment: '',
          dispenserId: [],
          managerUser: undefined
        }
        this.$refs['dynamicValidateForm'].resetFields()
      })
    },
    // 获取分发人员树
    async queryProcess() {
      let res = await getProcessor()
      if (this.$takeAES) {
        this.processorList = res.data ? JSON.parse(decryptByAES(res.data)) : []
      } else {
        this.processorList = res.data
      }
      this.processorList.map((item) => {
        this.approverDisabled(item, this.user.userId)
      })
    },
    // 获取上报人员树
    async queryLoad() {
      let res = await loadApprover()
      if (this.$takeAES) {
        this.loadList = res.data ? JSON.parse(decryptByAES(res.data)) : []
      } else {
        this.loadList = res.data
      }
      this.loadList.map((item) => {
        this.approverDisabled(item, this.user.userId)
      })
    },
    // 匹配id相同不可以上报
    approverDisabled(item, userId) {
      if (!item.children) {
        if (item.id == userId) {
          this.$set(item, 'isDisabled', true)
        }
      } else {
        for (let i in item.children) {
          this.approverDisabled(item.children[i], userId)
        }
      }
    },
    cancelDialog() {
      this.$emit('cancelAudit')
    },
    resetParams() {
      this.form = {
        managerUser: undefined,
        processResult: 4,
        dispenserId: [],
        deadlineNum: '',
        comment: ''
      }
      this.$nextTick(() => {
        this.$refs['dynamicValidateForm'].resetFields()
      })
    },
    // 提交
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            let params = JSON.parse(JSON.stringify(this.form))
            if (this.form.processResult == 5) {
              params.dispenserId = params.dispenserId.join(',')
            } else {
              params.dispenserId = undefined
            }
            this.loading = true
            let allParams = {}
            if (this.$takeAES) {
              allParams = {encryptJson: encryptByAES(JSON.stringify({...params, infoId: this.infoId}))}
            } else {
              allParams = {...params, infoId: this.infoId}
            }
            let res = await approvalSubmit(allParams)
            this.$message.success(res.msg)
            this.cancelDialog()
            this.$emit('success')
          } finally {
            this.loading = false
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}
</style>
