<template>
  <div>
    <!-- 转派 -->
    <el-dialog title="转派" width="550px" :visible.sync="dialogTransVisible" :before-close="cancelDialog"
               :close-on-press-escape="false" :close-on-click-modal="false">
      <el-form :model="form" ref="dynamicValidateForm" label-width="120px" :rules="rules">
        <el-form-item label="转派人员：" prop="dispenserId">
          <treeselect v-model="form.dispenserId" value-consists-of="LEAF_PRIORITY" :multiple="true"
                      noOptionsText="没有数据" noResultsText="暂无结果" :options="processorList"
                      placeholder="请选择接收人"/>
        </el-form-item>
        <el-form-item label="评论：" prop="comment">
          <el-input type="textarea" v-model.trim="form.comment" placeholder="请输入200字符以内的评论"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" :loading="loading" @click="submitForm('dynamicValidateForm')">确 定</el-button>
        <el-button @click="cancelDialog">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Treeselect from "@riophae/vue-treeselect";
import {decryptByAES, encryptByAES} from '@/utils/jsencrypt'
import {getManager, getProcessor, approvalSubmit} from '@/api/infoSubmit/index'
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: 'TransferDialog',
  components: {Treeselect},
  props: {
    dialogTransVisible: {
      type: Boolean,
      default: false,
    },
    infoId: {
      type: String,
      default: '',
    },
    processResult: {
      type: String,
      default: '6',
    }
  },
  data() {
    return {
      rules: {
        dispenserId: [
          {required: true, message: '请选择转派人员', trigger: 'blur'}
        ],
        comment: [
          {min: 0, max: 200, message: '长度在200个字符以内', trigger: 'blur'}
        ],
      },
      formLabelWidth: '120',
      form: {
        dispenserId: [],
        comment: ''
      },
      processorList: [],
      loading: false
    }
  },
  computed: {
    roles() {
      return this.$store.getters.roles
    },
    user() {
      return this.$store.getters.user
    }
  },
  created() {
    if (!this.roles.includes('fenxishi')) {
      this.queryProcess()
    }
  },
  methods: {

    resetParams() {
      this.form = {
        dispenserId: [],
        comment: ''
      }
      this.$nextTick(() => {
        this.$refs['dynamicValidateForm'].resetFields()
      })
    },
    // 获取接收人员树
    async queryProcess() {
      let res = await getProcessor()
      if (this.$takeAES) {
        this.processorList = res.data ? JSON.parse(decryptByAES(res.data)) : []
      } else {
        this.processorList = res.data
      }
      this.processorList.map((item) => {
        this.approverDisabled(item, this.user.userId)
      })
    },

    // 匹配id相同不可以上报
    approverDisabled(item, userId) {
      if (!item.children) {
        if (item.id == userId) {
          this.$set(item, 'isDisabled', true)
        }
      } else {
        for (let i in item.children) {
          this.approverDisabled(item.children[i], userId)
        }
      }
    },
    cancelDialog() {
      this.$emit('cancelTrans')
    },
    submitForm(formName) {
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          try {
            let params = JSON.parse(JSON.stringify(this.form))
            params.dispenserId = params.dispenserId.join(',')
            this.loading = true
            let allParams = {}
            if (this.$takeAES) {
              allParams = {
                encryptJson: encryptByAES(JSON.stringify({
                  ...params,
                  processResult: this.processResult,
                  infoId: this.infoId
                }))
              }
            } else {
              allParams = {...params, processResult: this.processResult, infoId: this.infoId}
            }
            let res = await approvalSubmit(allParams)
            this.$message.success(res.msg)
            this.cancelDialog()
            this.$emit('success')
          } finally {
            this.loading = false
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}
</style>
