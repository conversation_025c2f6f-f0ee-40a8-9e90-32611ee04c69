<template>
  <div class="eventContext">
    <div class="eventContextBlock">
      <div class="formH2">
        <div class="bluePoint"></div>
        <div>首发</div>
        <el-tooltip placement="top" effect="light">
          <div slot="content">
            当前方案支持近一个月事件脉络查询，历史方案按保存的时间区间查询。
          </div>
          <img src="@/assets/images/icon-question.png" alt="" class="name-question">
        </el-tooltip>
      </div>

      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%">
        <el-table-column prop="typeName" label="类型" align="center">
          <!-- <template slot-scope="scope">
              <div>{{ scope.row.warnGrade==1?'一般':scope.row.warnGrade==2?'中等':scope.row.warnGrade==3?'严重':''
                  }}
              </div>
          </template> -->
        </el-table-column>
        <el-table-column prop="host" label="来源" align="center"></el-table-column>
        <el-table-column prop="author" label="作者" align="center"></el-table-column>
        <el-table-column prop="publishTime" label="时间" align="center"></el-table-column>
        <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <div @click="goOrigin(scope.row)"
                 style="overflow: hidden;text-overflow: ellipsis;cursor: pointer;">{{
              replaceHtml(scope.row.title||'') }}
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>


    <div class="eventContextBlock">
      <div class="formH2">
        <div class="bluePoint"></div>
        <div>事件脉络</div>
        <el-tooltip placement="top" effect="light">
          <div slot="content">
            当前方案支持近一个月事件脉络查询，历史方案按保存的时间区间查询。
          </div>
          <img src="@/assets/images/icon-question.png" alt="" class="name-question">
        </el-tooltip>
      </div>
      <!-- <el-timeline v-show="activities.length>0" class="myTimeLine">
          <el-timeline-item v-for="(activity, index) in activities" :key="index"
              :timestamp="activity.publishTime">
              <span @click="goOrigin(activity)">{{replaceHtml(activity.title||'')}}</span>
              <img v-show="index==0" src="@/assets/images/firstRelease.png" alt="">
              <img v-show="activity.accountGrade==4" src="@/assets/images/isProvince.png" alt="">
              <img v-show="activity.accountGrade==2" src="@/assets/images/isCountry.png" alt="">
              <img v-show="activity.type==3&&activity.accountLevel==0" src="@/assets/images/daren.png" alt="">
              <img v-show="activity.type==3&&activity.accountLevel==1" src="@/assets/images/blueV.png" alt="">
              <img v-show="activity.type==3&&activity.accountLevel==2" src="@/assets/images/redV.png" alt="">
              <img v-show="activity.type==3&&activity.accountLevel==3" src="@/assets/images/orangeV.png" alt="">
          </el-timeline-item>
          <el-button v-show="!noMore" :disabled="disabled" :loading="timelineLoading" type="text"
              @click="moreTimeline">加载更多</el-button>
      </el-timeline> -->

      <div v-show="activities.length>0" class="eventContextList">
        <div v-for="(activity, index) in activities" :key="index" class="eventContextListItem">

          <div class="eventContext_num">{{index+1}}</div>

          <div class="eventContext_context">
            <div class="context_title">
              <span @click="goOrigin(activity)">{{replaceHtml(activity.title||'')}}</span>
              <!-- <img v-show="index==0" src="@/assets/images/firstRelease.png" alt=""> -->
              <img v-show="activity.accountGrade==4" src="@/assets/images/isProvince.png" alt="">
              <img v-show="activity.accountGrade==2" src="@/assets/images/isCountry.png" alt="">
              <img v-show="activity.type==3&&activity.accountLevel==0" src="@/assets/images/daren.png"
                   alt="">
              <img v-show="activity.type==3&&activity.accountLevel==1" src="@/assets/images/blueV.png"
                   alt="">
              <img v-show="activity.type==3&&activity.accountLevel==2" src="@/assets/images/redV.png"
                   alt="">
              <img v-show="activity.type==3&&activity.accountLevel==3" src="@/assets/images/orangeV.png"
                   alt="">
            </div>
            <div class="context_info">
              <span>{{ activity.publishTime }}</span>
              <span>媒体：{{activity.typeName}}</span>
              <span>相似文章数量：<span style="cursor: pointer;" @click="goSimilar(activity)">{{activity.similarCount||0}}</span></span>
            </div>
          </div>

        </div>
      </div>
      <el-empty v-show="activities.length==0" v-loading="timelineLoading" description="无数据"></el-empty>
    </div>
  </div>
</template>

<script>
import {replaceHtml} from '@/utils/index';
import {getEventContextApi, getFirstReleaseApi} from "@/api/publicOpinionMonitor/index.js";

export default {
  components: {},
  props: {
    checkedNode: {
      type: Object,
      default: () => {
      },
    },
  },
  data() {
    return {
      replaceHtml,
      tableLoading: false,
      tableData: [],
      timelineLoading: false,
      activities: [],
      pageNum: 1,
      total: 0,

    }
  },

  computed: {
    noMore() {
      return this.activities.length >= this.total
    },
    disabled() {
      return this.timelineLoading || this.noMore
    }
  },
  async mounted() {
  },
  methods: {
    init() {
      this.pageNum = 1
      this.tableData = []
      this.activities = []
      this.total = 0
      this.getFirstRelease()
      this.getEventContext()
    },
    //获取首发
    getFirstRelease() {
      let params = {
        planId: this.checkedNode.id,
      }

      this.tableLoading = true
      getFirstReleaseApi(params).then(res => {
        if (res.code == 200) {
          this.tableData = res.data
          this.tableLoading = false
        }
      }).catch(err => {
        this.tableLoading = false
      })
    },
    //获取事件脉络
    getEventContext() {
      let params = {
        planId: this.checkedNode.id,
        pageSize: 10,
        pageNum: this.pageNum,
      }
      this.timelineLoading = true
      getEventContextApi(params).then(res => {
        if (res.code == 200) {
          this.activities = [...this.activities, ...res.rows]
          this.total = res.total
          this.timelineLoading = false
        }
      }).catch(err => {
        this.timelineLoading = false
      })
    },
    moreTimeline() {
      this.pageNum = this.pageNum + 1
      this.getEventContext()
    },

    // 查看原文
    goOrigin(item) {
      window.open(item.url, '_blank')
    },
    goSimilar(row) {
      if (row.similarCount <= 1) {
        return
      }
      let query = {
        startTime: this.queryForm.startTime || '',
        endTime: this.queryForm.endTime || '',
        id: row.id,
        count: row.count,
        keyWord1: this.queryForm.keyWord1,
      }
      if (this.queryForm.timeIndex || this.queryForm.timeIndex == 0) {
        let timeRange = this.updateQueryTimeRange(this.queryForm.timeIndex)
        query.startTime = timeRange[0]
        query.endTime = timeRange[1]
        console.log('query', query)
      }

      if (this.queryForm.historyFlag == '0') {//当前方案只查一个月
        query.startTime = moment(new Date()).subtract(1, 'months').format('YYYY-MM-DD 00:00:00')
        query.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      }

      const planRoute = this.$router.resolve({
        path: '/fullSearch/similarArticles',
        query
      })
      window.open(planRoute.href, '_blank')
    },
    updateQueryTimeRange(timeIndex) {
      if (timeIndex == 0) {
        return [moment(new Date()).format('YYYY-MM-DD 00:00:00'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
      } else {
        return [moment(new Date()).subtract(timeIndex, 'days').format('YYYY-MM-DD 00:00:00'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.eventContext {
  padding: 0px 40px;
  font-family: PingFangSC, PingFang SC;

  .eventContextBlock {
    margin-top: 25px;

    .myTimeLine {
      margin: 30px 0 0 30px;

      span {
        vertical-align: middle;
        cursor: pointer;
      }

      img {
        vertical-align: middle;
        margin-left: 5px;
        height: 1.3em;
      }
    }


    .eventContextList {
      height: 500px;
      // width: 500px;
      overflow-y: auto;

      .eventContextListItem {
        display: flex;
        justify-content: flex-start;
        border-top: 1px dashed #eee;
        padding: 10px 0;

        &:first-child {
          border-top: none;
          padding-top: 0;
        }

        .eventContext_num {
          height: 25px;
          width: 25px;
          text-align: center;
          line-height: 25px;
          color: #fff;
          background-color: #1B76FF;
          border-radius: 4px;
          font-weight: bold;
          margin-right: 10px;
          flex-shrink: 0
        }

        .eventContext_context {
          .context_title {
            display: flex;
            align-items: center;

            span {
              vertical-align: text-top;
              cursor: pointer;


              display: -webkit-box;
              -webkit-line-clamp: 1;
              /* 显示的行数 */
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            img {
              vertical-align: middle;
              margin-left: 5px;
              height: 16px;
            }
          }

          .context_info {
            margin-top: 10px;

            > span {
              margin-right: 20px;
              white-space: nowrap;
              color: #1677FF;
            }
          }

        }
      }
    }


    .eventContextList::-webkit-scrollbar-track-piece {
      background-color: transparent;
    }

    .eventContextList::-webkit-scrollbar {
      width: 7px;
      height: 7px;
      background-color: transparent;
    }

    .eventContextList::-webkit-scrollbar-thumb {
      border-radius: 5px;
      background-color: hsla(220, 4%, 58%, .3);
    }
  }

  .formH2 {
    font-size: 18px;
    line-height: 25px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;

    div {
      display: inline-block;
      vertical-align: middle;
    }

    .name-question {
      width: 16px;
      margin-left: 5px;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .bluePoint {
    width: 8px;
    height: 8px;
    background: #247CFF;
    border-radius: 50%;
    margin-right: 5px;
  }


  ::v-deep.el-timeline-item__node {
    background-color: #ffffff;
    border: 2px solid #1677FF;
  }

  ::v-deep.el-timeline-item__node--normal {
    width: 10px;
    height: 10px;
    left: 0px;
  }
}
</style>
