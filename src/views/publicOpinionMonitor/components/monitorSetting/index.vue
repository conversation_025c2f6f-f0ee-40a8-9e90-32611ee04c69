<template>
  <div class="monitorSetting">
    <div class="settingSwitch">
      <span class="settingLable">预警设置</span>
      <el-switch class="switch" style="display: inline-block; height: 100%" v-model="settingForm.status"
                 active-color="#247CFF" inactive-color="#8A8A8A" active-text="开" inactive-text="关" :active-value="1"
                 :inactive-value="0">
      </el-switch>
      <span class="settingTip">请设置您的方案预警词、接收方式等条件，以便我们帮助您更精准、更及时的掌握用情信息</span>
    </div>

    <el-form v-show="settingForm.status" ref="form" class="formMain" :model="settingForm" label-width="160px"
             :rules="rules">


      <div class="formSetPart2">
        <div class="formH2">
          <div class="bluePoint"></div>
          <div>预警词设置</div>
        </div>
        <el-form-item label="预警词：" prop="kw1">
          <el-tooltip class="item" effect="light" placement="top-start" popper-class="addProgram-tip-class">
            <template #content>
              <div class="explain-words">请设置您的预警关键词，以空格隔开，设置后会从监测方案的信息中二次筛选出相关信息；
              </div>
              <div class="explain-words">如果不设，系统会给您推送与监测方案相关的信息</div>
            </template>
            <img class="tipIconGrey" src="@/assets/images/icon-question.png" alt="">
          </el-tooltip>
          <el-input v-model="settingForm.kw1" placeholder="输入预警词，以空格隔开" style="width: 100%"/>
          <!-- <div class="el-textarea__icon" @click="clearTextarea('kw1')">
                  <img src="@/assets/images/clearWords.png" alt="">
                  清空
              </div>
              <div class="el-textarea__icon" @click="copyText(settingForm.kw1)">
                  <img src="@/assets/images/copyWords.png" alt="">
                  复制
              </div>
              <div v-show="settingForm.historyFlag!='1'" class="el-textarea__icon"
                  @click="openLexiconDialog('kw1')">
                  <img src="@/assets/images/lexicon.png" alt="">
                  词库
              </div> -->
        </el-form-item>

        <el-form-item label="排除词：" prop="excludeWord">
          <el-tooltip class="item" effect="light" placement="top-start" popper-class="addProgram-tip-class">
            <template #content>
              <div class="explain-words">命中排除关键词的信息，将不进入系统，关键词之间用空格分开。</div>
            </template>
            <img class="tipIconGrey" src="@/assets/images/icon-question.png" alt="">
          </el-tooltip>
          <el-input v-model="settingForm.excludeWord" :disabled="settingForm.historyFlag=='1'"
                    placeholder="输入排除词，以空格隔开" style="width: 100%"/>
        </el-form-item>
      </div>
      <div class="formSetPart2">
        <div class="formH2">
          <div class="bluePoint"></div>
          <div>预警内容设置</div>
        </div>
        <el-form-item label="媒体类型：" prop="type">
          <check-box v-model="settingForm.type" :list="mediaList"></check-box>
        </el-form-item>
        <el-form-item label="倾向性：" prop="emotionFlag">
          <check-box v-model="settingForm.emotionFlag" :list="emotionData"></check-box>
        </el-form-item>
        <el-form-item label="监控范围：" prop="searchPosition">
          <check-box v-model="settingForm.searchPosition" :list="searchPositionList"></check-box>
        </el-form-item>
        <el-row>
          <el-form-item label="去除重复：" prop="isOriginal">
            <el-radio-group v-model="settingForm.isOriginal">
              <el-radio label="0">否</el-radio>
              <el-radio label="1">是</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-row>

        <el-row>
          <el-form-item label="相似信息：" prop="similarInfo" style="color: #666666;">
            相似信息 ≥ <el-input v-model="settingForm.similarInfo" style="width: 100px;" controls-position="right" @input="handleSimilarInfoInput"></el-input> 条
          </el-form-item>
        </el-row>
      </div>

      <div class="formSetPart2">
        <div class="formH2">
          <div class="bluePoint"></div>
          <div>预警分级设置</div>
        </div>

        <div class="sourceLever">
          <div class="sourceLeverRow topRow">
            <div class="sourceLever-title">信源级别</div>
            <div class="sourceLever-item">
              <div>一般</div>
              <div>中等</div>
              <div>严重</div>
            </div>
          </div>
          <el-radio-group v-for="item in sourceList" v-model="grading[item.dictValue]" class="sourceLeverRow"
                          :key="item.dictValue">
            <div class="sourceLever-title">{{item.dictLabel}}</div>
            <div class="sourceLever-item">
              <el-radio :label="1">
                <div/>
              </el-radio>
              <el-radio :label="2">
                <div/>
              </el-radio>
              <el-radio :label="3">
                <div/>
              </el-radio>
            </div>
          </el-radio-group>
        </div>

      </div>
      <div class="formSetPart2">
        <div class="formH2">
          <div class="bluePoint"></div>
          <div>预警接收设置</div>
        </div>

        <el-form-item label="接收时间：" prop="acceptTime">
          <el-radio-group v-model="settingForm.acceptTime">
            <el-radio :label="0">每天</el-radio>
            <el-radio :label="1">工作日</el-radio>
            <el-radio :label="2">节假日</el-radio>
          </el-radio-group>
        </el-form-item>
        <span style="display: flex;justify-content: flex-start;">
                    <el-form-item label="预警方式：" prop="warningType">
                        <el-select v-model="settingForm.warningType" placeholder="请选择预警方式">
                            <el-option label="定时" :value="0"></el-option>
                            <el-option label="实时" :value="1"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="预警间隔：" prop="intervalTime" v-show="settingForm.warningType==0">
                        <el-select v-model="settingForm.intervalTime" placeholder="请选择预警间隔">
                            <el-option label="5分钟" :value="5"></el-option>
                            <el-option label="15分钟" :value="15"></el-option>
                            <el-option label="30分钟" :value="30"></el-option>
                            <el-option label="1小时" :value="60"></el-option>
                            <el-option label="2小时" :value="120"></el-option>
                            <el-option label="3小时" :value="180"></el-option>
                            <el-option label="4小时" :value="240"></el-option>
                            <el-option label="5小时" :value="300"></el-option>
                            <el-option label="8小时" :value="480"></el-option>
                        </el-select>
                       <span style="vertical-align: middle;color: #606266; "><span
                         style="color: red;margin: 0px 5px 0 20px;">*</span>短信推送需间隔30分钟及以上</span>
                    </el-form-item>
                </span>
        <el-form-item label="接收时段：">
          <!-- <el-time-picker v-model="value1" placeholder="任意时间点"></el-time-picker> -->
          <el-time-select style="width: 120px;" placeholder="起始时间" v-model="settingForm.startReceiveTime"
                          :picker-options="{start: '00:00',step: '01:00',end: '24:00'}" :clearable="false">
          </el-time-select>
          —
          <el-time-select style="width: 120px;" placeholder="结束时间" v-model="settingForm.endReceiveTime"
                          :picker-options="{start: '00:00',step: '01:00',end: '24:00'}" :clearable="false">
          </el-time-select>
        </el-form-item>
        <el-form-item label="接收方式：">
          <div class="receiveWay">
              <span class="receiveTitle">
                  <span>
                      <img src="@/assets/images/warning.svg" alt="">
                      系统推送
                  </span>
                  <el-switch class="switch" style="display: inline-block; height: 100%"
                      v-model="settingForm.system" active-color="#247CFF" inactive-color="#8A8A8A"
                      active-text="开" inactive-text="关" :active-value="1" :inactive-value="0">
                  </el-switch>
              </span>
          </div>

          <div class="receiveWay">
                        <span class="receiveTitle">
                            <span>
                                <img src="@/assets/images/emailPush.svg" alt="">
                                邮件推送
                            </span>
                            <el-switch class="switch" style="display: inline-block; height: 100%"
                                       v-model="settingForm.mail" active-color="#247CFF" inactive-color="#8A8A8A"
                                       active-text="开" inactive-text="关" :active-value="1" :inactive-value="0">
                            </el-switch>
                        </span>
            <div v-show="settingForm.mail" class="receiveMain">
                            <span class="receiveMain-option">
                                接收人：请选择需要接收消息的联系人
                                <el-button type="text" style="color: #247CFF;"
                                           @click="resetContacts('1')">重新设置接收人</el-button>
                            </span>
              <el-table :data="emailContactsList" border>
                <el-table-column prop="username" label="姓名" width="300"></el-table-column>
                <el-table-column prop="email" label="邮箱地址"></el-table-column>
                <el-table-column align="center" label="操作" width="200">
                  <template #default="scope">
                    <el-button slot="reference" type="text" style="color: #247CFF;"
                               @click="delContactsList('emailContactsList',scope.row)">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="receiveWay">
                        <span class="receiveTitle">
                            <span>
                                <img src="@/assets/images/messagePush.svg" alt="">
                                短信推送
                            </span>
                            <el-switch :disabled="!messageSetting" class="switch"
                                       style="display: inline-block; height: 100%" v-model="settingForm.message"
                                       active-color="#247CFF" inactive-color="#8A8A8A" active-text="开"
                                       inactive-text="关"
                                       :active-value="1" :inactive-value="0">
                            </el-switch>
                        </span>
            <div v-show="settingForm.message" class="receiveMain">
                            <span class="receiveMain-option">
                                接收人：请选择需要接收消息的联系人
                                <el-button type="text" style="color: #247CFF;"
                                           @click="resetContacts('0')">重新设置接收人</el-button>
                            </span>
              <el-table :data="phoneContactsList" border>
                <el-table-column prop="username" label="姓名" width="300"></el-table-column>
                <el-table-column prop="phone" label="手机号"></el-table-column>
                <el-table-column align="center" label="操作" width="200">
                  <template #default="scope">
                    <el-button slot="reference" type="text" style="color: #247CFF;"
                               @click="delContactsList('phoneContactsList',scope.row)">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>


          <div class="receiveWay" v-hasPermi="['monitor:setting:chat']">
                        <span class="receiveTitle">
                            <span>
                                <img src="@/assets/images/wechatPush.svg" alt="">
                                微信推送
                            </span>
                            <el-switch class="switch" style="display: inline-block; height: 100%"
                                       v-model="settingForm.chat" active-color="#247CFF" inactive-color="#8A8A8A"
                                       active-text="开" inactive-text="关" :active-value="1" :inactive-value="0">
                            </el-switch>
                        </span>
            <div v-show="settingForm.chat" class="receiveMain">
                            <span class="receiveMain-option">
                                接收人：请选择需要接收消息的联系人
                                <el-button type="text" style="color: #247CFF;"
                                           @click="resetContacts('2')">重新设置接收人</el-button>
                            </span>
              <el-table :data="wechatContactsList" border>
                <el-table-column prop="username" label="姓名" width="300"></el-table-column>
                <el-table-column prop="wxUserName" label="微信号"></el-table-column>
                <el-table-column align="center" label="操作" width="200">
                  <template #default="scope">
                    <el-button slot="reference" type="text" style="color: #247CFF;"
                               @click="delContactsList('wechatContactsList',scope.row)">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <div style="text-align: center;margin-bottom: 20px;">
            <span class="dialog-footer">
                <el-button type="primary" :loading="btnLoading" @click="submitForm">确定</el-button>
                <el-button @click="emits('tabBack')">取消</el-button>
            </span>
    </div>
    <lexiconDialog :visible.sync="lexiconDialogVisible" @visibleChange="lexiconVisibleChange"
                   :lexiconType="lexiconType" @submit="submitWords"></lexiconDialog>


    <el-dialog :visible.sync="sendMsgDialog" title="选择推送用户" width="50%" @close="cancelMsgDialog">
      <el-form ref="sendMsgForm" @submit.prevent>

        <el-form-item label="接收人：" prop="contacts">
                    <span style="display: flex;justify-content: space-between;">
                        <div class="yellowTips">
                            <img src="@/assets/images/yellowWarn.svg" alt="">
                            <span>请选择需要接收消息的联系人</span>
                        </div>
                        <el-input style="margin: 0 40px;" v-model.trim="searchWord" placeholder="请输入联系人"
                                  clearable>
                            <el-button slot="append" icon="el-icon-search" @click="getContactsList"></el-button>
                        </el-input>
                        <el-button type="primary" plain icon="el-icon-plus" @click="addNewUser">添加</el-button>
                    </span>
        </el-form-item>
        <el-table ref="contactsTableRef" :data="contactsList" :row-key="(row) => row.id"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center"
                           :reserve-selection="true"></el-table-column>
          <el-table-column prop="username" label="姓名">
          </el-table-column>
          <el-table-column v-if="contactsListType=='1'" prop="email" label="邮箱">
          </el-table-column>
          <el-table-column v-if="contactsListType=='0'" prop="phone" label="手机号">
          </el-table-column>
          <el-table-column v-if="contactsListType=='2'" prop="wxUserName" label="微信号">
          </el-table-column>
          <el-table-column align="center" label="操作">
            <template #default="scope">
              <el-button v-show="contactsListType=='1'" type="text" style="color: #409eff;"
                         @click="checkEmail(scope.row)">邮箱验证
              </el-button>
              <el-button type="text" style="color: #409eff;" @click="updataRow(scope.row)">修改</el-button>
              <el-popconfirm title="确定删除该联系人吗？" @confirm="delRow(scope.row)" style="margin-left: 10px;">
                <el-button slot="reference" type="text" style="color: #f56c6c;">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

      </el-form>
      <template #footer>
        <div style="text-align: center;">
          <el-button type="primary" @click="submitSendMsgForm">确定</el-button>
          <el-button @click="cancelMsgDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>


    <contactsEdit :visible.sync="contactsDialog" :contactsRow="contactsRow" :contactsOption="contactsOption"
                  :type="contactsListType" @visibleChange="contactsVisibleChange" @afterSubmit="contactsSubmit">
    </contactsEdit>
  </div>

</template>

<script>
import {copyText} from '@/utils/index';
import {getContactsApi, delContactsApi} from "@/api/search/index";
import {warnChangeApi, warnGetApi, mailTestApi} from "@/api/publicOpinionMonitor/index.js";
import lexiconDialog from '../lexiconDialog/index.vue'

import contactsEdit from '@/views/publicOpinionMonitor/components/contactsEdit'

import CheckBox from "@/components/CheckBox";

export default {
  components: {
    lexiconDialog,

    CheckBox,
    contactsEdit,
  },
  props: {
    checkedNode: {
      type: Object,
      default: () => {
      },
    },
  },
  data() {
    return {

      monitorSettingStatus: true,
      mediaList: [],
      emotionData: [{dictLabel: '中性', dictValue: '0'}, {dictLabel: '敏感', dictValue: '1'}, {
        dictLabel: '非敏感',
        dictValue: '2'
      }],
      searchPositionList: [{dictLabel: '标题', dictValue: '1'}, {dictLabel: '正文', dictValue: '2'}],

      sourceList: [],

      messageSetting: true,//短信报送是否置灰


      grading: {},

      settingForm: {
        typeId: '',
        kw1: '',
        excludeWord: '',


        type: [],
        emotionFlag: [],
        similarInfo:  '',
      },


      emailContactsList: [],//邮件推送联系人列表
      phoneContactsList: [],//短信推送联系人列表
      wechatContactsList: [],//微信推送联系人列表

      contactsListType: '',//确定打开弹窗设置的是哪种接收方式   0: 短信 1: 邮件 2: 微信


      sendMsgDialog: false,
      searchWord: '',
      contactsList: [],
      selectedRows: [],


      contactsDialog: false,
      contactsRow: {},
      contactsOption: 'add',


      rules: {
        typeId: [{required: true, message: '请选择方案分类', trigger: 'blur'}],
        similarInfo: [
          {
            validator: this.validateSimilarInfo,
            trigger: ['blur', 'change']
          }
        ],
      },
      btnLoading: false,
      optionProps: {
        multiple: true,
        value: 'id',
        label: 'name',
        children: 'children',
        emitPath: false,
        checkStrictly: true
      },

      lexiconDialogVisible: false,
      lexiconType: '',
    }
  },

  watch: {
    settingForm: {
      immediate: true, // 确保在初始渲染时也能执行此函数
      deep: true,
      handler(newval, oldVal) {
        // 选择30分钟以下，短信设置置灰，不可设置；若已设置短信接收人，切换至30分钟以下，则预警不触发
        if (newval.warningType == 0 && newval.intervalTime < 30) {
          this.settingForm.message = 0
          this.messageSetting = false
        } else {
          this.messageSetting = true
        }
      },
    },
  },
  async mounted() {
    await this.querySysList()
    // this.getWarnSet()
  },
  methods: {

    init() {
      this.getWarnSet()
    },

    // 处理相似信息输入，只允许正整数或空字符串
    handleSimilarInfoInput(value) {
      // 如果是空字符串，直接允许
      if (value === '') {
        this.settingForm.similarInfo = ''
        return
      }

      // 移除所有非数字字符
      const numericValue = value.replace(/[^\d]/g, '')

      // 如果处理后为空，设置为空字符串
      if (numericValue === '') {
        this.settingForm.similarInfo = ''
        return
      }

      // 移除前导零（但保留单个0）
      const cleanValue = numericValue.replace(/^0+/, '') || '0'

      // 如果值为0，设置为空字符串（因为我们只要正整数）
      if (cleanValue === '0') {
        this.settingForm.similarInfo = ''
        return
      }

      this.settingForm.similarInfo = cleanValue
    },

    // 验证相似信息字段
    validateSimilarInfo(rule, value, callback) {
      // 允许空字符串
      if (value === '' || value === null || value === undefined) {
        callback()
        return
      }

      // 检查是否为字符串类型的数字
      if (typeof value === 'string') {
        // 检查是否只包含数字
        if (!/^\d+$/.test(value)) {
          callback(new Error('只能输入正整数'))
          return
        }

        // 检查是否为正整数（不能为0）
        const num = parseInt(value, 10)
        if (num <= 0) {
          callback(new Error('请输入大于0的正整数'))
          return
        }
      } else if (typeof value === 'number') {
        // 如果是数字类型，检查是否为正整数
        if (!Number.isInteger(value) || value <= 0) {
          callback(new Error('请输入大于0的正整数'))
          return
        }
      } else {
        callback(new Error('只能输入正整数'))
        return
      }

      callback()
    },

    // 获取搜索字段列表
    async querySysList() {
      // 获取媒体类型
      try {
        let res = await this.getDicts('sys_media_type')
        this.mediaList = res.data
        // let choseList = this.mediaList.map((item)=>item.dictValue)
        // this.settingForm.type = choseList
      } catch (error) {
        this.$message.error(error)
      }

      // 获取账号类型
      try {
        let res = await this.getDicts('account_level')
        this.sourceList = res.data
      } catch (error) {
        this.$message.error(error)
      }
    },

    //重新设置接收人

    async resetContacts(type) {
      this.sendMsgDialog = true
      //确定打开弹窗设置的是哪种接收方式
      this.contactsListType = type
      await this.getContactsList()
      // 0: 短信 1: 邮件 2: 微信
      if (this.contactsListType == '1') {
        this.toggleSelection(this.emailContactsList)
      } else if (this.contactsListType == '0') {
        this.toggleSelection(this.phoneContactsList)
      } else if (this.contactsListType == '2') {
        this.toggleSelection(this.wechatContactsList)
      }
    },

    //同步接收人表单的已选项
    toggleSelection(list) {
      this.$refs.contactsTableRef.clearSelection();
      if (!list || list?.length == 0) return
      this.contactsList.forEach(row => {
        list?.forEach(rows => {
          if (row.id === rows.id) {
            this.$refs.contactsTableRef.toggleRowSelection(row, true)
          }
        })
      })
    },

    //关闭接收人弹窗
    cancelMsgDialog() {
      this.sendMsgDialog = false
      this.searchWord = ''
      // this.updateVisible()
    },
    //获取联系人列表
    async getContactsList() {
      let param = {
        username: this.searchWord,
        type: this.contactsListType
      }
      await getContactsApi(param).then(res => {
        this.contactsList = res.data
      })
    },

    //table选中项改变
    handleSelectionChange(val) {
      this.selectedRows = val
    },


    // 提交表单
    submitSendMsgForm() {
      let rows = JSON.parse(JSON.stringify(this.selectedRows))
      // 0: 短信 1: 邮件 2: 微信
      if (this.contactsListType == '1') {
        this.emailContactsList = rows
      } else if (this.contactsListType == '0') {
        this.phoneContactsList = rows
      } else if (this.contactsListType == '2') {
        this.wechatContactsList = rows
      }
      this.cancelMsgDialog()
    },

    //删除对应接收方式的联系人列表
    delContactsList(key, row) {
      this[key] = this[key].filter(item => item.id !== row.id);
    },

    contactsVisibleChange(val) {
      this.contactsDialog = val
    },


    addNewUser() {
      this.contactsDialog = true
      this.contactsOption = 'add'
    },

    async updataRow(row) {
      this.contactsDialog = true
      this.contactsOption = 'edit'
      this.contactsRow = row

    },

    //新增编辑联系人弹窗回调
    contactsSubmit(res) {
      this.getContactsList()
    },


    //邮箱验证
    checkEmail(row) {
      if (!row.email) {
        this.$message.error('邮箱不存在!')
        return
      }
      this.$confirm(`是否向邮箱${row.email}发送验证邮件？`, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let params = {
          toEmail: row.email
        }
        mailTestApi(params).then(res => {
          if (res.code == 200) {
            this.$message.success('发送成功!')
          }
        })
      }).catch(() => {
      });
    },


    //删除联系人
    async delRow(row) {
      try {
        await delContactsApi(row.id)
        this.$message.success('删除成功')
        this.getContactsList()
      } catch (err) {

      }
    },


    // 回显数据字典 name-->id
    selectDictId(datas, value) {
      var actions = [];
      Object.keys(datas).some((key) => {
        if (datas[key].typeName == value) {
          actions.push(datas[key].typeId);
          return true;
        }
      })
      return actions.join('');
    },


    // 清空文本域
    clearTextarea(item) {
      this.settingForm[item] = ''
    },


    //获取预警设置
    getWarnSet() {
      let params = {
        planId: this.checkedNode.id,
      }
      warnGetApi(params).then(res => {
        if (res.code == 200) {
          if (!res.data) {
            this.settingForm = {
              type: this.mediaList.map((item) => item.dictValue),
              emotionFlag: this.emotionData.map((item) => item.dictValue),
              searchPosition: this.searchPositionList.map((item) => item.dictValue),
              isOriginal: '0',
              acceptTime: 0,
              warningType: 0,
              startReceiveTime: "08:00",
              endReceiveTime: "23:00",
              intervalTime: 30,
              system: 1,
              similarInfo: '',
            }
            this.sourceList.map(item =>
              this.$set(this.grading, item.dictValue, 1)
            )
          } else {
            let formdata = {
              ...res.data,
              type: res.data.type ? res.data.type.split(',') : [],
              emotionFlag: res.data.emotionFlag ? res.data.emotionFlag.split(',') : [],
              searchPosition: res.data.searchPosition ? res.data.searchPosition.split(',') : [],
            }

            this.settingForm = formdata
            this.grading = res.data.grading
            this.phoneContactsList = formdata.messageContact
            this.emailContactsList = formdata.mailContact
            this.wechatContactsList = formdata.chatContact
          }
        }
      })
    },


    // 提交按钮
    submitForm() {
      this.$refs['form'].validate((valid) => {

        if (valid) {
          let form = JSON.parse(JSON.stringify(this.settingForm))
          this.btnLoading = true
          let params = {
            ...form,
            planId: this.checkedNode.id,
            type: form.type ? form.type.join(',') : '',
            emotionFlag: form.emotionFlag ? form.emotionFlag.join(',') : '',
            searchPosition: form.searchPosition ? form.searchPosition.join(',') : '',
            messageContact: this.phoneContactsList,
            mailContact: this.emailContactsList,
            chatContact: this.wechatContactsList,
            grading: this.grading,
          }
          console.log('params', params)

          warnChangeApi(params).then(res => {
            if (res.code == 200) {
              this.$message.success('保存成功')
              this.btnLoading = false
            }
          }).catch(err => {
            this.btnLoading = false
          })
        }
      })
    },


    // 重置表单
    reset() {
      this.settingForm = {
        // 表单数据
        typeId: '',
        kw1: '',
        excludeWord: '',
      }
      this.$nextTick(() => {
        this.$refs['form'].resetFields()
      })
    },


    // 复制
    copyText(content) {
      copyText(content, false)
    },
    lexiconVisibleChange(val) {
      this.lexiconDialogVisible = val
    },

    openLexiconDialog(key) {
      this.lexiconDialogVisible = true
      // if (key == 'kw1') {
      //     this.lexiconType = 'kw1'
      // }
      this.lexiconType = key
    },

    submitWords(type, wordsList) {
      // console.log(type, wordsList);
      if (wordsList.length == 0 || !type) return

      //无该字段时，赋初始值为空字符串
      if (!this.settingForm[type]) {
        this.settingForm[type] = ''
      }

      if (type == 'monitorWord') {
        //有值且最后一位不为+时，添加+作为间隔
        if (this.settingForm[type] && this.settingForm[type]?.length > 0 && this.settingForm[type]?.slice(-1) !== '+') {
          this.settingForm[type] += '+'
        }
        this.settingForm[type] += `(${wordsList.join('|')})`
      } else {
        //有值且最后一位不为空格时，添加空格作为间隔
        if (this.settingForm[type] && this.settingForm[type]?.length > 0 && this.settingForm[type]?.slice(-1) !== ' ') {
          this.settingForm[type] += ' '
        }
        this.settingForm[type] += wordsList.join(' ')
      }
    },
  }
}
</script>

<style lang="scss" scoped>
.tipIconGrey {
  position: absolute;
  left: -90px;
  top: 12px;
  cursor: pointer;
  color: #fff;
  font-size: 16px;
}

.el-textarea__icon {
  color: #247CFF;
  text-align: end;
  cursor: pointer;
  width: fit-content;
  float: right;
  margin-left: 10px;

  img {
    height: 15px;
    vertical-align: text-bottom;
  }
}


.monitorSetting {
  padding: 0px 40px;
  font-family: PingFangSC, PingFang SC;

  .settingSwitch {
    padding: 30px 60px;

    .settingLable {
      display: inline-block;
      vertical-align: middle;
      font-size: 16px;
    }

    .switch {
      vertical-align: middle;
      margin-left: 15px;
      margin-right: 20px;
    }

    .settingTip {
      display: inline-block;
      vertical-align: middle;
      font-size: 12px;
      color: #999999;
    }
  }

  .formMain {
    ::v-deep .el-form-item__label {
      font-weight: 400;
      line-height: 40px;
    }

    ::v-deep .el-textarea__inner {
      font-family: Arial;
    }

    .bluePoint {
      width: 8px;
      height: 8px;
      background: #247CFF;
      border-radius: 50%;
      margin-right: 5px;
    }

    .formSetPart2 {
      margin-left: 50px;
    }

    .formH2 {
      font-size: 18px;
      line-height: 25px;
      margin-bottom: 15px;
      padding-bottom: 10px;
      border-bottom: 1px solid #eee;

      div {
        display: inline-block;
        vertical-align: middle;
      }
    }


    .sourceLever {
      width: 60%;
      padding-left: 78px;

      .sourceLeverRow {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        .sourceLever-title {
          // font-size: 14px;
          // color: #333333;
          width: 20%;
          font-weight: 400;
          font-size: 14px;
          color: #666666;
        }

        .sourceLever-item {
          flex: 1;
          display: flex;
          justify-content: space-between;

          & > * {
            width: 70px;
            text-align: center;
          }

          ::v-deep.el-radio__label {
            padding-left: 0;
          }
        }

      }

      .topRow {
        .sourceLever-title {
          font-weight: 600;
          font-size: 14px;
          color: #333333;
        }

        .sourceLever-item {
          font-weight: 600;
          font-size: 14px;
          color: #333333;
        }
      }

    }

    .receiveWay {
      .receiveTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 5px 0;
        border-bottom: 1px solid #EEEEEE;

        img {
          vertical-align: middle;
        }
      }

      .receiveMain {
        width: 100%;
        margin-bottom: 20px;

        .receiveMain-option {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 10px 0;
        }

        ::v-deep.el-table .el-table__header-wrapper th,
        .el-table .el-table__fixed-header-wrapper th {
          padding-top: 0;
          padding-bottom: 0;
          background-color: #F8FAFF;
        }
      }
    }

  }

}

.explain-words {
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji, Segoe UI Symbol;
  line-height: 24px;
}

.yellowTips {
  white-space: nowrap;
  line-height: 32px;
  background: #FFFBE6;
  border-radius: 4px;
  border: 1px solid #FAAD14;
  padding: 0px 10px;

  img {
    vertical-align: middle;
    height: 17px;
    margin-right: 5px;
  }

  span {
    vertical-align: middle;
  }
}

::v-deep.monitorSetting {

  /* switch按钮样式 */
  .switch .el-switch__label {
    position: absolute;
    top: 2px;
    display: none;
    color: #fff !important;
  }

  /*打开时文字位置设置*/
  .switch .el-switch__label--right {
    z-index: 1;
    left: 7px;
  }

  /* 调整打开时文字的显示位子 */
  .switch .el-switch__label--right span {
    margin-right: 9px;
  }

  /*关闭时文字位置设置*/
  .switch .el-switch__label--left {
    z-index: 1;
    left: 10px;
  }

  /* 调整关闭时文字的显示位子 */
  .switch .el-switch__label--left span {
    margin-left: 9px;
  }

  /*显示文字*/
  .switch .el-switch__label.is-active {
    display: block;
  }

  /* 调整按钮的宽度 */
  .switch.el-switch .el-switch__core,
  .el-switch .el-switch__label {
    // width: 50px !important;
    margin: 0;
  }
}
</style>
