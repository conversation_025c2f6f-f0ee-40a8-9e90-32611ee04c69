<template>
  <div class="directSource">
    <div class="directSource_top">
      <div class="directSource_top_colorBlock"/>
      <div class="directSource_top_title">{{!isExclude?'定向信源':'排除信源'}}</div>
      <el-tooltip placement="top" effect="light">
        <div slot="content">
          {{!isExclude?'定向信源是基于您设置的关键词，只查看某几个网站、某几个微博账号以及某些微信公众账号的信息。':
          '排除信源是基于系统提供的所有信源中，您可自由的排除对你无用的信源，增加信息准确性。'}}
        </div>
        <img src="@/assets/images/icon-question.png" alt="" class="name-question">
      </el-tooltip>
    </div>
    <div class="formDiv">
      <div class="dataSel">
        <el-button type="primary" size="mini" icon="el-icon-plus" @click="handleAdd">添加</el-button>
        <span></span>
        <div class="dataSel-left">
          <div class="jump-page">
            <i class="el-icon-arrow-left" @click="goLeft"></i>
            <el-input-number size="mini" @change="submitSearch()" v-model="queryForm.pageNum"
                             :max="totalPage" :min="1" placeholder="请输入内容"></el-input-number>
            <span class="jump-line">/</span>
            <span>{{totalPage}}</span>
            <i class="el-icon-arrow-right" @click="goRight"></i>
          </div>
          <el-input v-model.trim="queryForm.keywords" size="small" clearable placeholder="输入关键词"
                    class="input-with-select">
            <el-select slot="prepend" size="small" v-model="queryForm.sourceType" placeholder="平台类型"
                       style="width: 130px;">
              <el-option label="全部" value=""/>
              <el-option v-for="item in mediaList" :key="item.dictValue" :label="item.dictLabel"
                         :value="item.dictValue"/>
            </el-select>
          </el-input>
          <el-button size="small" :loading="searchLoading" icon="el-icon-search" type="primary"
                     class="search-btns" @click="submitSearch('search')">
            搜索
          </el-button>
        </div>
      </div>
      <div class="optionSet">
                <span>
                    <span class="totalNum">{{`已使用${usedNum}项，共计${total}；`}}</span>
                    <el-button class="textButton" type="text" @click="()=>handleDel()">删除</el-button>
                </span>
        <span></span>
        <span>
                    <el-button class="textButton" type="text" @click="exportAll"
                               :disabled="downloadLoading">全部导出</el-button>
                    <el-button class="textButton" type="text" @click="handleImport">批量导入</el-button>
                    <el-popover ref="quotePopover" placement="left" trigger="click" @after-enter="afterEnter"
                                :append-to-body="false">
                        <template>
                            <el-tabs v-if="ifShow" class="treeTabs" v-model="activeName" @tab-click="setTab">
                                <el-tab-pane label="方案来源" name="1">
                                    <el-tree ref="cusTreeRef" :data="treeDataauto" default-expand-all
                                             class="popoverTree">
                                        <template #default="{ node, data }">
                                            <span v-if="node.level == 1" class="treeitemTitle">{{ data.name }}</span>
                                            <el-radio v-else v-model="checkRadio" :label="data.id">
                                                {{ data.name}}
                                            </el-radio>
                                        </template>
                                    </el-tree>
                                    <el-button type="primary" size="mini" @click="handleCover">覆盖</el-button>
                                    <el-button type="primary" size="mini" @click="handleSuper">叠加</el-button>
                                    <el-button size="mini" @click="cancelPovover">取消</el-button>
                                </el-tab-pane>
                            </el-tabs>
                        </template>
                        <el-button slot="reference" class="textButton" type="text">引用</el-button>
                    </el-popover>
                </span>
      </div>
      <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" style="width: 100%" border
                :header-cell-style="{background:'#F8FAFF'}" @selection-change="handleSelectionChange">
        <template slot="empty">
          <el-empty :image-size="100" :image="require('@/assets/images/nodata.svg')"
                    description="暂无数据"></el-empty>
        </template>
        <el-table-column type="selection" width="50" align="center"></el-table-column>
        <el-table-column label="序号" width="80" align="center">
          <template #default="scope">
            <span>{{(queryForm.pageNum - 1) * queryForm.pageSize + scope.$index + 1}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="名称" align="center"></el-table-column>
        <el-table-column prop="sourceType" label="类别" align="center">
          <template #default="scope">
            <span>{{sourceTypeFormat(scope.row.sourceType)}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="settingHost" label="网址" align="center"></el-table-column>
        <el-table-column prop="state" label="状态" align="center">
          <template #default="scope">
            <span>{{scope.row.state==1?'启用':'禁用'}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="options" label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleDel(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="jump-page" style="text-align: right;margin:10px 0 0">
        <i class="el-icon-arrow-left" @click="goLeft"></i>
        <el-input-number size="mini" @change="submitSearch()" v-model="queryForm.pageNum" :max="totalPage"
                         :min="1" placeholder="请输入内容"></el-input-number>
        <span class="jump-line">/</span>
        <span>{{totalPage}}</span>
        <i class="el-icon-arrow-right" @click="goRight"></i>
      </div>
    </div>

    <el-dialog title="上传信源" :visible.sync="importDialog" width="700px" append-to-body>
      <div class="importDialogTips">
        <img :src="require('@/assets/images/warningIcon.svg')" alt="">
        <span>请下载信源模板，按照模板格式，填写并上传您需要添加的信源</span>
      </div>
      <el-upload class="uploadFile" ref="upload" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
                 :action="upload.url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress"
                 :on-success="handleFileSuccess" :auto-upload="false" drag :data="{settingType,plateId:checkedNode.id}">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">
          将文件拖到此处，或
          <em>点击上传</em>
        </div>
      </el-upload>

      <div slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button type="primary" @click="submitFileForm">确定</el-button>
        <el-button @click="importDialog = false">取消</el-button>
        <div style="margin-top: 20px;">
          <el-link type="primary" @click="importTemplate">下载模版</el-link>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="定向信源添加" :visible.sync="addSourceDialog" width="70%" append-to-body
               @close="submitSearch('search')">
      <el-tabs type="card">
        <el-tab-pane label="单条添加">

          <div class="filterRow">
                        <span>
                            <el-select size="small" v-model="addSourceForm.type" placeholder="平台类型"
                                       @change="changeType"
                                       class="filter-select">
                                <el-option v-for="item in mediaList" :key="item.dictValue"
                                           :label="item.dictLabel" :value="item.dictValue"/>
                            </el-select>

                            <!-- <el-select v-model="addSourceForm.type" placeholder="平台类型" @change="changeType" class="filter-select">
                              <el-option-group
                                v-for="group in mediaList"
                                :key="group.dictLabel"
                                :label="group.dictLabel">
                                <el-option
                                  v-for="item in group.options"
                                  :key="item.dictValue"
                                  :label="item.dictLabel"
                                  :value="item.dictValue">
                                </el-option>
                              </el-option-group>
                            </el-select> -->

                            <el-select v-show="addSourceForm.type=='6'" size="small"
                                       v-model="addSourceForm.domain" placeholder="平台" class="filter-select"
                                       clearable>
                                <el-option v-for="item in platformList" :key="item.dictValue" :label="item.dictLabel"
                                           :value="item.dictValue"/>
                            </el-select>
                            <el-select v-show="addSourceForm.type=='11'" size="small"
                                       v-model="addSourceForm.domain" placeholder="平台" class="filter-select"
                                       clearable>
                                <el-option v-for="item in videoPlatformList" :key="item.dictValue"
                                           :label="item.dictLabel"
                                           :value="item.dictValue"/>
                            </el-select>
                            <el-input v-model.trim="addSourceForm.nickname" size="small" clearable
                                      placeholder="输入关键词"
                                      class="filter-input"></el-input>
                            <el-button size="small" type="primary" class="filter-btns" @click="addSourceSearch">
                                查询
                            </el-button>
                          <!-- <el-button size="small" type="primary" plain class="filter-btns" @click="addSourceNew">
                              新增
                          </el-button> -->
                        </span>
            <span></span>
            <el-button size="small" type="primary" plain class="filter-btns" @click="addSourceNewList">
              批量增加
            </el-button>
          </div>


          <el-table ref="addTableRef" v-loading="addTableLoading" :data="addTableData" style="width: 100%"
                    :header-cell-style="{background:'#F8FAFF'}" @selection-change="handleAddSelectionChange">
            <template slot="empty">
              <el-empty :image-size="100" :image="require('@/assets/images/nodata.svg')"
                        description="暂无数据"></el-empty>
            </template>
            <el-table-column type="selection" width="50" align="center"></el-table-column>
            <el-table-column prop="nickname" label="名称" align="left"
                             header-align="center">
              <template slot-scope="scope">
                {{scope.row.nickname}}
                <img style="height: 30px;vertical-align: middle;" :src="scope.row.avatar" alt="">
              </template>
            </el-table-column>
            <el-table-column prop="type" label="类别" align="center">
              <template #default="scope">
                <span>{{sourceTypeFormat(scope.row.type)}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="domain" label="网址" align="center"></el-table-column>
            <el-table-column prop="accountId" label="账户ID" align="center"></el-table-column>
            <el-table-column prop="options" label="操作" align="center">
              <template slot-scope="scope">
                <img :src="require('@/assets/images/addIcon.svg')" alt=""
                     @click="addSourceNewOne(scope.row)">
              </template>
            </el-table-column>
          </el-table>
          <div class="jump-page" style="text-align: right;margin:10px 0 0">
            <i class="el-icon-arrow-left" @click="addGoLeft"></i>
            <el-input-number size="mini" @change="addSourceSearch()" v-model="addSourceForm.pageNum"
                             :max="addTotalPage" :min="1" placeholder="请输入内容"></el-input-number>
            <span class="jump-line">/</span>
            <span>{{addTotalPage}}</span>
            <i class="el-icon-arrow-right" @click="addGoRight"></i>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>


</template>

<script>
import {downGetBlobFile} from '@/utils/index'
import {getToken} from "@/utils/auth";
import {
  getTreeDataApi,
  getSourceSetting,
  searchSource,
  exportSouceApi,
  deleteSouceApi,
  importDataApi,
  addSourceSetting,
  overwriteSettingApi,
  addSettingApi
} from "@/api/publicOpinionMonitor/index.js";

export default {
  data() {
    return {
      mediaList: [],
      platformList: [
        {
          dictValue: 'www.xiaohongshu.com',
          dictLabel: '小红书'
        },
        {
          dictValue: 'www.toutiao.com',
          dictLabel: '今日头条'
        },
        {
          dictValue: 'www.dongchedi.com',
          dictLabel: '懂车帝'
        },
        {
          dictValue: 'www.yoojia.com',
          dictLabel: '有驾'
        },
        {
          dictValue: 'baijiahao.baidu.com',
          dictLabel: '百家号'
        },
        {
          dictValue: 'mbd.baidu.com',
          dictLabel: '百度'
        },
        {
          dictValue: 'xueqiu.com',
          dictLabel: '雪球'
        },
        {
          dictValue: 'www.yidianzixun.com',
          dictLabel: '一点资讯'
        },
        {
          dictValue: 'www.sohu.com',
          dictLabel: '搜狐'
        },
        {
          dictValue: 'c.m.163.com',
          dictLabel: '网易新闻'
        },
        {
          dictValue: 'www.meipian.cn',
          dictLabel: '美篇'
        },
        {
          dictValue: '3g.k.sohu.com',
          dictLabel: '搜狐新闻'
        },
        {
          dictValue: 'dy.163.com',
          dictLabel: '网易订阅'
        },
        {
          dictValue: 'user.qzone.qq.com',
          dictLabel: 'QQ空间'
        },
      ],
      videoPlatformList: [
        {
          dictValue: 'www.iesdouyin.com,live.douyin.com,www.douyin.com',
          dictLabel: '抖音'
        },
        {
          dictValue: 'www.dongchedi.com',
          dictLabel: '懂车帝'
        },
        {
          dictValue: 'www.ixigua.com',
          dictLabel: '西瓜'
        },
        {
          dictValue: 'www.toutiao.com',
          dictLabel: '头条'
        },
        {
          dictValue: 'www.kuaishou.com',
          dictLabel: '快手'
        },
        {
          dictValue: 'haokan.baidu.com,quanmin.baidu.com',
          dictLabel: '好看视频'
        },
        {
          dictValue: 't.bilibili.com,www.bilibili.com',
          dictLabel: '哔哩哔哩'
        },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        keywords: '',
        sourceType: '',
      },
      tableData: [],
      tableLoading: false,
      searchLoading: false,
      usedNum: 0,
      total: 0,
      totalPage: 0,
      selectedRows: [],
      downloadLoading: false,
      importDialog: false,//导入弹窗
      // 用户导入参数
      upload: {
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: {Authorization: "Bearer " + getToken()},
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/souce/importData"
      },

      activeName: '1',
      ifShow: false,

      treeDataauto: [],
      checkRadio: '',

      addSourceDialog: false,//定向信源添加弹窗
      addSourceForm: {
        pageNum: 1,
        pageSize: 10,
        nickname: '',
        type: '',
        domain: '',
      },
      addTotal: 0,
      addTotalPage: 0,
      addTableLoading: false,
      addTableData: [],
      addSelectedRows: [],
    }
  },
  mounted() {
    this.getMediaType()
    this.getPlatformList()
  },
  props: {
    isExclude: {
      type: Boolean,
      default: false,
    },
    checkedNode: {
      type: Object,
      default: () => {
      },
    },
    show: {
      type: Boolean,
      default: false,

    },
  },
  computed: {
    settingType() {
      return this.isExclude ? 2 : 1
    },
  },
  watch: {
    checkedNode(newVal, oldVal) {
      if (newVal && this.show) {//当前tab显示时，树节点改变才刷新
        this.$nextTick(() => {
          this.init()
        });
      }
    }
  },
  methods: {
    //每次切换到该tab需要调用的方法
    init() {
      this.submitSearch('search')
    },
    // 获取媒体类型
    async getMediaType() {
      try {
        let res = await this.getDicts('sys_media_type')
        this.mediaList = res.data
      } catch (error) {
        console.log(error)
      }
    },
    // 获取平台下拉
    async getPlatformList() {
      try {
        let res = await this.getDicts('sys_client_platform')
        this.platformList = res.data
        let videores = await this.getDicts('sys_search_short_video')
        this.videoPlatformList = videores.data
      } catch (error) {
        console.log(error)
      }
    },
    // 类别字典翻译
    sourceTypeFormat(item) {
      return this.selectDictLabel(this.mediaList, item);
    },
    goRight() {
      if (this.totalPage > this.queryForm.pageNum && this.totalPage > 0) {
        this.queryForm.pageNum++
        this.submitSearch()
      }
    },
    goLeft() {
      if (this.totalPage >= this.queryForm.pageNum && this.queryForm.pageNum > 1) {
        this.queryForm.pageNum--
        this.submitSearch()
      }
    },
    changeType() {
      this.addSourceForm.domain = ''
    },
    // 查询列表
    async submitSearch(type) {
      if (type == 'search') {
        this.queryForm.pageNum = 1
      }

      // name=合肥
      // settingHost=www.baidu.com
      // sourceType=1   信源类型 读字典：sys_media_type
      // settingType=1  1定向选择 2定向排除

      try {
        this.tableLoading = true
        let params = JSON.parse(JSON.stringify(this.queryForm))
        params.plateId = this.checkedNode.id
        params.settingType = this.settingType

        const res = await getSourceSetting(params)

        this.tableData = res.rows
        this.total = Number(res.total)
        this.totalPage = Math.ceil(this.total / this.queryForm.pageSize)
        this.tableLoading = false

      } catch (error) {
        console.log(error)
        this.tableLoading = false
      }
    },

    //table选中项改变
    handleSelectionChange(val) {
      this.selectedRows = val
    },
    //删除按钮
    handleDel(row) {
      if (this.selectedRows.length == 0 && !row) {
        this.$message.warning('请选择需要删除的定向信源')
        return
      }
      let params = {
        ids: row ? row.id : this.selectedRows.map(item => item.id).join(',')
      }
      deleteSouceApi(params).then(res => {
        this.$message.success('删除成功')
        this.selectedRows = []
        this.submitSearch()
      }).catch(err => {
        console.log(err)
      })
    },
    //导出全部
    exportAll() {
      if (this.tableData.length == 0) {
        this.$message.warning('没有添加信源')
        return
      }

      const req = {
        settingType: this.settingType,
        plateId: this.checkedNode.id
      }
      this.downloadLoading = true
      const message = this.$message({
        message: '导出中，请耐心等候',
        type: 'warning',
        duration: 0
      });
      // downGetBlobFile('/souce/export', req, '信源列表.xlsx', () => {
      //     this.downloadLoading = false
      //     message.close()
      //     this.$message.success('导出成功')
      // }, () => {
      //     this.downloadLoading = false
      //     message.close()
      // })
      exportSouceApi(req).then(response => {
        this.downloadLoading = false
        message.close()
        this.download(response.msg);
      })
    },
    //批量导入
    handleImport() {
      this.importDialog = true
    },
    //下载导入模板
    importTemplate() {
      let req = {settingType: this.settingType}
      downGetBlobFile('/souce/importTemplate', req, '定向信源上传模板.xlsx', () => {
        this.$message.success('下载成功')
      })
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      if (response.code == 200) {
        this.importDialog = false;
        this.$message({
          dangerouslyUseHTMLString: true,
          message: response.msg,
          type: 'success'
        });
        // this.$message.success(response.msg)
        this.submitSearch('search');
      } else {
        this.$message.warning(response.msg)
      }
    },
    setTab(tab, event) {
      this.activeName = tab.name
    },
    afterEnter() {
      this.getTreeData()
      this.ifShow = true
    },
    // 获取方案树数据
    async getTreeData(type) {
      this.searchLoading = true
      let params = {
        historyFlag: ''
      }
      // console.log('params', params)
      let resauto = await getTreeDataApi(params)
      if (resauto.code == 200) {
        const newdata = resauto.data.map((item) => {
          if (!item.children || item.children.length == 0) {
            item.disabled = true
          }
          return item
        })
        // newdata.forEach(item => {
        //     if (item.children) {
        //         item.children.forEach(child => {
        //             child.fatherid = item.id
        //         })
        //     }
        // })
        // console.log('newdata', newdata)
        this.treeDataauto = newdata
      }
      this.searchLoading = false
    },
    //关闭povover
    cancelPovover() {
      this.$refs['quotePopover'].doClose()
      this.checkRadio = ''
    },
    //覆盖
    async handleCover() {
      let params = {
        plateId: this.checkRadio,
        id: this.checkedNode.id,
        settingType: this.settingType
      }
      try {
        let res = await overwriteSettingApi(params)
        console.log(res)
        this.$message.success(res.msg)
        this.cancelPovover()
        this.submitSearch('search')
      } catch {

      }
    },
    //叠加
    async handleSuper() {
      let params = {
        plateId: this.checkRadio,
        id: this.checkedNode.id,
        settingType: this.settingType
      }
      try {
        let res = await addSettingApi(params)
        this.$message.success(res.msg)
        this.cancelPovover()
        this.submitSearch('search')
      } catch {

      }
    },


    //新增按钮
    handleAdd() {
      this.addSourceDialog = true
    },
    //定向信源添加-table选中项改变
    handleAddSelectionChange(val) {
      this.addSelectedRows = val
    },
    //定向信源添加-搜索
    async addSourceSearch() {
      if (!this.addSourceForm.type) {
        this.$message.warning('请选择平台类型')
        return
      }
      if (this.addSourceForm.type != '6' && this.addSourceForm.type != '11') {
        this.addSourceForm.domain = ''
      }
      try {
        this.addTableLoading = true
        let params = JSON.parse(JSON.stringify(this.addSourceForm))
        params.settingType = this.settingType
        params.plateId = this.checkedNode.id
        params.precision = 0 //0是模糊 1是精准

        const res = await searchSource(params)

        this.addTableData = res.rows
        this.addTotal = Number(res.total)
        this.addTotalPage = Math.ceil(this.addTotal / this.addSourceForm.pageSize)
        this.addTableLoading = false

      } catch (error) {
        console.log(error)
        this.addTableLoading = false
      }
    },
    addGoRight() {
      if (this.addTotalPage > this.addSourceForm.pageNum && this.addTotalPage > 0) {
        this.addSourceForm.pageNum++
        this.addSourceSearch()
      }
    },
    addGoLeft() {
      if (this.addTotalPage >= this.addSourceForm.pageNum && this.addSourceForm.pageNum > 1) {
        this.addSourceForm.pageNum--
        this.addSourceSearch()
      }
    },
    //定向信源添加-直接新增
    addSourceNew() {

    },
    //定向信源添加-批量添加
    async addSourceNewList() {
      if (this.addSelectedRows.length == 0) {
        this.$message.warning('请选择信源')
        return
      }
      let params = this.addSelectedRows.map(item => ({
        name: item.nickname,
        settingHost: item.domain,
        plateId: this.checkedNode.id,
        sourceType: this.addSourceForm.type,
        settingType: this.settingType
      }))
      console.log('params', params)
      try {
        await addSourceSetting(params)
        this.addSourceSearch()
      } catch (error) {
        console.log(error)
      }
    },
    //定向信源添加-单个添加
    async addSourceNewOne(row) {
      let params = [{
        name: row.nickname,
        settingHost: row.domain,
        plateId: this.checkedNode.id,
        sourceType: this.addSourceForm.type,
        settingType: this.settingType
      }]
      try {
        await addSourceSetting(params)
        this.addSourceSearch()
      } catch (error) {
        console.log(error)
      }
    },
  }

}
</script>

<style scoped lang="scss">
.directSource {
  .directSource_top {
    padding: 12px 0;
    border-bottom: 1px dashed #DCDEE0;
    background-color: #fff;

    .directSource_top_colorBlock {
      width: 6px;
      height: 16px;
      background: #247CFF;
      display: inline-block;
      vertical-align: middle;
    }

    .directSource_top_title {
      font-size: 18px;
      color: #333333;
      line-height: 25px;
      display: inline-block;
      vertical-align: middle;
      margin: 0 10px 0 20px;
    }

    .name-question {
      width: 16px;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .formDiv {
    padding: 15px 26px;

    .dataSel {
      display: flex;
      justify-content: space-between;

      .input-with-select {
        width: 300px;
      }

      .dataSel-left {
        display: flex;
        align-items: center;
      }

      .search-btns {
        margin-left: 15px;
      }
    }

    .optionSet {
      width: 100%;
      background: #F0F5FF;
      border: 1px solid #247CFF;
      display: flex;
      justify-content: space-between;
      padding: 8px 26px;
      margin: 10px 0 15px 0;

      .textButton {
        padding: 0;
        margin-left: 20px;
      }
    }
  }
}

.jump-page {
  margin-right: 50px;
  font-size: 14px;
  line-height: 22px;

  span {
    color: #000;

    &.jump-line {
      margin: 0 16px;
    }
  }

  i {
    color: #000;

    &.el-icon-arrow-right {
      margin-left: 18px;
    }

    &.el-icon-arrow-left {
      margin-right: 18px;
    }
  }
}

.treeTabs {
  ::v-deep .el-tree {
    .el-tree-node:hover {
      // background-color:#F5F7FA;
    }

    .el-dropdown {
      font-size: 12px;
    }

    .el-tree-node__content {
      // margin-left: 20px;
      height: 35px;
    }

    .is-expanded {
    }

    // is-leaf el-tree-node__expand-icon el-icon-caret-right
    .el-tree-node__expand-icon.is-leaf::before {
      content: url("~@/assets/images/closedFolder.png") !important;
    }

    .el-tree-node__children {
      .el-tree-node__expand-icon.is-leaf::before {
        content: '' !important;
      }
    }

    .el-icon-caret-right::before {
      content: url("~@/assets/images/closedFolder.png");
    }

    .el-tree-node__expand-icon.expanded::before {
      content: url("~@/assets/images/openedFolder.png");
    }

    .el-tree-node__expand-icon.expanded {
      -webkit-transform: none;
      transform: none
    }

    .el-tree-node__expand-icon.is-leaf::before {
      content: '';
    }
  }

  .popoverTree {
    margin-bottom: 20px;
    max-height: 300px;
    overflow-y: auto;
  }
}

.uploadFile {
  width: 100%;
  text-align: center;
  margin-top: 30px;
}

.filterRow {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;

  .filter-select {
    width: 130px;
    margin-right: 10px;
  }

  .filter-input {
    width: 180px;
  }

  .filter-btns {
    margin-left: 10px;
  }

  // ::v-deep .el-button--primary.is-plain {
  //     background: #fff;
  //     &:hover{
  //         background: #1890ff;
  //     }
  //     &:focus{
  //         // color: #1890ff;
  //     }
  // }
}

.importDialogTips {
  img {
    vertical-align: middle;
    margin-right: 5px;
  }

  span {
    font-size: 14px;
    color: #333333;
  }
}

::v-deep {
  .el-dialog__header {
    background-color: #F4F7FB;

    .el-dialog__title {
      color: #000;
    }
  }
}
</style>
