<template>
  <div>
    <el-dialog append-to-body class="lexiconDialog" :visible.sync="dialogVisible" title="词库" width="60%"
               @close="cancelDialog">

      <div class="dialogMain">

        <div class="dialogMain-left">
          <div class="topInfo">
            <!-- <div>
                <img class="localIcon" src="@/assets/images/localIcon.png" alt="">
                <span style="margin: 0 5px;">当前省市：</span>
                <span style="color: #000;">安徽</span>
            </div> -->
            <div class="topTitle">
              <div class="colorBlock"></div>
              词库标签
            </div>

            <el-input style="width: 250px;height: fit-content;" v-model.trim="searchWord" placeholder="请输入关键词"
                      clearable>
              <el-button slot="append" icon="el-icon-search" @click="getWordsList"></el-button>
            </el-input>
          </div>
          <div class="selectBorder">
            <el-radio-group v-model="checkedClass" style="width: 100%;">
              <el-collapse v-model="openedCollapse">
                <el-collapse-item v-for="item in classList" :key="item.type" :title="item.type"
                                  :name="item.type">
                  <el-radio v-for="itemIn in item.child" :key="itemIn.id" :label="itemIn.name+itemIn.word" border
                            @click.native="clickClassRadio($event,itemIn)">{{ itemIn.name }}
                  </el-radio>
                </el-collapse-item>
              </el-collapse>
            </el-radio-group>
          </div>
        </div>
        <div class="dialogMain-right">
          <div class="topInfo">
            <div class="topTitle">
              <div class="colorBlock"></div>
              {{rightTitle}}
            </div>
            <el-checkbox v-model="checkAll" @change="handleCheckAllChange"
                         class="check-all">全选
            </el-checkbox>
          </div>
          <div class="selectBorder">
            <el-checkbox-group v-if="wordsList.length > 0" v-model="nowCheckedWords"
                               @change="handleCheckedChange">
              <el-checkbox v-for="item,index in wordsList" :label="item" :key="item+index"
                           border>{{item}}
              </el-checkbox>
            </el-checkbox-group>
            <p v-else>暂无数据</p>
          </div>
        </div>
      </div>

      <template #footer>
        <div style="text-align: center;">
          <el-button type="primary" :loading="submitLoading" @click="submitSendMsgForm">确定</el-button>
          <el-button @click="cancelDialog">取消</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import {selectWordLibTreeApi, areaWordLibTreeApi} from "@/api/publicOpinionMonitor/index.js";

export default {
  name: 'lexiconDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    lexiconType: {
      type: String,
      default: 'kw1'
    },
  },
  watch: {
    visible(newValue) {
      this.dialogVisible = newValue; // 确保当父组件的值变化时，子组件也同步更新
      if (newValue) {
        this.getWordsList()
      }
    },

    checkedClass(newValue) {
      // this.wordsList = newValue ? newValue.split(' ') : []
      // this.$nextTick(() => [
      //     this.handleCheckedChange(this.nowCheckedWords)
      // ])
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      submitLoading: false,
      checkedClass: '',
      classList: [],
      wordsList: [],

      searchWord: '',
      openedCollapse: [],//展开的折叠面板

      checkAll: false,

      nowCheckedWords: [],

      rightTitle: ''
    }
  },
  methods: {
    // 同步Visible到父组件
    updateVisible() {
      this.$emit('visibleChange', this.dialogVisible);
    },
    // 提交表单
    submitSendMsgForm() {
      this.$emit('submit', this.lexiconType, this.nowCheckedWords)
      this.cancelDialog()
    },
    cancelDialog() {
      this.dialogVisible = false
      this.checkedClass = ''
      this.rightTitle = ''
      this.wordsList = []
      this.searchWord = ''
      this.nowCheckedWords = []
      this.updateVisible()
    },
    getWordsList() {
      let param = {
        state: 0,
        name: this.searchWord,
      }

      let queryApi = selectWordLibTreeApi

      if (this.lexiconType == 'kw1') {
        queryApi = areaWordLibTreeApi
      }
      queryApi(param).then(res => {
        this.classList = res.data

        //展开所有伸缩栏
        this.openedCollapse = res.data.map(item => item.type)

        //默认选中第一个
        if (res.data?.length > 0 && res.data[0]?.child.length > 0) {
          this.checkedClass = res.data[0].child[0].name + res.data[0].child[0].word
          // this.rightTitle = res.data[0].child[0].name

          let item = {
            name: res.data[0].child[0].name,
            word: res.data[0].child[0].word
          }
          this.clickClassRadio('', item)
        }
      })
    },

    handleCheckAllChange(value) {
      let choseList = this.wordsList
      // this.nowCheckedWords = value ? choseList : [];
      this.nowCheckedWords = value
        ? [...new Set([...this.nowCheckedWords, ...choseList])] // 合并并去重
        : this.nowCheckedWords.filter(word => !choseList.includes(word)); // 移除nowCheckedWords中choseList存在的元素
    },
    handleCheckedChange(value) {
      // 将wordsList转为Set以便高效查找
      const wordsListSet = new Set(this.wordsList);
      // 检查wordsList中的每个元素是否都在value中
      const isAllIncluded = [...wordsListSet].every(item => value.includes(item));
      // 根据检查结果更新checkAll
      this.checkAll = isAllIncluded;
    },

    clickClassRadio(el, item) {
      // 当是input标签触发的点击事件时，阻止该事件
      if (el?.target?.tagName === 'INPUT') return
      this.rightTitle = item.name

      this.wordsList = item?.word ? item.word.split(' ') : []
      this.$nextTick(() => [
        this.handleCheckedChange(this.nowCheckedWords)
      ])
    }
  },
}
</script>

<style lang="scss" scoped>
.lexiconDialog {
  ::v-deep {
    .el-dialog__body {
      padding: 10px 20px;
    }
  }
}

.dialogMain {
  display: flex;
  flex-direction: row;
  padding: 0px 20px;

  .dialogMain-left {
    flex: 1;
    margin-right: 20px;

    ::v-deep {

      .el-collapse-item__wrap,
      .el-collapse,
      .el-collapse-item__header {
        border-block-color: transparent;
      }

      .el-radio__input {
        display: none !important;
      }

      .el-radio {
        margin: 0 5px 5px 0 !important;
      }

      .el-input-group__append {
        background-color: #FFFFFF;
        color: #666666;
      }
    }

  }

  .dialogMain-right {
    flex: 1;
  }

  .topInfo {
    width: 100%;
    height: 40px;
    line-height: 40px;
    display: flex;
    justify-content: space-between;

    > div {
      white-space: nowrap;
    }

    .topTitle {
      font-family: PingFangSC, PingFang SC;
      font-size: 16px;
      color: #333333;
    }

    .localIcon {
      height: 16px;
      vertical-align: text-bottom;
    }
  }

  .selectBorder {
    width: 100%;
    height: 60vh;
    overflow-y: auto;
    border: 1px solid #EFEFEF;
    padding: 20px;

    ::v-deep {
      .el-checkbox__input {
        display: none !important;
      }

      .el-checkbox {
        margin: 0 5px 5px 0 !important;
      }
    }
  }

  .colorBlock {
    width: 5px;
    height: 20px;
    background-color: #247CFF;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
  }
}
</style>
