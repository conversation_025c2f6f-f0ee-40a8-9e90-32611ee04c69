<template>
  <div style="height: 100%">
    <div class="topOption">
      <div class="tabAndButton">

        <div class="customTab">
          <el-tabs v-model="timeTab" class="top-tabs" @tab-click="handleClick">
            <el-tab-pane label="当前方案" name="0">
              <div/>
            </el-tab-pane>
            <el-tab-pane label="历史事件" name="1">
              <div/>
            </el-tab-pane>
          </el-tabs>
        </div>

        <div>
          <img src="@/assets/images/searchIcon.png" alt="" class="topbutton" @click="switchSearch">
          <el-dropdown>
            <img src="@/assets/images/addPlanIcon.png" alt="" class="topbutton">
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="() => addTask('添加方案')">添加方案</el-dropdown-item>
              <el-dropdown-item @click.native="
                      {
                        resetClassForm();
                        dialogTaskClass = true;
                        dialogClassTitle = '添加方案分类';
                    }
                        ">添加方案分类
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </div>

      <div v-show="isShowSearch">
        <el-input class="topSearch" v-model.trim="inputvalue" clearable placeholder="" style="width: 100%">
          <!-- <el-input class="topSearch" v-model.trim="inputvalue" clearable placeholder="" style="width: 100%" @clear="getTreeData"> -->
          <template #append>
            <!-- <el-button @click="getTreeData">搜索</el-button> -->
            <el-button>搜索</el-button>
          </template>
        </el-input>
      </div>


    </div>

    <div v-loading="searchLoading" class="dataTree">

      <el-tree ref="cusTreeRef" :data="treeDataauto" node-key="id" check-on-click-node :props="defaultProps"
               @check="handleCheckChangeCus" :draggable="timeTab == '0'" :allow-drag="allowDrag"
               :allow-drop="allowDrop" @node-drag-end="handleNodeDragEnd" :filter-node-method="filterNode">
        <template #default="{ node, data }">
                    <span
                      :class="`custom-tree-node ${node.level == 2 && data.id == checkedNode.id ? 'activeNode' : ''}`"
                      style="width: 100%">
                        <el-tooltip effect="light" placement="top" :open-delay="1000" :content="node.label">
                            <span class="treeitemTitle">
                                <img class="trackIcon" v-show="data.tracking == 1" src="@/assets/images/trackIcon.png"
                                     alt="">
                                <span v-show="node.level != 1" style="margin-right: 0.5em;">{{data.index}}.</span>
                                <span class="treeitemTitle-main">{{ node.label }}</span>
                            </span>
                        </el-tooltip>
                        <el-dropdown style="float: right">
                            <span class="el-dropdown-link">
                                <img class="gearIcon" src="@/assets/images/gear.svg" alt=""
                                     @click.stop="clickGear(node, data)"/>
                            </span>

                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item v-if="node.level == 1 && data.id != '-1'"
                                                  @click.native="editTaskClass(data)">重命名</el-dropdown-item>
                                <el-dropdown-item v-if="node.level == 2 && timeTab == '0'"
                                                  @click.native="editTask(data, '修改方案')">修改方案</el-dropdown-item>


                                <el-dropdown-item v-if="node.level == 2 && timeTab == '0'"
                                                  @click.native="markKeyFocus(data,'1')">{{`${!isInclude(data.planMain,'1')?'转移到':'取消'}重点事件`}}</el-dropdown-item>

                                <el-dropdown-item v-if="node.level == 2 && timeTab == '0'"
                                                  @click.native="markKeyFocus(data,'2')">{{`${!isInclude(data.planMain,'2')?'标记为':'取消'}全省热点事件`}}</el-dropdown-item>
                                <el-dropdown-item v-if="node.level == 2 && timeTab == '0'"
                                                  @click.native="markKeyFocus(data,'3')">{{`${!isInclude(data.planMain,'3')?'标记为':'取消'}全国热点事件`}}</el-dropdown-item>




                                <el-dropdown-item v-if="node.level == 2 && timeTab == '0' && data.fatherid != '-1'"
                                                  @click.native="addToOther(data.id)">加入其他方案</el-dropdown-item>

                                <el-dropdown-item v-if="node.level == 2 && timeTab == '0' && data.id == checkedNode.id"
                                                  @click.native="addToHistory">加入历史事件</el-dropdown-item>
                              <!-- <el-dropdown-item v-if="node.level == 2 && timeTab == '1'"
                                  @click.native="addToTime(data.id,'0')">加入当前方案</el-dropdown-item> -->
                                <el-dropdown-item v-if="data.id!='-1'"
                                                  @click.native="deleteTask(data.id, node.level == 1 ? 'type' : 'task')">
                                    删除
                                </el-dropdown-item>
                                <el-dropdown-item v-if=" data.id != '-1'"
                                  @click.native="changeSort(data.id, '1', data.fatherid || '', node.level == 1 ? 'type' : 'task')">
                                    上移
                                </el-dropdown-item>
                                <el-dropdown-item v-if=" data.id != '-1'"
                                  @click.native="changeSort(data.id, '2', data.fatherid || '', node.level == 1 ? 'type' : 'task')">
                                    下移
                                </el-dropdown-item>
                                <el-dropdown-item v-if=" data.id != '-1'"
                                  @click.native="changeSort(data.id, '3', data.fatherid || '', node.level == 1 ? 'type' : 'task')">
                                    移动到顶部
                                </el-dropdown-item>
                                <el-dropdown-item
                                  @click.native="changeSort(data.id, '4', data.fatherid || '', node.level == 1 ? 'type' : 'task')">
                                    移动到底部
                                </el-dropdown-item>

                                <el-dropdown-item v-if="node.level == 2 && timeTab == '1'"
                                                  @click.native="goTracking(data.id)">
                                    事件追踪
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </span>
        </template>
      </el-tree>


    </div>
    <div class="addTaskButton" @click="() => addTask('添加方案')">创建监测方案</div>
    <!-- <AddTask ref="addProgramRef" :dialog-visible="addTaskDialog" :title="dialogTaskTitle" :foot-type="dialogTaskTitle == '添加下发任务' || dialogTaskTitle == '修改下发任务'
                        ? 'distribute'
                        : 'form'
                    " @dialogClose="onCloseDialog" /> -->

    <el-dialog :visible.sync="addTaskDialog" :title="dialogTaskTitle" width="70%">
      <addProgram ref="addProgramRef"></addProgram>

      <template #footer>
        <div style="text-align: center;">
                    <span class="dialog-footer">
                        <el-button type="primary" :loading="submitLoading" @click="submitForm">确定</el-button>
                        <el-button @click="closeAddTaskDialog">取消</el-button>
                    </span>
        </div>

      </template>
    </el-dialog>

    <!-- 新增/修改方案分类弹窗 -->
    <el-dialog :visible.sync="dialogTaskClass" :title="dialogClassTitle" width="30%">
      <el-form ref="classForm" :model="taskClassForm" label-width="120px" @submit.prevent>
        <el-form-item label="方案分类：" prop="typeName" :rules="[{ required: true, message: '请输入方案分类', trigger: 'blur' },
                    { max: 20, message: '方案分类长度不能超过20个字符', trigger: 'blur' }]">
          <el-input v-model.trim="taskClassForm.typeName"/>
        </el-form-item>
      </el-form>
      <template #footer>
                <span class="dialog-footer">
                    <el-button type="primary" :loading="submitClassLoading" @click="submitClassForm">确定</el-button>
                    <el-button @click="dialogTaskClass = false">取消</el-button>
                </span>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import {
  changeTypeSort,
  changeTaskSort,
  addTaskType,
  putTaskType,
  delTaskType,
  delTask,
  getCustomDetails,
  getTreeDataApi,
  addPlanApi,
  updateMark,
  updatePlanApi,
  movePlanApi,
  dragPlanApi,
  trackingApi
} from "@/api/publicOpinionMonitor/index.js";

import addProgram from '../addProgram/index.vue'

export default {
  components: {
    addProgram
  },
  data() {
    return {
      tabList: [{
        label: '当前方案',
        name: '1'
      }, {
        label: '历史事件',
        name: '2'
      }],
      timeTab: '0',
      isShowSearch: true,//搜索框的显隐


      defaultProps: {
        children: 'children',
        label: 'name'
      },
      inputvalue: '',
      searchLoading: false,//搜索按钮loading
      submitLoading: false, //方案提交按钮loading
      submitClassLoading: false, //方案分类提交按钮loading
      // treeData: [],
      treeDataauto: [],
      // treeDataDistribute: [],//下发任务树
      dialogTaskClass: false,
      dialogClassTitle: '添加方案分类',
      addTaskDialog: false,
      dialogTaskTitle: '添加方案',


      taskClassForm: {//方案分类表单内容
        typeName: ''
      },


      // checkedChild: {//树选中的子节点
      //     customPlanIdList: [],
      //     systemPlanIdList: [],
      //     distributePlanIdList: []
      // },

      checkedNode: {},//树选中的子节点(单选)

    }
  },
  async mounted() {
    if (this.$route.query?.planId) {
      this.timeTab = this.$route.query?.historyFlag
      let checkNode = {
        id: this.$route.query?.planId,
        name: this.$route.query?.name,
        planMain: this.$route.query?.planMain ? JSON.parse(this.$route.query?.planMain) : '',
        expire: this.$route.query?.expire,
        historyFlag: this.$route.query?.historyFlag,
        children: null
      }
      // this.handleCheckChangeCus(checkNode)
      this.checkedNode = checkNode
      this.$emit('checkNode', checkNode)
      await this.getTreeData('route')

      //判断this.$route.query?.planId是否在其他文件夹中，以此来判断是否打开其他方案
      if (this.$refs['cusTreeRef'].store.nodesMap['-1'] && this.$refs['cusTreeRef'].store.nodesMap['-1'].childNodes.some(item => item.data.id === this.$route.query?.planId)) {
        this.$refs['cusTreeRef'].store.nodesMap['-1'].expanded = true;
      }
    } else {
      await this.getTreeData()
    }

    let datailOption = sessionStorage.getItem("datailOption")
    console.log(datailOption, 'datailOption');
    if (datailOption) {
      this.addTaskDialog = true
      this.dialogTaskTitle = '添加方案'
      this.$nextTick(() => {
        let addProgramRef = this.$refs['addProgramRef']
        let formdata = {...addProgramRef.taskForm, ...JSON.parse(sessionStorage.getItem("datailOption"))}

        addProgramRef.searchMode = String(formdata.searchMode)
        addProgramRef.taskForm = formdata

        // let formdata = res.data
        // formdata.area = formdata.area?formdata.area.split(','):[]
        // formdata.highArea = formdata.highArea?formdata.highArea.split(','):[]
        // this.$refs['addProgramRef'].searchMode = String(formdata.searchMode)
        // this.$refs['addProgramRef'].taskForm = formdata
        addProgramRef.setTypeName()

        sessionStorage.removeItem("datailOption")
      })
    }
  },
  watch: {
    inputvalue(val) {
      this.$refs['cusTreeRef'].filter(val);
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    // 处理数据，添加序号
    addIndex(nodes, level = 1) {
      nodes.forEach((node, index) => {
        this.$set(node, 'index', index + 1);
        if (node.children && node.children.length > 0) {
          this.addIndex(node.children, level + 1);
        }
      });
    },
    isInclude(array, who) {
      if (!Array.isArray(array)) return false;

      for (let i = 0; i < array.length; i++) {
        if (array[i].mainType === who) {
          return true;
        }
      }

      return false;
    },
    handleClick(tab) {
      this.timeTab = tab.name
      // 注释掉，因为加入历史案件操作后会无法再选中案件
      // if(this.$route.query?.historyFlag&&tab.name==this.$route.query?.historyFlag){
      //   this.getTreeData('route')
      //   let checkNode = {
      //     id: this.$route.query?.planId,
      //     name: this.$route.query?.name,
      //     planMain: this.$route.query?.planMain ? JSON.parse(this.$route.query?.planMain) : '',
      //     expire: this.$route.query?.expire,
      //     historyFlag: this.$route.query?.historyFlag,
      //     children: null
      //   }
      //   this.checkedNode = checkNode
      //   this.$emit('checkNode', checkNode)
      // }else{
        this.getTreeData()
      // }
    },
    switchSearch() {
      this.isShowSearch = !this.isShowSearch
      this.inputvalue = ''
    },
    // //关闭方案弹框
    // onCloseDialog(data) {
    //     this.addTaskDialog = false
    //     if (data.type == 'reset') {
    //         this.getTreeData()
    //         this.$emit('checkNode', {
    //             customPlanIdList: [],
    //             systemPlanIdList: [],
    //             distributePlanIdList: []
    //         })
    //     }
    // },
    //方案选中树节点
    handleCheckChangeCus(data, checked, indeterminate) {
      // console.log(data, checked, indeterminate,'data, checked, indeterminate')
      // const checkedChildNodes = checked.checkedNodes
      //     .filter((obj) => obj.children === null)
      //     .map((obj) => obj.id)
      // this.checkedChild.customPlanIdList = checkedChildNodes
      // this.$emit('checkNode', this.checkedChild)


      if (data.children === null) {
        //原页面切换
        // this.checkedNode = data
        // this.$emit('checkNode', data)

        //更换成 跳转新页面
        const planRoute = this.$router.resolve({
          path: '/publicOpinionMonitor',
          query: {
            name: data.name,
            planId: data.id,
            historyFlag: data.historyFlag,
            expire: data.expire,
            planMain: data.planMain ? JSON.stringify(data.planMain) : '',
          }
        })
        window.open(planRoute.href, '_blank')
      }
    },


    // 修改分类按钮
    editTaskClass(item) {
      console.log(item, 'item')
      this.dialogTaskClass = true
      this.dialogClassTitle = '修改方案分类'
      this.taskClassForm.typeName = item.name
      this.taskClassForm.typeId = item.id
    },

    // 添加/修改方案分类-提交表单
    submitClassForm() {
      this.$refs['classForm'].validate((valid) => {
        if (valid) {
          this.submitClassLoading = true
          if (this.dialogClassTitle[0] == '修') {
            putTaskType(this.taskClassForm).then((res) => {
              this.submitClassLoading = false
              this.dialogTaskClass = false
              this.getTreeData()


              this.$refs['addProgramRef'].TypeLists()
              this.msgSuccess("修改成功");
            }).catch((err) => {
              this.submitClassLoading = false
            })
          } else {
            this.taskClassForm.historyFlag = this.timeTab
            addTaskType(this.taskClassForm).then((res) => {
              this.submitClassLoading = false
              this.dialogTaskClass = false
              this.getTreeData()
              this.$refs['addProgramRef'].TypeLists()
              this.msgSuccess("新增成功");
            }).catch((err) => {
              this.submitClassLoading = false
            })
          }
        }
      })
    },

    // 表单重置
    resetClassForm() {
      this.taskClassForm = {
        typeName: ''
      }
      this.$nextTick(() => {
        this.$refs['classForm'].resetFields()
      })
    },


    // 添加任务按钮
    addTask(title) {
      this.addTaskDialog = true
      this.dialogTaskTitle = title
      this.$nextTick(() => {
        this.$refs['addProgramRef'].reset()
      })
    },
    // 关闭添加任务弹窗
    closeAddTaskDialog() {
      this.addTaskDialog = false
    },

    // 添加方案-提交表单
    submitForm() {
      this.$refs['addProgramRef'].$refs['form'].validate((valid) => {
        if (valid) {
          let taskForm = JSON.parse(JSON.stringify(this.$refs['addProgramRef'].taskForm))
          let searchMode = JSON.parse(JSON.stringify(this.$refs['addProgramRef'].searchMode))

          //高级模式-监测关键词去除符号后的长度
          let highMonitorWordLength = taskForm?.highMonitorWord?.replace(/[+|\(|\)|]/g, '')?.length || 0

          //至少一个关键词
          if ((searchMode == '0' && !taskForm.kw1 && !taskForm.kw2 && !taskForm.kw3) || (searchMode == '1' && !highMonitorWordLength)) {
            this.$message({
              message: '请输入至少一个监测关键词',
              type: 'warning'
            })
            return
          }
          let params = taskForm
          params.area = taskForm.area?.join(',')
          params.highArea = taskForm.highArea?.join(',')
          params.searchMode = searchMode

          //提交时同步转化快速模式和高级模式
          if (searchMode == '0') {
            params.highArea = params.area
            params.highExcludeWord = params.excludeWord || ''

            // 确保kw1、kw2、kw3存在且不为空字符串再进行处理
            const kw1Processed = params.kw1 ? `(${params.kw1.replace(/\s/g, '|')})` : '';
            const kw2Processed = params.kw2 ? `(${params.kw2.replace(/\s/g, '|')})` : '';
            const kw3Processed = params.kw3 ? `(${params.kw3.replace(/\s/g, '|')})` : '';
            // 根据处理后的变量拼接最终的highMonitorWord，这样可以避免多余的'+('或')'
            params.highMonitorWord = `${kw1Processed ? kw1Processed + '+' : ''}${kw2Processed ? kw2Processed + '+' : ''}${kw3Processed ? kw3Processed + '+' : ''}`.replace(/\+$/, ''); // 移除开头可能存在的多余 '+' 符号
          } else {
            params.area = params.highArea
            params.excludeWord = params.highExcludeWord || ''
            params.kw1 = ''
            params.kw2 = ''
            params.kw3 = ''
          }
          console.log('params', params)

          this.submitLoading = true
          addPlanApi(params).then((res) => {
            this.submitLoading = false
            this.$message({
              message: '提交成功',
              type: 'success'
            })
            this.closeAddTaskDialog()
            setTimeout(() => {
              this.getTreeData()
            }, 500)
          }).catch((err) => {
            this.submitLoading = false
          })

        } else {
          this.$message({
            message: '表单校验未通过',
            type: 'error'
          })
        }
      })
    },

    // 修改任务按钮
    editTask(item, title) {
      // this.addTaskDialog = true
      // this.dialogTaskTitle = title
      // this.$nextTick(() => {
      //     this.$refs['addProgramRef'].reset()
      // })
      // const id = item.id
      // getCustomDetails(id).then((res) => {
      //     const taskForm = res.data
      //     // this.taskForm.searchPosition = String(taskForm.searchPosition)
      //     // this.$refs['addProgramRef'].taskForm = taskForm
      //     // this.$refs['addProgramRef'].keywords = taskForm.kwMap
      //     // this.$refs['addProgramRef'].statuss.searchMode = String(taskForm.searchMode)
      // })


      //打开侧边tab形式
      this.checkedNode = item
      this.$emit('checkNode', item)
      this.$nextTick(() => {
        this.$parent.activeName = '3'
      })
    },

    // 删除方案/方案分类
    deleteTask(id, who) {
      this.$confirm(`确认删除选中方案${who == 'type' ? '分类' : ''}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.searchLoading = true
        if (who == 'type') {
          delTaskType({typeId: id}).then((res) => {
            this.$message({message: '删除成功', type: 'success'})
            setTimeout(() => {
              this.getTreeData()
            }, 500)
          }).catch((err) => {
            this.searchLoading = false
            console.log('err', err)
          })
        } else {
          updatePlanApi({planId: id, delFlag: 1}).then((res) => {
            this.$message({message: '删除成功', type: 'success'})
            setTimeout(() => {
              this.getTreeData()
            }, 500)
          }).catch((err) => {
            this.searchLoading = false
            console.log('err', err)
          })
        }
      })
    },

    changeSort(id, sortType, fatherid, who) {
      this.searchLoading = true
      if (who == 'type') {
        changeTypeSort({typeId: id, sortType}).then((res) => {
          this.getTreeData()
          this.msgSuccess("修改成功");
        }).catch((err) => {
          this.searchLoading = false
        })
      } else {
        changeTaskSort({planId: id, typeId: fatherid, sortType}).then((res) => {
          this.getTreeData()
          this.msgSuccess("修改成功");
        }).catch((err) => {
          this.searchLoading = false
        })
      }
    },
    // 重点关注事件
    markKeyFocus(data, mainType) {

      let params = {
        planId: data.id,
        mainType: mainType,
        delFlag: this.isInclude(data.planMain, mainType) ? 1 : 0
      }
      this.searchLoading = true
      updateMark(params).then((res) => {
        this.getTreeData();
        this.msgSuccess(res.msg);
      }).catch((err) => {
        this.searchLoading = false
      })
    },

    addToHistory() {
      this.$parent.activeName = '1'
      this.$parent.$refs['opinionDataRef'].transTimeRange()
    },
    // 添加到历史/当前列表
    addToTime(id, historyFlag, planName) {


      //新增需求：添加前，要先弹窗口，确认时间段，将时间段通过保存条件接口保存

      //思路：在opinionData中写一个方法，用来弹出弹框，翻译时间段，
      //确认按钮:保存条件接口保存（保存时间段，不保存timeIndex，处理好存储的timeIndex）


      updatePlanApi({planId: id, historyFlag, planName}).then((res) => {
        // this.$message({ message: '添加成功', type: 'success' })
        this.$parent.$refs['opinionDataRef'].toHistoryVisible = false
        setTimeout(() => {
          this.getTreeData()
        }, 500)
      }).catch((err) => {
        console.log('err', err)
      })
    },
    // 添加到其他方案文件夹
    addToOther(id) {
      movePlanApi({planId: id, typeId: '-1'}).then((res) => {
        this.$message({message: '添加成功', type: 'success'})
        setTimeout(() => {
          this.getTreeData()
        }, 500)
      }).catch((err) => {
        console.log('err', err)
      })
    },


    // 获取方案树数据
    async getTreeData(type) {
      this.searchLoading = true
      let params = {
        // planName: this.inputvalue,
        historyFlag: this.timeTab
      }
      // console.log('params', params)

      let resauto = await getTreeDataApi(params)

      if (resauto.code == 200) {
        const newdata = resauto.data.map((item) => {
          if (!item.children || item.children.length == 0) {
            item.disabled = true
          }
          return item
        })
        newdata.forEach(item => {
          if (item.children) {
            item.children.forEach(child => {
              child.fatherid = item.id
            })
          }
        })
        // console.log('newdata', newdata)
        this.treeDataauto = newdata

        this.addIndex(this.treeDataauto);
      }

      let allChild = this.getAllChilds(resauto.data)

      //添加需求：typeId==-1的其他分类，默认不展开
      // allChild = allChild.filter(child => child.fatherid != '-1');

      //判断this.$route.query?.planId是否在其他文件夹中，以此来判断是否打开其他方案
      this.$nextTick(() => {
        if(this.$refs['cusTreeRef'].store.nodesMap['-1'] && this.$refs['cusTreeRef'].store.nodesMap['-1'].childNodes.some(item => item.data.id === this.$route.query?.planId)){

        }else{
          allChild = allChild.filter(child => child.fatherid != '-1');
        }
      })

      if (allChild.length > 0) {
        // if (type == 'route') {//跳转至该页面，带planId参数时，不默认选中第一个,打开所有文件夹
        if (false) {//跳转至该页面，带planId参数时，不默认选中第一个,打开所有文件夹
          this.$nextTick(() => {
            allChild.forEach(node => {
              this.$refs['cusTreeRef'].store.nodesMap[node.fatherid].expanded = true;
            })
          })
        } else {
          const matchingChild = allChild.find(child => child.id === this.checkedNode.id);
          if (matchingChild) {//当前选中的节点在新获取的tree中存在匹配的子节点
            this.checkedNode = matchingChild
            this.$emit('checkNode', matchingChild)
            this.$nextTick(() => {
              // //判断是否存在指定节点
              // if (this.$refs['cusTreeRef'].getNode(matchingChild.fatherid)) {
              //     //若存在指定节点，则设置当前节点为指定节点（用于高亮显示当前节点）
              //     //展开指定节点
              //     this.$refs['cusTreeRef'].store.nodesMap[matchingChild.fatherid].expanded = true;
              // } else {
              //     //若不存在指定节点，则设置当前节点为null（清除当前节点的高亮效果）
              //     this.$refs['cusTreeRef'].setCurrentKey()
              // }

              //新需求需要全部打开节点
              allChild.forEach(node => {
                this.$refs['cusTreeRef'].store.nodesMap[node.fatherid].expanded = true;
              })
            })
          } else {// 默认选中第一个
            this.checkedNode = allChild[0]
            this.$emit('checkNode', allChild[0])
            this.$nextTick(() => {
              // this.$refs['cusTreeRef'].setCurrentKey(allChild[0].id, true) //失效

              // //判断是否存在指定节点
              // if (this.$refs['cusTreeRef'].getNode(allChild[0].fatherid)) {
              //     //若存在指定节点，则设置当前节点为指定节点（用于高亮显示当前节点）
              //     //展开指定节点
              //     this.$refs['cusTreeRef'].store.nodesMap[allChild[0].fatherid].expanded = true;
              // } else {
              //     //若不存在指定节点，则设置当前节点为null（清除当前节点的高亮效果）
              //     this.$refs['cusTreeRef'].setCurrentKey()
              // }


              //新需求需要全部打开节点
              allChild.forEach(node => {
                this.$refs['cusTreeRef'].store.nodesMap[node.fatherid].expanded = true;
              })
            })
          }
        }
        //有筛选词时，自动展开搜索结果
        if (this.inputvalue) {
          this.$nextTick(() => {
            allChild.forEach(node => {
              this.$refs['cusTreeRef'].store.nodesMap[node.fatherid].expanded = true;
            })
          })
        }
      } else {
        this.$emit('checkNode', {})
      }

      this.searchLoading = false
    },
    // //获取所有子节点id
    // getAllChildIds(data) {
    //     const childIds = []
    //     data.forEach(parent => {
    //         parent.children?.forEach(child => {
    //             childIds.push(child.id)
    //         })
    //     })
    //     return childIds
    // },
    //获取所有子节点
    getAllChilds(data) {
      const childs = []
      if (data && data.length > 0) {
        data.forEach(parent => {
          parent.children?.forEach(child => {
            childs.push(child)
          })
        })
      }
      return childs
    },

    clickGear(node, data) {
      console.log(node, data)
    },

    //重置树
    resetTree() {
      this.$refs['cusTreeRef']?.setCheckedKeys([], false)
    },
    //设置选中的树节点
    setTreeNodes(sysNodes, cusNodes, disNodes) {
      cusNodes && this.$refs['cusTreeRef']?.setCheckedKeys(cusNodes)
    },

    allowDrag(draggingNode) {
      console.log('draggingNode', draggingNode)
      return draggingNode.level !== 1;
    },
    allowDrop(draggingNode, dropNode, type) {
      // type 参数有三种情况：'prev'、'inner' 和 'next'，分别表示放置在目标节点前、插入至目标节点和放置在目标节点后
      // 禁止拖拽到内部
      if (type === 'inner') {
        return false
      }

      // 判断是否是同一父节点,禁止跨文件夹拖拽
      if (draggingNode.parent !== dropNode.parent) {
        return false
      }
      return true;
    },
    handleNodeDragEnd(draggingNode, dropNode, dropType, ev) {
      // 如果是同一节点，则不处理
      if (draggingNode == dropNode) {
        return
      }

      let params = {
        originPlanId: draggingNode.data.id, //当前方案id
        planId: dropNode.data.id, //拖动至目标方案的id
        position: dropType == 'after' ? 0 : 1, //处于目标方案的位置  1是上面(before)  0是下面(after)
        typeId: draggingNode.data.fatherid, //文件夹id
        historyFlag: draggingNode.data.historyFlag //历史方案： 0 不是 1 是
      }
      dragPlanApi(params).then(res => {
        if (res.code == 200) {
          this.$message.success('移动成功')
        }
      })

      // 在拖拽结束时重新编号
      this.$nextTick(() => {
        this.addIndex(this.treeDataauto);
      });
    },
    // 事件追踪方法
    goTracking(id) {
      trackingApi(id).then(res => {
        if (res.code == 200) {
          this.checkedNode.id = res.msg
          this.timeTab = '0'
          setTimeout(() => {
            this.getTreeData()
          }, 500)
          this.$message.success('追踪成功')
        }
      })
    },
  }
}
</script>


<style lang="scss" scoped>
.customTab {
  vertical-align: middle;

  .tabItem {
    display: inline-block;
    vertical-align: middle;

    font-weight: 400;
    font-size: 16px;
    color: #666666;
    margin-left: 35px;
    cursor: pointer;
    border-bottom: 4px solid transparent;
    padding-bottom: 8px;
  }

  :first-child {
    margin-left: 0;
  }

  > .active {
    font-weight: 500;
    color: #000000;
    border-bottom: 4px solid #247CFF;
  }


  ::v-deep .el-tabs__header {
    margin: 0 0 0px;

    .el-tabs__nav-wrap::after {
      height: 0;
    }

    .el-tabs__item {
      height: 35px;
      font-size: 16px;
      color: #666666;
      line-height: 25px;
    }

    .el-tabs__item.is-active {
      color: #000;
    }

    .el-tabs__header {
      padding: 16px 0 0 16px;
      background: #fff;
    }

    .el-tabs__active-bar {
      height: 6px;
      border-radius: 3px;
      background-color: #247CFF;
    }
  }
}


.topOption {
  width: 100%;
  overflow: hidden;
  margin-bottom: 20px;

  .tabAndButton {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid #DCDFE6;
    padding-bottom: 5px;
    margin-bottom: 8px;

    .topbutton {
      width: 28px;
      height: 28px;
      margin-left: 8px;
      cursor: pointer;
    }
  }

  .topSearch {
    ::v-deep .el-input-group__append {
      color: #fff;
      background: #247CFF;
      border: none;
    }
  }

}

.dataTree {
  // height: 640px;
  height: calc(100% - 127px);
  overflow-y: auto;
  position: relative;

  .activeNode {
    color: #409eff !important;

    .treeitemTitle-main {
      color: #409eff !important;
    }
  }

  .custom-tree-node {
    width: 100%;
    position: relative;

    .treeitemTitle-main {
      color: #000000;
    }

    .gearIcon {
      display: none;
    }

    .iconGroup {
      position: absolute;
      right: 212px;
      bottom: -2px;

      img {
        margin-right: 2px;
      }
    }

    .markIcon {
      height: 15px;
      display: inline-block;
      // vertical-align: middle;
      margin-right: 2px;
    }
  }

  .custom-tree-node:hover {
    .gearIcon {
      display: inline;
      width: 13px;
      position: relative;
      top: 2px;
    }
  }

  p {
    height: 40px;
    line-height: 40px;
    background: #EBEFF4;
    font-weight: bolder;
    padding-left: 20px;
  }

  ::v-deep .el-tree {
    .el-tree-node:hover {
      // background-color:#F5F7FA;
    }

    .el-dropdown {
      font-size: 12px;
    }

    .el-tree-node__content {
      // margin-left: 20px;
      height: 35px;
    }

    .is-expanded {
    }

    // is-leaf el-tree-node__expand-icon el-icon-caret-right
    .el-tree-node__expand-icon.is-leaf::before {
      content: url("~@/assets/images/closedFolder.png") !important;
    }

    .el-tree-node__children {
      .el-tree-node__expand-icon.is-leaf::before {
        content: '' !important;
      }
    }

    .el-icon-caret-right::before {
      content: url("~@/assets/images/closedFolder.png");
    }

    .el-tree-node__expand-icon.expanded::before {
      content: url("~@/assets/images/openedFolder.png");
    }

    .el-tree-node__expand-icon.expanded {
      -webkit-transform: none;
      transform: none
    }

    .el-tree-node__expand-icon.is-leaf::before {
      content: '';
    }
  }
}

.addTaskButton {
  text-align: center;
  padding: 6px 0;
  margin: 0 auto;
  cursor: pointer;

  width: 148px;
  background: #247CFF;
  border-radius: 2px;
  font-weight: 400;
  font-size: 14px;
  color: #FFFFFF;

  &:hover {
    border: 1px solid #409eff;
    color: #409eff;
  }
}

.icon:hover {
  color: #409eff;
}

.treeitemIcon {
  width: 17px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 2px;
}

.treeitemTitle {
  display: inline-block;
  vertical-align: middle;
  width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;

  .trackIcon {
    width: 13px;
    position: absolute;
    top: 3px;
    left: -20px;
  }
}
</style>
