<template>
  <el-dialog :visible.sync="contactsDialog" :title="contactsDialogTitle" width="30%" @close="cancelContactsDialog">
    <el-form ref="contactsFormRef" :model="contactsForm" label-width="120px" :rules="rules" @submit.prevent>
      <el-form-item prop="id" v-show="false">
        <el-input v-model="contactsForm.id"/>
      </el-form-item>
      <el-form-item label="姓名：" prop="username">
        <el-input v-model.trim="contactsForm.username"/>
      </el-form-item>
      <el-form-item label="邮箱：" prop="email">
        <el-input v-model.trim="contactsForm.email"/>
      </el-form-item>
      <el-form-item label="手机：" prop="phone">
        <el-input v-model.trim="contactsForm.phone"/>
      </el-form-item>
      <el-form-item label="微信号：" prop="wxUserName">
        <el-input v-model.trim="contactsForm.wxUserName" disabled/>
      </el-form-item>
      <div class="qrCode">
        <div class="qrcodeBox" v-loading="!wechatimg">
          <qrcode-vue v-if="wechatimg" :value="wechatimg" :size="130"/>
        </div>
        <div class="refreshButton" @click="refreshImg">
          <span>刷新</span>
          <img src="@/assets/images/refresh.svg" alt="">
        </div>
      </div>


    </el-form>
    <template #footer>
      <div style="text-align: center;">
        <el-button type="primary" :loading="contactsLoading" @click="submitUserForm">确定</el-button>
        <el-button @click="cancelContactsDialog">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>

import {addContactsApi, updataContactsApi, delContactsApi, getContactsApi, sendMsgApi} from "@/api/search/index";

import {
  getMaPicUrlApi,
  checkScanStatus,
} from "@/api/wechat";
import QrcodeVue from "qrcode.vue";

export default {
  components: {QrcodeVue},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    contactsOption: {
      type: String,
      default: 'add'
    },
    contactsRow: {
      type: Object,
      default: () => {
      }
    },
    type: {
      type: String,
      default: '0'
    },
  },
  watch: {
    visible(newValue) {
      this.contactsDialog = newValue; // 确保当父组件的值变化时，子组件也同步更新
      if (newValue) {
        this.getMaPic()//获取二维码
        this.$nextTick(() => {
          this.$refs.contactsFormRef.resetFields()
          if (this.contactsOption == 'add') {
            this.contactsDialogTitle = '新增联系人'
            this.contactsForm = {}
          } else {
            this.contactsDialogTitle = '修改联系人'
            this.contactsForm = JSON.parse(JSON.stringify(this.contactsRow))
          }
        })
      }
    },
  },
  data() {
    return {

      rules: {
        username: [
          {required: true, message: '请输入姓名', trigger: 'blur'},
        ],
        phone: [
          {validator: this.validatePhoneNumber, trigger: 'blur'}
        ],
        email: [
          {validator: this.validateEmail, trigger: 'blur'}
        ],
      },

      contactsDialog: this.visible,
      contactsList: [],
      contactsForm: {
        username: '',
        phone: '',
        email: '',
        wxUserName: '',
      },
      contactsLoading: false,
      selectedRows: [],
      contactsDialogTitle: '新增联系人',

      wechatimg: 'http://weixin.qq.com/q/0255VLY8wOc3F1AiH71C1j',
      wechatQRCodeId: null,
      timerTwo: null,
    }
  },
  created() {
    this.timerTwo = setInterval(() => {
      this.getWechatUse();
    }, 2000);
  },
  beforeDestroy() {
    //清除定时器
    clearInterval(this.timerTwo);
    this.timerTwo = null;
  },
  methods: {
    // 同步Visible到父组件
    updateVisible() {
      this.$emit('visibleChange', this.contactsDialog);
    },


    // 自定义手机号码验证函数
    validatePhoneNumber(rule, value, callback) {
      // 检查值是否为空，如果是，则直接通过验证
      if (!value) {
        callback();
      } else {
        const phonePattern = /^1[3-9]\d{9}$/;
        // 如果值不为空，则继续进行手机号码格式验证
        if (!phonePattern.test(value)) {
          callback(new Error('请输入正确的11位手机号码'));
        } else {
          callback();
        }
      }
    },
    // 自定义邮箱验证函数
    validateEmail(rule, value, callback) {
      // 检查值是否为空，如果是，则直接通过验证
      if (!value) {
        callback();
      } else {
        const emailPattern = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
        if (!emailPattern.test(value)) {
          callback(new Error('请输入正确的邮箱地址'));
        } else {
          callback();
        }
      }
    },


    submitUserForm() {
      this.$refs['contactsFormRef'].validate(valid => {
        if (valid) {
          let Api = addContactsApi
          if (this.contactsOption == 'edit') {
            Api = updataContactsApi
          }
          let params = {
            ...this.contactsForm,
            type: this.type
          }
          //根据type判断当前必填项是否填写
          if ((params.type == '0' && !params.phone) || (params.type == '1' && !params.email) || (params.type == '2' && !params.wxUserName)) {
            this.$message({
              type: 'warning',
              message: `未填写必填项-${params.type == '0' ? '手机号' : params.type == '1' ? '邮箱' : '微信号'}`
            })
            return
          }
          this.contactsLoading = true
          Api(params).then(res => {
            this.contactsLoading = false
            this.$message.success(this.contactsOption == 'edit' ? '修改成功' : '新增成功')
            this.cancelContactsDialog()
            //触发父级的成功回调
            // this.getContactsList()
            this.$emit('afterSubmit')
          }).catch(err => this.contactsLoading = false)
        }
      })
    },

    cancelContactsDialog() {
      this.contactsDialog = false
      this.updateVisible()
      // this.$nextTick(() => {
      //     this.contactsForm = {
      //         username: '',
      //         phone: ''
      //     }
      // })
    },


    // 获取二维码
    async getMaPic() {
      let params = {
        position: "USER_WARN",
      };
      let res = await getMaPicUrlApi(params);
      this.wechatimg = res.data.url;
      this.wechatQRCodeId = res.data.code;
    },
    // 获取微信是否已扫码
    async getWechatUse() {
      if (!this.contactsDialog || !this.wechatQRCodeId) return;
      let params = {
        code: this.wechatQRCodeId,
      };
      let res = await checkScanStatus(params);
      if (res.data.status == "BIND_SUCCESS") {
        // 已扫码

        if (res.data.userInfo) {
          this.contactsForm.wxUserName = res.data.userInfo.nickname
          this.contactsForm.wxOpenId = res.data.userInfo.openid
          this.contactsForm.headImgUrl = res.data.userInfo.headImgUrl
        }

        this.getMaPic();
        this.$message.success('微信绑定成功');
      } else {
        // this.$message.error(res.msg)
      }
    },
    refreshImg() {
      this.getMaPic();
    },
  },
}
</script>

<style lang="scss" scoped>
.qrCode {
  text-align: center;

  .qrcodeBox {
    width: 130px;
    margin: 0 auto;
  }

  .refreshButton {
    width: fit-content;
    margin: 0 auto;
    cursor: pointer;

    span,
    img {
      vertical-align: middle;
    }

    span {
      font-family: PingFangSC, PingFang SC;
      font-size: 14px;
      color: #247CFF;
      margin-right: 5px;
    }
  }
}
</style>
