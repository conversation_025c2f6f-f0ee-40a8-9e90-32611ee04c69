<template>
  <el-menu
    :default-active="activeMenu"
    background-color="background: linear-gradient( 180deg, #3485FF 0%, #0066FF 100%);"
    text-color="#fff"
    :unique-opened="true"
    active-text-color="#fff"
    :collapse-transition="false"
    mode="horizontal"
  >
    <sidebar-item
      v-for="(route, index) in sidebarRouters"
      :key="route.path + index"
      :item="route"
      :base-path="route.path"
    />
  </el-menu>
</template>

<script>
import {mapGetters, mapState} from "vuex";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.scss";

export default {
  components: {SidebarItem},
  computed: {
    ...mapState(["settings"]),
    ...mapGetters(["sidebarRouters", "sidebar"]),
    activeMenu() {
      const route = this.$route;
      const {meta, path} = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    },
    variables() {
      return variables;
    },
    menuView() {
      return this.$store.state.settings.menuView;
    },
  },
};
</script>
<style lang="scss">
</style>
