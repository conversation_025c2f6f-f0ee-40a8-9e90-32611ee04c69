import request from '@/utils/request'

// 获取所有方案
export function manageAllApi(obj) {
  return request({
    url: '/plan/manage/all',
    method: 'post',
    data: obj
  })
}


// 获取舆情预警列表
export function getWarnDataApi(obj) {
  return request({
    url: '/warn/data/get',
    method: 'post',
    data: obj
  })
}


// 舆情预警阅读
export function warnReadApi(obj) {
  return request({
    url: '/WarnRead/add',
    method: 'post',
    data: obj
  })
}


// 批量导出舆情预警
export function warnExportApi(obj) {
  return request({
    url: '/warn/data/export',
    method: 'post',
    data: obj
  })
}

// 批量导入至素材
export function warnMaterialAddApi(obj) {
  return request({
    url: '/warn/data/material/add',
    method: 'post',
    data: obj
  })
}
