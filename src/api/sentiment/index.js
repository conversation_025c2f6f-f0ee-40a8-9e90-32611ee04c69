import request from '@/utils/request'

// 查询列表
export function selectSpecialPage(data) {
  return request({
    url: '/search/selectSpecialPage',
    method: 'post',
    data
  })
}

// 导出
export function exportSpecialPage(data) {
  return request({
    url: '/search/exportSpecialPage',
    method: 'post',
    data
  })
}

// 处置详情
export function getProcessInfo(id) {
  return request({
    url: `/submit/getProcessInfo?id=${id}`,
    method: 'get'
  })
}
