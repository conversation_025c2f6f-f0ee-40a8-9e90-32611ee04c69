import request from '@/utils/request'

// 获取主题
export function getHabit(params) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: params
  })
}

// 设置主题
export function setHabit(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 总量
export function getTotal(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 情感分布
export function getEmontionStatis(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 律师动态
export function getLawyerMoment(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 折线图
export function getCurve(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 活跃账号
export function getAuthorLineChat(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 热词云
export function getWordsAnalyse(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 辖区总览
export function getAreaOverview(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 重点事件
export function getMainPlan(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 全国热点事件
export function getCountryHotPlan(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 热点事件
export function getHotPlan(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 过滤热词
export function DeleteHotWord(data) {
  return request({
    url: '/hot/cloud/add',
    method: 'post',
    data: data
  })
}

// 大屏-预警信息
export function homeWarn(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 大屏-重点账号/重点账号动态
export function keyAccounts(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}

// 大屏-浙江政法热点
export function hotZJ(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data: data
  })
}
