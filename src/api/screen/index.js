import request from '@/utils/request'

// 采集情况
export function getGatherCase() {
  return request({
    url: `/plateScreen/getGatherCase`,
    method: 'post'
  })
}
// 实时采集数据
export function getRealtimeInfo() {
  return request({
    url: `/plateScreen/getRealtimeInfo`,
    method: 'post'
  })
}
// 24小时趋势图
export function getTodayTrend() {
  return request({
    url: `/plateScreen/getTodayTrend`,
    method: 'post'
  })
}
// 数据来源分布
export function getDataDistribute() {
  return request({
    url: `/plateScreen/getDataDistribute`,
    method: 'post'
  })
}


// 方案
export function getPlans() {
  return request({
    url: `/planScreen/plan`,
    method: 'post'
  })
}

// 总数
export function getTotal(data) {
  return request({
    url: `/planScreen/total`,
    method: 'post',
    data
  })
}

// 情感
export function getEmotion(data) {
  return request({
    url: `/planScreen/emotionAnalyse`,
    method: 'post',
    data
  })
}

// 地图
export function getMap(data) {
  return request({
    url: `/planScreen/map`,
    method: 'post',
    data
  })
}

// 词云
export function getWords(data) {
  return request({
    url: `/planScreen/wordsAnalyse`,
    method: 'post',
    data
  })
}

// 实时舆情
export function getRealTime(data) {
  return request({
    url: `/planScreen/realTime`,
    method: 'post',
    data
  })
}

// 热点信息
export function getHotInfo(data) {
  return request({
    url: `/planScreen/hotInfo`,
    method: 'post',
    data
  })
}

// 一周趋势图
export function getWeekType(data) {
  return request({
    url: `/planScreen/weekType`,
    method: 'post',
    data
  })
}