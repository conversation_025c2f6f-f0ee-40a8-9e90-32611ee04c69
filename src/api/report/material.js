import request from '@/utils/request'

// 素材库
export function getFolderList(params) {
  return request({
    url: `/material/folder/list`,
    method: 'get',
    params
  })
}

// 列表
export function getPageList(data) {
  return request({
    url: `/material/list`,
    method: 'post',
    data
  })
}

// 新增素材库
export function saveFolder(data) {
  return request({
    url: `/material/folder/save`,
    method: 'post',
    data
  })
}

// 删除素材库
export function deleteFolder(id) {
  return request({
    url: `/material/folder/delete/` + id,
    method: 'delete',
  })
}

// 添加至素材
export function addFolder(data) {
  return request({
    url: `/material/add`,
    method: 'post',
    data
  })
}

// 批量添加至素材
export function addBatchFolder(data) {
  return request({
    url: `/material/addBatch`,
    method: 'post',
    data
  })
}

// 删除素材
export function deleteMaterialList(ids) {
  return request({
    url: `/material/delete/` + ids,
    method: 'delete',
  })
}

// 批量增加素材
export function addBatch(data) {
  return request({
    url: `/material/addBatch`,
    method: 'post',
    data
  })
}
