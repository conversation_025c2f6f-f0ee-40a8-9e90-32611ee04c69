import request from '@/utils/request'

// 获取信息详情
export function resultDetail(query) {
  return request({
    url: '/search/result/detail',
    method: 'get',
    headers: {
      isToken: false
    }, // 验证码请求headers不设置token
    params: query
  })
}

// 获取账号信息
export function getAccountInfo(query) {
  return request({
    url: '/account/getAccountInfo',
    method: 'get',
    headers: {isToken: false},
    params: query
  })
}

// 获取传播路径
export function resultArticle(data) {
  return request({
    url: '/search/result/author',
    method: 'post',
    headers: {isToken: false},
    data
  })
}

// 相关热搜
export function similarHot(query) {
  return request({
    url: '/search/result/similarHot',
    method: 'get',
    headers: {isToken: false},
    params: query
  })
}

// 短信处置
export function submitProcess(data) {
  return request({
    url: '/submit/process',
    method: 'post',
    headers: {isToken: false},
    data
  })
}
