import request from '@/utils/request'

// 登录方法
export function login(username, password, code, uuid) {
  const data = {
    username,
    password,
    code,
    uuid
  }
  return request({
    url: '/login',
    method: 'post',
    data: data
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    method: 'get',
    headers: {
      isToken: false
    } // 验证码请求headers不设置token
  })
}

// 获取登录用户的图片
export function getSystemLogoApi(query) {
  return request({
    url: '/customization/' + query,
    method: 'get',
    headers: {isToken: false},
  })
}

// 获取token
export function getLoginToken(query) {
  return request({
    url: '/adszLogin',
    method: 'get',
    params: query,
    headers: {
      isToken: false
    } // headers不设置token
  })
}