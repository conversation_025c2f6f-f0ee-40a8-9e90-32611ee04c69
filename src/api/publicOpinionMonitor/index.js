import request from '@/utils/request'

// 获取信源区域
export function sourceAreaApi(query) {
  return request({
    url: '/area/tree/' + query,
    method: 'get'
  })
}

// 新增方案分类
export function addTaskType(obj) {
  return request({
    url: '/planType/add',
    method: 'post',
    data: obj
  })
}

// 更新方案分类
export function putTaskType(obj) {
  return request({
    url: '/planType/update',
    method: 'post',
    data: obj
  })
}

// 删除方案分类
export function delTaskType(ids) {
  return request({
    url: '/planType/del',
    method: 'post',
    data: ids
  })
}

// 获取方案树
export function getTreeDataApi(data) {
  return request({
    url: `/plan/tree`,
    method: 'post',
    data: data
  })
}

// 查询方案分类
export function getTypeLists(data) {
  return request({
    url: '/planType/list',
    method: 'post',
    data: data
  })
}

// 新增方案
export function addPlanApi(data) {
  return request({
    url: '/plan/add',
    method: 'post',
    data: data
  })
}

// 修改/删除方案
export function updatePlanApi(data) {
  return request({
    url: '/plan/update',
    method: 'post',
    data: data
  })
}

// 根据id查询方案
export function planDetailApi(data) {
  return request({
    url: '/plan/one',
    method: 'post',
    data: data
  })
}

// 更改分类排序(上移,下移,置顶,置底)
export function changeTypeSort(data) {
  return request({
    url: '/planType/move',
    method: 'post',
    data: data
  })
}

// 更改任务排序(上移,下移,置顶,置底)
export function changeTaskSort(data) {
  return request({
    url: '/plan/move',
    method: 'post',
    data: data
  })
}

// 添加关注标签
export function updateMark(data) {
  return request({
    url: '/planMain/update',
    method: 'post',
    data: data
  })
}


// 定向信源查询
export function getSourceSetting(query) {
  return request({
    url: '/souce/getSourceSetting',
    method: 'get',
    params: query
  })
}

// 信源接口搜索
export function searchSource(data) {
  return request({
    url: '/souce/searchSource',
    method: 'post',
    data: data
  })
}

//新增定向信源-支持多个
export function addSourceSetting(data) {
  return request({
    url: '/souce/addSourceSetting',
    method: 'post',
    data: data
  })
}

// 下载信源模板
export function importTemplateApi(query) {
  return request({
    url: '/souce/importTemplate',
    method: 'get',
    params: query
  })
}

// 导入信源
export function importDataApi(data) {
  return request({
    url: '/souce/importData',
    method: 'post',
    data: data
  })
}

// 导出信源
export function exportSouceApi(query) {
  return request({
    url: '/souce/export',
    method: 'get',
    params: query
  })
}

// 删除信源-支持多个
export function deleteSouceApi(query) {
  return request({
    url: '/souce/delete',
    method: 'delete',
    params: query
  })
}


// 覆盖引用信源
export function overwriteSettingApi(data) {
  return request({
    url: '/souce/overwriteSetting',
    method: 'post',
    data: data
  })
}


// 叠加引用信源
export function addSettingApi(data) {
  return request({
    url: '/souce/addSetting',
    method: 'post',
    data: data
  })
}

// 搜索关键词库
export function selectWordLibTreeApi(data) {
  return request({
    url: `/word/selectWordLibTree`,
    method: 'post',
    data
  })
}

// 搜索地域关键词库
export function areaWordLibTreeApi(data) {
  return request({
    url: `/word/areaTree`,
    method: 'post',
    data
  })
}

// 获取习惯
export function getUserHabits() {
  return request({
    url: `/plan/userHabits`,
    method: 'get'
  })
}

// 保存习惯
export function saveUserHabits(data) {
  return request({
    url: `/plan/userHabits`,
    method: 'post',
    data
  })
}

// 移动方案的文件夹
export function movePlanApi(data) {
  return request({
    url: `/plan/movePlan`,
    method: 'post',
    data
  })
}

// 方案拖拽排序
export function dragPlanApi(data) {
  return request({
    url: `/plan/updatePlanInfo`,
    method: 'post',
    data
  })
}

// 历史方案事件追踪
export function trackingApi(query) {
  return request({
    url: `/plan/tracking/${query}`,
    method: 'get'
  })
}

// 修改预警
export function warnChangeApi(data) {
  return request({
    url: `/warn/set/change`,
    method: 'post',
    data
  })
}

// 获取预警详情
export function warnGetApi(data) {
  return request({
    url: `/warn/set/get`,
    method: 'post',
    data
  })
}

// 邮箱验证
export function mailTestApi(data) {
  return request({
    url: `/mail/test`,
    method: 'post',
    data
  })
}

// 获取事件首发
export function getFirstReleaseApi(data) {
  return request({
    url: `/eventContext/firstRelease`,
    method: 'post',
    data
  })
}

// 获取事件热文
export function getHotArticle(data) {
  return request({
    url: `/eventContext/relatedHotArticle`,
    method: 'post',
    data
  })
}

// 获取事件脉络
export function getEventContextApi(data) {
  return request({
    url: `/eventContext`,
    method: 'post',
    data
  })
}
// 保存统计分析pc端二维码条件-json格式
export function saveAnalyseConditionApi(data) {
  return request({
      url: `/analyse/saveAnalyseCondition`,
      method: 'post',
      data
  })
}

