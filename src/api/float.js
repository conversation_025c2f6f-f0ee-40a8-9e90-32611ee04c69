import request from '@/utils/request'

// 列表

export function getNoticeLists(params) {
  return request({
    url: '/module/notice/list',
    method: 'get',
    params
  })
}

// 是否启用通知

export function getUserSetting() {
  return request({
    url: '/home/<USER>',
    method: 'get',
  })
}

// 设置通知
export function setUserSetting(data) {
  return request({
    url: '/home/<USER>',
    method: 'post',
    data
  })
}

// 是否最新通知
export function getUserNotice() {
  return request({
    url: '/module/notice/latest',
    method: 'get',
  })
}
