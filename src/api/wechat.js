import request from '@/utils/request'

// 微信绑定
export function bindWarningApi(data) {
  return request({
    url: '/system/wechatQRCode/bindWarning',
    method: 'post',
    data: data
  })
}

// 获取微信二维码的链接
export function getMaPicUrlApi(data) {
  return request({
    url: '/wechat/getWarnCode',
    method: 'post',
    data: data
  })
}

// 扫码状态新接口
export function checkScanStatus(data) {
  return request({
    url: '/wechat/checkScanStatus',
    method: 'post',
    data: data
  })
}
