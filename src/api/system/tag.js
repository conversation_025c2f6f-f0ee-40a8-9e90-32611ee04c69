import request from '@/utils/request'

// 查询标签列表
export function listTag(data) {
  return request({
    url: '/tag/list',
    method: 'post',
    data: data
  })
}

// 新增或更新标签
export function saveOrUpdateTag(data) {
  return request({
    url: '/tag/saveOrUpdate',
    method: 'post',
    data: data
  })
}

// 获取标签详情
export function getTagByIds(ids) {
  return request({
    url: '/tag/getByIds',
    method: 'post',
    data: Array.isArray(ids) ? ids : [ids]
  })
}

// 删除标签
export function deleteTag(ids) {
  return request({
    url: '/tag/delete',
    method: 'post',
    data: Array.isArray(ids) ? ids : [ids]
  })
}