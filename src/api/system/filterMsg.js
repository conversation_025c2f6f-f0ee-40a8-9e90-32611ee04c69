import request from '@/utils/request'

// 查询列表
export function getFilterInfoPage(query) {
  return request({
    url: '/info/selectFilterInfoPage',
    method: 'get',
    params: query
  })
}

// 批量取消过滤
export function mulFilterInfo(data) {
  return request({
    url: '/info/deleteFilterInfo',
    method: 'post',
    data
  })
}

// 所属方案
export function getPlanType(data) {
  return request({
    url: '/plan/manage/list',
    method: 'post',
    data
  })
}

