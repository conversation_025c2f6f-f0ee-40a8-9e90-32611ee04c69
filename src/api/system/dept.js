import request from '@/utils/request'

// 查询部门列表
export function listDept(query) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params: query
  })
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/dept/list/exclude/' + deptId,
    method: 'get'
  })
}

// 查询服务公司下拉树结构
export function treeRela() {
  return request({
    url: '/submit/loadManagerDept',
    method: 'get'
  })
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'get'
  })
}

// 查询部门下拉树结构
export function treeselect() {
  return request({
    url: '/system/dept/treeselect',
    method: 'get'
  })
}

// 根据角色ID查询部门树结构
export function roleDeptTreeselect(roleId) {
  return request({
    url: '/system/dept/roleDeptTreeselect/' + roleId,
    method: 'get'
  })
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/system/dept',
    method: 'post',
    data: data
  })
}

// 修改部门
export function updateDept(data) {
  return request({
    url: '/system/dept',
    method: 'put',
    data: data
  })
}

// 删除部门
export function delDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'delete'
  })
}

// 更新定制
export function updateCustom(data) {
  return request({
    url: '/customization/update',
    method: 'post',
    data
  })
}

// 获取定制信息
export function parentDeptInfo(query) {
  return request({
    url: '/customization/deptInfo',
    method: 'get',
    params: query
  })
}

// 一键引用
export function copyPlan(query) {
  return request({
    url: '/plan/manage/copyPlan',
    method: 'get',
    params: query
  })
}

// 校验系统id是否重复
export function checkSystemId(query) {
  return request({
    url: '/customization/checkSystemId',
    method: 'get',
    params: query
  })
}

// 更新部门短信模板
export function saveDeptMessagetApi(data) {
  return request({
    url: '/message/saveDeptMessage',
    method: 'post',
    data
  })
}
