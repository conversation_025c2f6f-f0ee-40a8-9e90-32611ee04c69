{"name": "proj-init-f", "version": "1.0.0", "description": "博约舆情", "author": "BY", "scripts": {"dev": "vue-cli-service serve", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "keywords": ["vue", "admin", "dashboard", "element-ui", "boilerplate", "admin-template", "management-system"], "repository": {"type": "git", "url": ""}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "address-parse": "^1.2.19", "axios": "0.24.0", "clipboard": "2.0.8", "core-js": "3.19.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.11", "dom-to-image": "^2.6.0", "echarts": "^5.0.2", "echarts-wordcloud": "^2.1.0", "element-ui": "2.15.5", "file-saver": "2.0.4", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "html2canvas": "^1.4.1", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.0.0-rc.1", "md5": "^2.3.0", "moment": "^2.30.1", "nprogress": "0.2.0", "qrcode.vue": "^1.7.0", "quill": "1.3.7", "screenfull": "5.0.2", "sortablejs": "1.10.2", "vue": "2.6.12", "vue-clipboard2": "^0.3.3", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-danmaku": "^1.7.4", "vue-infinite-loading": "^2.4.5", "vue-router": "3.4.9", "vue-seamless-scroll": "^1.1.23", "vuedraggable": "2.24.3", "vuex": "3.6.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.6", "@vue/cli-plugin-eslint": "4.4.6", "@vue/cli-service": "4.4.6", "babel-eslint": "10.1.0", "chalk": "4.1.0", "connect": "3.6.6", "eslint": "7.15.0", "eslint-plugin-vue": "7.2.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass": "1.39.0", "sass-loader": "10.1.1", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}